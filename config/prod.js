const TerserPlugin = require('terser-webpack-plugin');

module.exports = {
  env: {
    NODE_ENV: '"production"',
  },
  defineConstants: {
  },
  // 小程序端专用配置
  weapp: {
    module: {
      postcss: {
        autoprefixer: {
          enable: true,
        },
        // 小程序端样式引用本地资源内联配置
        url: {
          enable: true,
          config: {
            limit: 10240, // 文件大小限制
          },
        },
      },
    },
  },
  copy: {
    patterns: [
      // { from: 'src/bootstrap.js', to: 'dist/js/bootstrap4.js' },
    ],
    options: {
    },
  },
  mini: {
    optimizeMainPackage: {
      enable: true,
    },
    webpackChain(chain) {
      chain.merge({
        plugin: {
          install: {
            plugin: require('terser-webpack-plugin'),
            args: [
              {
                test: ['common.js', 'vendors.js', 'taro.js', 'app.js'],
                cache: true,
                extractComments: false,
                parallel: true,
                sourceMap: true,
                terserOptions: {
                  compress: true, // 默认使用terser压缩
                  keep_classnames: true, // 不改变class名称
                  keep_fnames: true, // 不改变函数名称
                },
              },
            ],
          },
        },
      });
      chain.plugin('analyzer')
        .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin, []);
    },
  },
  h5: {
    // webpackChain(chain) {
    //   if (!process.env.CI) {
    //     chain.plugin('analyzer')
    //       .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin, []);
    //   }
    // },
  },
};
