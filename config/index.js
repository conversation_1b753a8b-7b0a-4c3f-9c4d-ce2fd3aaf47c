const path = require('path');
const {
  minify: terserMinify,
} = require('terser');

const config = {
  compiler: 'webpack4',
  terser: {
    enable: true,
    config: {
      cache: false,
      terserOptions: {
        compress: {
          drop_debugger: true,
          drop_console: false,
        },
        // ecma: undefined,
        // warnings: false,
        // parse: {},
        // compress: {},
        // mangle: {
        //   safari10: true,
        // },
        // module: false,
        // output: null,
        // toplevel: false,
        // nameCache: null,
        // ie8: false,
        // keep_classnames: undefined,
        // keep_fnames: false,
        // safari10: true,
      },
      minify: terserMinify,
    },
  },
  projectName: 'yqcloud-mobile-service',
  date: '2021-5-11',
  designWidth: 750,
  deviceRatio: {
    640: 2.34 / 2,
    750: 1,
    828: 1.81 / 2,
  },
  sourceRoot: 'src',
  outputRoot: process.env.TARO_ENV === 'weapp' ? 'mini_dist' : 'dist',
  plugins: [
    '@tarojs/plugin-html',
  ],
  defineConstants: {
  },
  copy: {
    patterns: [
      {
        from: 'src/static/root',
        to: 'dist',
      },
      {
        from: 'src/static',
        to: 'dist/static',
      },
      {
        from: 'src/components/open-captcha/aj-captcha',
        to: 'mini_dist/aj-captcha',
      },
    ],
    options: {
    },
  },
  framework: 'react',
  mini: {
    alias: {
      lodash: path.resolve(process.cwd(), 'node_modules', 'lodash'),
      'lodash.isequal': path.resolve(process.cwd(), 'node_modules', 'lodash/isEqual'),
      'bn.js': path.resolve(process.cwd(), 'node_modules', 'bn.js'),
      quill: path.resolve(process.cwd(), 'node_modules', 'quill/dist/quill.js'),
      'readable-stream': path.resolve(process.cwd(), 'node_modules', 'readable-stream'),
      'taro-ui$': 'taro-ui/lib/index',
      '@/global-component': path.resolve(__dirname, '..', 'src/global-component'),
      '@/components': path.resolve(__dirname, '..', 'src/components'),
      '@/pages': path.resolve(__dirname, '..', 'src/pages'),
      '@/store': path.resolve(__dirname, '..', 'src/store'),
      '@/style': path.resolve(__dirname, '..', 'src/style'),
      '@/util': path.resolve(__dirname, '..', 'src/util'),
      '@/hooks': path.resolve(__dirname, '..', 'src/hooks'),
      '@/types': path.resolve(__dirname, '..', 'src/types'),
      '@config': path.resolve(__dirname, '..', './config'),
    },
    postcss: {
      pxtransform: {
        enable: true,
        config: {
        },
      },
      url: {
        enable: true,
        config: {
          limit: 10240, // 设定转换尺寸上限
        },
      },
      cssModules: {
        enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:5]',
        },
      },
    },
  },
  h5: {
    devServer: {
      hot: false,
    },
    router: {
      mode: 'browser',
    },
    output: {
      filename: 'js/[name].[hash:8].js',
      chunkFilename: 'js/[name].[chunkhash:8].js',
    },
    publicPath: '/',
    staticDirectory: 'static',
    esnextModules: ['taro-ui', '@taroify/core', '@taroify'],
    alias: {
      '@/global-component': path.resolve(__dirname, '..', 'src/global-component'),
      '@/components': path.resolve(__dirname, '..', 'src/components'),
      '@/hooks': path.resolve(__dirname, '..', 'src/hooks'),
      '@/pages': path.resolve(__dirname, '..', 'src/pages'),
      '@/store': path.resolve(__dirname, '..', 'src/store'),
      '@/style': path.resolve(__dirname, '..', 'src/style'),
      '@/util': path.resolve(__dirname, '..', 'src/util'),
      '@/types': path.resolve(__dirname, '..', 'src/types'),
      '@config': path.resolve(__dirname, '..', './config'),
    },
    postcss: {
      cssModules: {
        enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module，下文详细说明
          generateScopedName: '[name]__[local]___[hash:base64:5]',
        },
      },
      autoprefixer: {
        enable: true,
      },
      // 小程序端样式引用本地资源内联配置
      url: {
        enable: true,
        config: {
          limit: 10240, // 文件大小限制
        },
      },
    },
    webpackChain(chain) {
      chain.merge({
        // optimization: {
        //   splitChunks: {
        //     name: true,
        //     cacheGroups: {
        //       default: {
        //         name: 'common',
        //         chunks: 'initial',
        //       },
        //     },
        //   },
        // },
        module: {
          rule:
            { es5Loader:
                { test: /(.*node_modules\/swr.*$)|(.*node_modules\/@stencil\/core.*$)|(.*node_modules\/tiny-case.*$)|(.*\/.*\.ts$)/,
                  use: [
                    { loader: 'babel-loader',
                      options: {
                        presets: [
                          [
                            '@babel/preset-env',
                            { useBuiltIns: 'usage', targets: '> 0.25%, not dead, chrome 49', corejs: '3.26.1' },
                          ],
                        ],
                      },
                    },
                  ],
                },
            },
        },
      });
    },
    miniCssExtractPluginOption: {
      filename: 'css/[name].[hash:8].css',
      chunkFilename: 'css/[id].[hash:8].css',
    },
  },
};

module.exports = function (merge) {
  if (process.env.TARO_ENV === 'weapp') {
    config.terser = undefined;
  }
  if (process.env.NODE_ENV === 'development') {
    return merge({}, config, require('./dev'));
  }
  return merge({}, config, require('./prod'));
};
