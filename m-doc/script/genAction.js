const fs = require('fs-extra');
const path = require('path');

/* eslint-disable no-restricted-syntax */

function fileDisplay(filePath) {
  const externalFiles = [];
  // 根据文件路径读取文件，返回文件列表
  const files = fs.readdirSync(filePath);
  for (const filename of files) {
    const filedir = path.join(filePath, filename);
    // 根据文件路径获取文件信息，返回一个fs.Stats对象
    const stats = fs.statSync(filedir);
    const isFile = stats.isFile(); // 是文件
    if (isFile) {
      const fileContent = fs.readFileSync(filedir).toString('utf-8');
      const externalizes = fileContent.matchAll(/DATAFLUX_RUM\.addAction\('([^']+)'/g);
      for (const externalize of externalizes) {
        if (externalize) {
          externalFiles.push(externalize[1]);
        }
      }
    }
    const isDir = stats.isDirectory(); // 是文件夹
    if (isDir) {
      const dirExternalFiles = fileDisplay(filedir);
      externalFiles.push(...dirExternalFiles);
    }
  }
  return externalFiles;
}

function getExternalizeExposes() {
  return fileDisplay(path.resolve(process.cwd(), '../src'));
}
// getExternalizeExposes();
// 获得所有埋点数据
fs.appendFileSync(path.join(__dirname, '../docs/监控/埋点列表.md'), getExternalizeExposes().map(v => `- ${v}`).join('\n'))
