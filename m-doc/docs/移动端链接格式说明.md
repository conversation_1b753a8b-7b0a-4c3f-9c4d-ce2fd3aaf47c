# 移动端链接说明

移动端链接遵循几个原则
- 上线正式环境后任何链接规则都不能有删改（可以添加新参数，但必须保证原有旧参数效果不变，所有路由严格不变）
- 登陆参数开发不得用于其他用途，目前登陆

一个移动端的链接大致如下：

https://m.yqcloud.com/pages/create-order/index?tenant=xtep&itemId=xxxxxxxxxxxxxxxxxxxxxxxx

这个链接分为三部分，前面第一部分是域名，通常代表了一个燕千云环境，目前移动端可用的环境有

- 生产环境：`https://m.yqcloud.com`
- 交付环境：`https://m.yqcloud.com`
- 预生产环境：`https://m.yqcloud.com`
- 测试环境：`https://m.yqcloud.com`
- 开发环境：`https://m.yqcloud.com`

中间是路径，目前可用的路径有

| 路径                                           | 说明                       |
| ---------------------------------------------- | -------------------------- |
| 'pages/home/<USER>',                            | 首页                       |
| 'pages/notice/index',                          | 消息中心页                 |
| 'pages/new-doc-viewer/index',                  | 通知详情页（公告和消息）   |
| 'pages/me/index',                              | 个人中心页                 |
| 'pages/app-login/index',                       | 小程序和公众号登陆页       |
| 'pages/survey/index',                          | 调查列表                   |
| 'pages/task/index',                            | 我的任务                   |
| 'pages/ocr/index',                             | ocr中间页                  |
| 'pages/request/index',                         | 我的请求                   |
| 'pages/solution-detail/index',                 | 解决方案详情页             |
| 'pages/task-detail/index',                     | 低代码页面，可渲染任何页面 |
| 'pages/tenant-list/index',                     | 切换租户页                 |
| 'pages/language-list/index',                   | 切换语言页                 |
| 'pages/event-detail/index',                    | 服务单详情页               |
| 'pages/file-preview/index',                    | 文件预览页面               |
| 'pages/botpress/index',                        | 机器人页                   |
| 'pages/service-center/index',                  | 服务中心页（旧版）         |
| 'pages/knowledge-center/index',                | 知识中心页                 |
| 'pages/knowledge-operations/index',            |                            |
| 'pages/knowledge-space/index',                 |                            |
| 'pages/knowledge-collection/index',            |                            |
| 'pages/knowledge-detail/index',                |                            |
| 'pages/create-order/index',                    |                            |
| 'pages/service-item/index',                    |                            |
| 'pages/service-catalogs/index',                |                            |
| 'pages/children-catalogs/index',               |                            |
| 'pages/view/index',                            |                            |
| 'pages/modules/index',                         |                            |
| 'pages/survey-detail/index',                   |                            |
| 'pages/vaccines-nucleic-acids/index',          |                            |
| 'pages/vaccines-result/index',                 |                            |
| 'pages/service-catalog/index', // 新版服务目录 |                            |
| 'pages/search/index', // 新版全局搜索          |                            |
| 'pages/solution-list/index',                   |                            |
| 'pages/qa-detail/index',                       |                            |
| 'pages/order-page/index',                      |                            |


## 参数列表

### 登陆参数

- appid 三方应用的id
- type 三方应用的类型
- isPlatformWechat 是否

### 通用参数
这类参数的特点是在所有页面上都可以加都可以使用，这类参数通常能对页面进行一定的影响和定制

### 页面参数

这类参数对基本所有页面都生效


