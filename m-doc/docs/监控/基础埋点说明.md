

## 一、埋点添加统一数据tag

### 所有的url参数都会作为全局参数

tenant - 租户域名 （用于统计xxx租户下xxx情况用）

userid - 访问用户燕千云里的id （用于统计真实uv）

username - 用户realname

language - 用户访问时燕千云语言

appid - 用户访问的三方appid

apptype - 用户访问的三方类型

## 二、埋点添加自定义动作和动作额外数据tag

```js
DATAFLUX_RUM.onReady(function() {
    DATAFLUX_RUM.addRumGlobalContext('<CONTEXT_KEY>', '<CONTEXT_VALUE>');
})

// Code example
DATAFLUX_RUM.onReady(function() {
    DATAFLUX_RUM.addRumGlobalContext('isvip', 'xxxx');
})
DATAFLUX_RUM.onReady(function() {
    DATAFLUX_RUM.addRumGlobalContext('activity', {
        hasPaid: true,
        amount: 23.42
    });
})
```

### 需求

1. 根据租户统计，智能推荐功能用户点开/收起次数
   a. 当服务项配置智能推荐为“默认展开”时，用户点击“收起智能推荐”的次数。
   b. 当服务项配置智能推荐为“默认收起”时，用户点击“查看智能推荐”的次数。
   c. 点击智能推荐下拉弹窗中的“查看更多”的次数
   d.点击智能推荐下拉弹窗中的“查看更多相似问题”次数
   e. 用户点击采纳后，点击“继续浏览”的次数
   f. 用户点击采纳后，点击“前往首页”的次数

2. 统计“验证码登录”功能使用情况

  1. 点击“如何获得企业域名”次数
  2. 进入账号密码登录页后，点击“返回验证码登录”页面
  3. 点击“验证码登录”次数
  4. 进入“验证码登录”后，点击“返回域名登录”页面的次数
3. 统计移动端各个接入渠道的使用情况
  1. 使用小程序、微信公众号、企微、钉钉、飞书、其他的租户
  2. 统计以上渠道下每个租户的PV、UV

### 动作和动作额外数据tag

用户点击“收起智能推荐” （对应需求1. ab）

```json5
{
	defaultExpand: true, // 服务项配置智能推荐是否默认展开
}
```

------

其他需求直接埋对应动作的点。且无额外数据tag
