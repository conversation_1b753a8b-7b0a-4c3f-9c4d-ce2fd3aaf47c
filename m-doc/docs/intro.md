---
sidebar_position: 1
---

# README
## 准备
- node > 12.0.0
- @tarojs/cli 见下文文档


## 开发文档

- [Taro UI库](https://taro-ui.aotu.io/#/docs/button)
- [Taro 安装及使用](https://fontawesome.dashgame.com/)
- [FontAwesome 图标库](https://fontawesome.dashgame.com/)

## 启动

```

npm install
npm start
```
启动后访问 http://localhost:10086?tenant=yqcloud

tenant 后面是租户域名

### 小程序启动

1. 安装微信开发者工具
2. 打开后选择本项目中的dist文件夹
   > 若没有则需要启动一下本项目 appid需要从微信后台的小程序开发管理里面获取
3. 最后启动命令
```bash
npm run dev:weapp
```


> 注意：小程序不需要IDE的编译，直接在IDE中终端启动这个命令，即可监听预览

## API

图片地址格式

```https://api.dev.yqcloud.co/hfle/yqc/v1/0/files/download-by-key?fileKey=0/<EMAIL>&access_token=bearer 255fccd3-7af6-4d3d-8d7d-6ff75352e5be```

## 包安装
npm i --registry http://nexus.saas.hand-china.com/repository/yqcloud-npm-group/

## 开发规范

__组件必须使用css module,参照`scr/components/yq-output`

__组件必须使用typescript,参照`scr/components/yq-output`

Taro开发遇到问题请自行阅读参考[官方文档](https://taro-docs.jd.com/)

Taro-ui没有或无法满足需求的组件(如List列表弹出层)可使用[taroify](https://taroify.gitee.io/taroify.com/introduce/)

仍无法满足需求二次封装请参考“组件规范”

不允许使用document等基于h5 dom的操作，可以使用useRef替代

少用css伪类选择器，伪类选择器无法在小程序使用

## 需注意的Taro函数使用

Taro.navigateTo 不会销毁当前页面，只会隐藏。
Taro.redirectTo 销毁当前页面并跳转

### 函数命名


请求接口的函数`fetch`开头

渲染jsx `render`开头

常量`const SOME_STATE = ['a', 'b']`




### 组件规范

所有组件使用函数式组件开发，拥抱hooks，

### 有内部状态的组件示例
`src/components/ui-action`
动作集需要请求接口，并且查看pc的代码可发现其依赖

viewCode,
formConfig,
formDataSet,

（为了方便移植代码，此处为pc端代码中的命名。）

因此需要这三个属性，其余动作集接口等由组件内部请求和处理。

### 无内部状态的组件示例
`src/components/action-operation`
传入状态的数据即可使用

### 数据管理

数据使用mobx管理，除全局需使用的store外，组件store直接使用useLocalObservable写在组件中

我们使用一个代码实例来解释怎样规范的使用mobx
```jsx
// bad 来自create-order.js但在使用时却叫做eventStore （import eventStore from '@/store/create-order'）
const Store = observable({ //bad 实例的名称不应该使用大写开头
    // bad 一个函数有两个功能
    setViewAndFormJson(data = {}) { //bad 多余的仅用来设置值的函数
        // bad 不应该使用this
        this.viewData = data // bad 无法知道data是什么，查找逻辑麻烦
        // bad 应使用或运算符不应使用三目
        this.formJson = JSON.parse(data.mobileJsonData ? data.mobileJsonData : data.jsonData) //bad 没有捕获JSON.parse的异常
    },
    // 表单视图
    // bad 函数名让人迷惑，一般人看到函数名不会觉得这会请求个接口
    async getFormJson(variableViewId) {
        const url = `lc/v1/${appStore.tenantId}/views/form/variable/${variableViewId}?deviceType=H5`;
        // bad 错误和异常界面上不可见
        const result = await fetch(url).catch(e => console.log('e == ', e)) //bad 重要信息请使用console.error
        // bad 函数名是getFormJson却要把获得的数据传给另一个函数并设置到viewData中
        this.setViewAndFormJson(result)
        return result //bad 无用的return
    },
})
```

```jsx
  // good 表单视图
const store = observable({
    async fetchFormVariable(variableViewId) {
        const url = `lc/v1/${appStore.tenantId}/views/form/variable/${variableViewId}?deviceType=H5`
        const result = await fetch(url).catch(e => console.error('e == ', e))
        store.formVariableData = result;
    },
    // good
    get formJSON() {
        try {
            JSON.parse(store.formVariableData.mobileJsonData || store.formVariableData.jsonData)
        } catch (e) {
            console.error(e)
        }
    },
})

```

禁止state和mobx store混用
```jsx
// bad
async fetchData() {
    const result1 = await eventDetailStore.getEventItem(this.state.itemId);
    const result2 = await eventDetailStore.getFormJson(result1.viewId)
    this.setState({
      formFieldValues: JSON.parse(result2.jsonData) || {},
    })
  }
// good
async function fetchData() {
    await eventDetailStore.fetchData(itemId, viewId);
}
```
