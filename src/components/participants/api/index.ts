import useSWR from 'swr';
import queryString from 'query-string';
import { fetch, _ } from '@/util';
import appStore from '@/store/app';
import platformLovStore from '@/store/platformLov';

/**
 * 请求参与者&&参与人
 */
function useParticipants(businessObjectCode: string, ticketId: string, searchValue: string = '') {
  const { tenantId } = appStore;
  const params = queryString.stringify(searchValue ? { param: searchValue } : {});
  const url = `itsm/v1/${tenantId}/${businessObjectCode}/${ticketId || 0}/participants${params ? `?${params}` : ''}`;
  const fetcher = (_url: string) => {
    return fetch(url).then(res => {
      const { user, group } = res || {};
      if (Array.isArray(user) && user.length > 0) {
        user.forEach(u => Object.assign(u, { primaryKey: u.id }));
      }
      if (Array.isArray(group) && group.length > 0) {
        group.forEach(g => Object.assign(g, { primaryKey: g.id }));
      }
      return res;
    });
  };

  return useSWR(url, fetcher, { revalidateOnFocus: false, revalidateIfStale: true });
}

/**
 * 删除参与人或参与组，多个
 */
async function deleteParticipants(ids: string | string[], businessObjectCode: string, ticketId: string) {
  const idList = Array.isArray(ids) ? ids : [ids];
  const url = `itsm/v1/${appStore.tenantId}/${businessObjectCode.toLocaleLowerCase()}/${ticketId}/participants`;
  await fetch(url, JSON.stringify(idList), 'delete');
}

/**
 * 添加参与人或参与组
 */
async function addParticipants(participants: any[], businessObjectCode: string, ticketId: string) {
  const url = `itsm/v1/${appStore.tenantId}/${businessObjectCode.toLocaleLowerCase()}/${ticketId}/participants`;
  await fetch(url, JSON.stringify(participants), 'post');
}

/**
 * 请求值列表的配置
 */
async function fetchLovConfig(ticketId: string, businessObjectCode: string, type: string, lovId?: string, intl?:any) {
  const config = { displayField: 'search_name', url: '', searchable: false, params: {}, method: 'get', restNameFieldCodes: [] };

  if (lovId) {
    // 若配置了选择值列表，则走object_options接口
    const url = `lc/v1/${appStore.tenantId}/object_options/id/${lovId}`;
    const res = await fetch(url).catch(() => {});
    if (res && !res.failed) {
      const jsonData = _.isJSON(res.jsonData) ? JSON.parse(res.jsonData) : res.jsonData;
      const { dataSource, layout } = jsonData || {};
      // const { nameFieldCode, searchType, searchable } = dataSource || {};
      const { nameFieldCode, searchable } = dataSource || {};
      const restNameFieldCodes: any[] = [];
      if (Array.isArray(layout)) {
        layout.forEach(l => {
          if (l.name && l.props?.name !== nameFieldCode && l.props?.displayFlag !== false) {
            restNameFieldCodes.push({ meaning: l.name, value: l.props?.name, intl: false });
          }
        });
      }
      const searchUrl = `lc/v1/engine/${appStore.tenantId}/options/${lovId}/queryWithCondition`;
      Object.assign(config, {
        searchable,
        displayField: nameFieldCode || 'real_name',
        valueField: 'id', // 先给个固定值id
        searchField: `search_${nameFieldCode}`,
        url: searchUrl,
        method: 'post',
        params: {
          condition: [{ condition: 'AND', filters: [{ condition: 'AND', filter: 'is not', widgetType: 'Lov', componentType: 'Lov', field: 'id', fieldValue: ticketId }] }],
          params: {
            search_businessObjectCode: businessObjectCode?.toLocaleUpperCase(),
            search_taskId: ticketId,
          },
        },
        restNameFieldCodes,
      });
    }
  } else {
    // 若没有配置选择值列表，则走默认的接口逻辑
    const code = type === 'user' ? 'AT_PERSON' : 'PARTICIPANT_GROUP';
    const res = await platformLovStore.getLovConfig(code);
    if (res && !res.failed) {
      const { displayField, requestUrl, valueField } = res; // multipleFlag 可多选
      let searchUrl = requestUrl?.startsWith?.('/') ? requestUrl.slice(1, requestUrl.length) : requestUrl;
      searchUrl = searchUrl.replace('{tenantId}', appStore.tenantId);
      Object.assign(config, {
        searchable: true,
        displayField,
        url: searchUrl,
        valueField, // 先给个固定值id
        searchField: `search_${displayField}`,
        params: type === 'user' ? { taskId: ticketId, businessObjectCode: businessObjectCode.toLocaleUpperCase() } : {},
        restNameFieldCodes: [{ meaning: intl ? intl?.formatMessage({ id: 'yqc.mobile.model.email', defaultMessage: '邮箱' }) : 'email', value: 'email', intl: true }],
      });
    }
  }

  return config;
}

interface ParticipantsPermission {
  addParticipantFlag?: boolean;
  deleteParticipantFlag?: boolean;
}
const useParticipantsPermission = (businessObjectId, ticketId) => {
  const url = `itsm/v1/${appStore.tenantId}/service_settings/apply?businessObjectId=${businessObjectId}&ticketId=${ticketId || 0}`;

  return useSWR<ParticipantsPermission>(url, fetch, { revalidateOnFocus: false, revalidateIfStale: true });
};

export {
  ParticipantsPermission,
  useParticipantsPermission,
  useParticipants,
  deleteParticipants,
  addParticipants,
  fetchLovConfig,
};
