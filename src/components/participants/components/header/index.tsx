import React, { useEffect, useMemo, useState } from 'react';
import classNames from 'classnames';
import { Search } from '@taroify/core';
import { View, Text, BaseEventOrig, InputProps } from '@tarojs/components';
import YqButton from '@/components/yq-button';
import YqIcon from '@/components/yq-icon';
import Modal from '@/components/yq-modal';
import Permission from '@/components/permission';
import MultipleSelect from '@/components/multiple-select';
import YqComponentTitleFrame from '@/components/yq-component-title-frame';
import { HeaderProps, ParticipantsType } from '../../Participants';
import { deleteParticipants, fetchLovConfig, addParticipants } from '../../api';
import styles from './Header.module.less';
import ParticipantsCell from '../cell';

const Header: React.FC<HeaderProps> = function Header(props) {
  const { intl, userTotal, groupTotal, title, fetchConfig, searchValue, setSearchValue, data, trigger, pData } = props;

  const { ticketId, userId, groupId, businessObjectCode } = fetchConfig;

  const [show, setShow] = useState(false);
  const [focus, setFocus] = useState(false);
  const [config, setConfig] = useState<any>({ user: {}, group: {} });

  useEffect(() => {
    fetchLovConfig(ticketId, businessObjectCode, 'user', userId, intl).then(res => {
      setConfig((pre) => ({ ...Object.assign(pre, { user: res }) }));
    });
    fetchLovConfig(ticketId, businessObjectCode, 'group', groupId, intl).then(res => {
      setConfig((pre) => ({ ...Object.assign(pre, { group: res }) }));
    });
  }, [userId, groupId, ticketId, businessObjectCode]);

  const curData = useMemo(() => {
    const { user = [], group = [] } = data;
    const { user: userConfig, group: groupConfig } = config;
    if (userConfig.valueField && userConfig.displayField && user.length > 0) {
      user.forEach(u => Object.assign(u, {
        [userConfig.valueField]: (u.participantUserId || u.id),
        [userConfig.displayField]: (u.participant),
      }));
    }
    if (groupConfig.valueField && groupConfig.displayField && group.length > 0) {
      group.forEach(g => Object.assign(g, {
        [groupConfig.valueField]: (g.participantGroupId || g.id),
        [groupConfig.displayField]: (g.participant),
      }));
    }
    return data;
  }, [data, config]);

  const handleCancel = () => {
    setShow(false);
    setSearchValue('');
  };

  const handleSearch = (e: BaseEventOrig<InputProps.inputValueEventDetail>) => {
    setSearchValue(e.detail.value); // @ts-ignore
    e.target?.children?.[0]?.blur?.();
  };

  const handleShowSearch = () => {
    setShow(true);
    setFocus(true);
  };

  const handleClear = () => {
    setSearchValue('');
    setFocus(true);
  };

  const handleConfirm = async (records, modal, type: ParticipantsType) => {
    const oldRecords = curData[type];
    const { valueField } = config[type];
    const reduceRecords: string[] = [];
    oldRecords.forEach((record) => {
      // 老数据里面有，新数据里面没有就是删除掉的数据
      if (!records.some(r => r[valueField] === record[valueField])) {
        reduceRecords.push(record.primaryKey);
      }
    });
    const postData = records.map(r => ({ participantId: r[valueField], type: type.toLocaleUpperCase() }));
    if (reduceRecords.length > 0) {
      await deleteParticipants(reduceRecords, businessObjectCode, ticketId);
    }
    await addParticipants(postData, businessObjectCode, ticketId);
    await trigger();
    modal?.close();
  };

  const handleOpenParticipants = (type: ParticipantsType) => {
    const _config = config[type];
    const records = curData[type];
    const _title = type === 'user' ? intl.formatMessage({ id: 'yqc.mobile.please.select.a.participant', defaultMessage: '请选择参与人' }) : intl.formatMessage({ id: 'yqc.mobile.please.select.a.group', defaultMessage: '请选择参与组' });
    const selectedTitle = type === 'user' ? intl.formatMessage({ id: 'yqc.mobile.view.selected.participants', defaultMessage: '查看已选参与人' }) : intl.formatMessage({ id: 'yqc.mobile.view.selected.participating.groups', defaultMessage: '查看已选参与组' });

    Modal.open({
      popupProps: {
        style: { height: '80vh' },
      },
      children: (
        <MultipleSelect
          title={_title}
          selectedTitle={selectedTitle}
          url={_config.url}
          params={_config.params}
          method={_config.method}
          searchPrefix={_config.searchField}
          displayField={_config.displayField}
          valueField={_config.valueField}
          search={_config.searchable}
          records={records}
          onConfirm={(_records, modal) => handleConfirm(_records, modal, type)}
          renderCell={(record: any, displayField: string) => (
            <ParticipantsCell
              id={record.id}
              type={type}
              imageUrl={record?.imageUrl}
              name={record?.[displayField]}
              restField={_config.restNameFieldCodes}
              record={record}
            />
          )}
        />
      ),
    });
  };

  const renderTitle = () => {
    const button = () => (
      <>
        <View className={styles.status}>
          <Text>{intl.formatMessage({ id: 'yqc.mobile.current.participant', defaultMessage: '当前参与人' })}</Text>
          <Text style={{ fontWeight: 'bold' }}>{`: ${userTotal || 0}`}</Text>
        </View>
        <View className={styles.status}>
          <Text>{intl.formatMessage({ id: 'yqc.mobile.current.participating.group', defaultMessage: '当前参与组' })}</Text>
          <Text style={{ fontWeight: 'bold' }}>{`: ${groupTotal || 0}`}</Text>
        </View>
      </>
    );
    return (
      <YqComponentTitleFrame
        alreadyInFrame
        header={title}
        rightButtonRenderer={button}
        style={{ marginBottom: '20px' }}
      />
    );
  };
  useEffect(() => {
    if (!pData?.addParticipantFlag && pData) {
      setShow(true);
    }
  }, [pData?.addParticipantFlag, pData]);
  return (
    <View className={styles.header}>
      {renderTitle()}
      <View className={styles.headerBottom}>
        <View className={classNames(styles.buttonGroup, { [styles.hidden]: show })}>
          {pData?.addParticipantFlag && <Permission onDeny={() => setShow(true)} service={['edit_participant']}>
            <YqButton
              onClick={() => handleOpenParticipants('user')}
              className={styles.button}
              fontColor="#2979ff"
              iconProps={{ type: 'PeoplePlusOne', fill: '#2979ff', size: 36 }}
            >
              {intl.formatMessage({ id: 'yqc.mobile.add.participants', defaultMessage: '添加参与人' })}
            </YqButton>
            <YqButton
              onClick={() => handleOpenParticipants('group')}
              className={styles.button}
              fontColor="#2979ff"
              iconProps={{ type: 'EveryUser', fill: '#2979ff', size: 36 }}
            >
              {intl.formatMessage({ id: 'yqc.mobile.add.participating.group', defaultMessage: '添加参与组' })}
            </YqButton>
          </Permission>}
          <View className={styles.searchIcon} onClick={handleShowSearch}>
            <YqIcon type="icon-search" fill="#12274d" opacity={0.85} size={40} />
          </View>
        </View>
        <Search
          focus={focus}
          value={searchValue}
          className={classNames(styles.search, { [styles.show]: show })}
          icon={<YqIcon type="icon-search" size={36} fill="#12274d" opacity={0.45} />}
          action={<Text onClick={handleCancel}>{intl.formatMessage({ id: 'yqc.mobile.cancel', defaultMessage: '取消' })}</Text>}
          onSearch={handleSearch}
          onChange={(e) => {
            setSearchValue(e.detail.value);
          }}
          onFocus={() => setFocus(true)}
          onBlur={() => setFocus(false)}
          onClear={handleClear}
        />
      </View>
    </View>
  );
};

export default Header;
