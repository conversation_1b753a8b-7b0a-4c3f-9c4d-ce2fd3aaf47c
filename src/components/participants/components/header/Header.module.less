.header {
  flex-shrink: 0;
  padding: 24px 32px;
  position: relative;
  overflow: hidden;
  background-color: #fff;

  .headerTop {
    display: flex;
    align-items: center;
  }

  .headerBottom {
    margin-top: 20px;
  }

  .buttonGroup {
    width: 100%;
    display: flex;
    align-items: center;
    overflow: hidden;
    transition-duration: 300ms;
    transition-timing-function: linear;

    &.hidden {
      width: 0;
    }
  }

  .title {
    width: 0;
    flex-grow: 1;
    font-size: 32px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #12274d;
    word-break: keep-all;
  }

  .status {
    height: 45px;
    padding: 0 16px;
    font-size: 24px;
    line-height: 45px;
    flex-shrink: 0;
    background-color: #f0f8ff;
    border-radius: 8px;
    color: #2979ff;
    margin-left: 16px;
  }

  .button {
    width: 244px;
    height: 64px !important;
    margin-right: 24px;
    background-color: #fff !important;
    border-radius: 12px;
    font-size: 28px;
    border: 2px solid #2979ff !important;
    flex: none !important;
    flex-shrink: 0;
  }

  .searchIcon {
    width: 0;
    height: 64px;
    flex-grow: 1;
    text-align: right;
    line-height: 64px;
  }

  .search {
    width: 0;
    height: 68px;
    overflow: hidden;
    position: absolute;
    right: 32px;
    bottom: 24px;
    --search-padding: 0;
    --search-action-font-size: 28px;
    --search-action-padding: 0 12px;
    transition-duration: 300ms;
    transition-timing-function: linear;

    :global {
      .taroify-search__action {
        word-break: keep-all;
        white-space: nowrap;
      }
    }

    &.show {
      width: calc(100% - 64px);
    }
  }
}
