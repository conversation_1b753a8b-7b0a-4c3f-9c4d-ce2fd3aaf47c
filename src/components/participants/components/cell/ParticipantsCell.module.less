.row {
  min-height: 103px;
  display: flex;
  align-items: center;

  .userAvatar {
    margin-right: 20px;
    flex-shrink: 0;
    --avatar-size-medium: 60px;
  }

  .groupAvatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(180deg, #6797ff 0%, #1f5df3 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
  }

  .brief {
    width: 0;
    min-height: 103px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 18px 0;
    border-bottom: 1px solid rgba(203, 210, 220, 0.5);
    font-family: PingFangSC-Regular, PingFang SC;
    line-height: 40px;

    .briefName {
      flex-shrink: 0;
      word-break: keep-all;
      font-size: 28px;
      color: #12274d;

      &.onlyname {
        width: 100%;
      }
    }

    .briefValue {
      word-break: keep-all;
      font-size: 24px;
      margin-top: 4px;
      color: rgba(18, 39, 77, 0.65);
    }
  }

}