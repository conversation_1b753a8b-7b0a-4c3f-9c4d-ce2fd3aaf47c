import { FC } from 'react';
import classNames from 'classnames';
import { Text, View } from '@tarojs/components';
import { useIntl } from '@/util/intl';
import YqAvatar from '@/components/yq-avatar';
import YqIcon from '@/components/yq-icon';
import { _ } from '@/util';
import { ParticipantsCellProps } from '../../Participants';
import styles from './ParticipantsCell.module.less';

const ParticipantsCell: FC<ParticipantsCellProps> = function ParticipantsCell(props) {
  const { type, imageUrl, name = '', id, ownerName = '', restField = [], record = {} } = props;

  const intl = useIntl();

  const handleGroupDetail = () => {
    const _name = encodeURIComponent(name);
    const _ownerName = encodeURIComponent(ownerName);
    _.navigateTo({ url: `/packageOther/pages/group-page/index?id=${id}&name=${_name}&ownerName=${_ownerName}` });
  };

  return (
    <View className={styles.row}>
      {type === 'user' ? (
        <YqAvatar personId={id} classNames={styles.userAvatar} fileKey={imageUrl}>{name}</YqAvatar>
      ) : (
        <View className={styles.groupAvatar} onClick={handleGroupDetail}>
          <YqIcon type="EveryUser" size={36} fill="#fff" />
        </View>
      )}
      <View className={styles.brief}>
        <Text className={classNames(styles.briefName, 'taroify-ellipsis', { [styles.onlyname]: !restField.length })}>
          {name}
        </Text>
        {restField.length > 0 && restField.map((field) => record[field.value] && (
          <Text className={classNames(styles.briefValue, 'taroify-ellipsis')} key={field.value}>
            {`${field.meaning}: ${record[field.value]}`}
          </Text>
        ))}
      </View>
    </View>
  );
};

export default ParticipantsCell;
