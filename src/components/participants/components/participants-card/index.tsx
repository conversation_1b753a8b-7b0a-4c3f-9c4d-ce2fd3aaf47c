import React, { useCallback, useEffect, useState } from 'react';
import { View } from '@tarojs/components';
import { SwipeCell, Loading } from '@taroify/core';
import YqIcon from '@/components/yq-icon';
import Permission from '@/components/permission';
import { deleteParticipants } from '../../api';
import ParticipantsCell from '../cell';
import { PCardProps } from '../../Participants';
import styles from './ParticipantsCard.module.less';

const ParticipantsCard: React.FC<PCardProps> = function ParticipantsCard(props) {
  const { intl, record, type, trigger, openId, setOpenId, searchValue, businessObjectCode, ticketId, pData } = props;

  const [expand, setExpand] = useState<boolean>(() => (Array.isArray(record) && record?.length <= 5) || !!searchValue);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    if (Array.isArray(record) && record?.length <= 5) {
      setExpand(true);
    }
  }, [record]);

  const handleDelete = useCallback(async (id: string) => {
    setLoading(true);
    try {
      await deleteParticipants(id, businessObjectCode, ticketId);
      await trigger();
    } finally {
      setLoading(false);
    }
  }, [trigger, businessObjectCode, ticketId]);

  const renderRecordItem = useCallback((r) => {
    const { id, participant, imageUrl, primaryKey, ownerName } = r;

    const open = id === openId ? 'right' : 'outside';

    return (
      <SwipeCell key={id} stopPropagation onOpen={() => setOpenId(id)} open={open}>
        <ParticipantsCell
          id={id}
          type={type}
          imageUrl={imageUrl}
          name={participant}
          ownerName={ownerName}
          restField={[{ intl: true, value: 'email', meaning: intl.formatMessage({ id: 'yqc.mobile.model.email', defaultMessage: '邮箱' }) }]}
          record={r}
        />
        <SwipeCell.Actions side="right">
          {pData?.deleteParticipantFlag && <Permission service={['edit_participant']}>
            <View className={styles.delete} onClick={() => handleDelete(primaryKey || id)}>
              {intl.formatMessage({ id: 'yqc.mobile.delete', defaultMessage: '删除' })}
            </View>
          </Permission>}
        </SwipeCell.Actions>
      </SwipeCell>
    );
  }, [intl, handleDelete, type, openId, setOpenId]);

  return Array.isArray(record) && record.length > 0 ? (
    <View className={styles.card}>
      <View className={styles.header}>
        {type === 'user' ? intl.formatMessage({ id: 'yqc.mobile.participant', defaultMessage: '参与人' }) : intl.formatMessage({ id: 'yqc.mobile.participating.group', defaultMessage: '参与组' })}
      </View>
      <View className={styles.body}>
        {record.slice(0, expand ? undefined : 5).map(renderRecordItem)}
        {!expand && (
          <View className={styles.loadBtn} onClick={() => setExpand(true)}>
            {type === 'user' ? intl.formatMessage({ id: 'yqc.mobile.load.all.participants', defaultMessage: '加载全部参与人' }) : intl.formatMessage({ id: 'yqc.mobile.load.all.participating.groups', defaultMessage: '加载全部参与组' })}
            <YqIcon type="right" size={36} fill="#2979ff" className={styles.loadBtnIcon} />
          </View>
        )}
      </View>
      {loading && <Loading className={styles.loading} size={32} />}
    </View>
  ) : null;
};

export default ParticipantsCard;
