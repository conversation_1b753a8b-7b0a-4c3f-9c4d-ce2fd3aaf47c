import React, { SetStateAction } from 'react';
import { IntlShape } from '@/util/intl';
import { ParticipantsPermission } from '@/components/participants/api';

export type ParticipantsType = 'user' | 'group';

interface Record {
  [key: string]: string;
}

interface FetchConfig {
  ticketId: string;
  businessObjectCode: string;
  groupId?: string;
  userId?: string;
}

interface Data {
  user: Record[];
  group: Record[];
}

interface WidgetConfig {
  participantGroupLovId?: string; // 参与组值列表id
  participantGroupLovName?: string; // 参与组值列表名称
  participantUserLovId?: string; // 参与人值列表id
  participantUserLovName?: string; // 参与人值列表名称
}

interface Field {
  id: string;
  code: string;
  name: string;
  widgetConfig: WidgetConfig;
  widgetType: string;
}

export interface ParticipantsProps {
  title: string;
  field: Field;
  ticketId: string;
  businessObjectCode: string;
  businessObjectId: string
  intl: IntlShape;
}

export interface HeaderProps {
  intl: IntlShape;
  pData?: ParticipantsPermission;
  title: string;
  groupTotal: number;
  userTotal: number;
  fetchConfig: FetchConfig;
  data: Data;
  trigger: any;
  searchValue: string;
  setSearchValue: React.Dispatch<SetStateAction<string>>;
}

export interface PCardProps {
  pData?: ParticipantsPermission;
  record?: [] | null;
  type: ParticipantsType;
  intl: IntlShape;
  trigger: any;
  openId: string;
  searchValue: string;
  setOpenId: React.Dispatch<SetStateAction<string>>;
  businessObjectCode: string;
  ticketId: string;
}

interface RestField {
  meaning: string;
  value: string;
  intl: boolean;
}

export interface ParticipantsCellProps {
  imageUrl?: string;
  name?: string;
  type: ParticipantsType;
  id?: string;
  ownerName?: string;
  record?: object;
  restField?: Array<RestField>
}
