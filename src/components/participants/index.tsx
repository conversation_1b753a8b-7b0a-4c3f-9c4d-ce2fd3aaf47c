import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import { Loading } from '@taroify/core';
import { View } from '@tarojs/components';
import YqEmpty from '@/components/yq-empty';
import { ParticipantsProps, ParticipantsType } from './Participants';
import { useParticipants, useParticipantsPermission } from './api';
import Header from './components/header';
import ParticipantsCard from './components/participants-card';
import styles from './Participants.module.less';

const Participants: React.FC<ParticipantsProps> = function Participants(props) {
  const { title, field: { widgetConfig }, ticketId, businessObjectCode, intl, businessObjectId } = props;

  const [openId, setOpenId] = useState<string>('');
  const [searchValue, setSearchValue] = useState<string>('');
  const { data: pData } = useParticipantsPermission(businessObjectId, ticketId);
  const { participantGroupLovId: groupId, participantUserLovId: userId } = widgetConfig;

  const { data, isLoading, mutate } = useParticipants(businessObjectCode, ticketId, searchValue);

  const { groupTotal, userTotal, group, user } = data || {};

  const isEmpty = (!Array.isArray(group) || group.length === 0) && (!Array.isArray(user) || user.length === 0);

  return (
    <View className={styles.participants} onClick={() => setOpenId('')}>
      <Header
        pData={pData}
        intl={intl}
        title={title}
        groupTotal={groupTotal}
        userTotal={userTotal}
        fetchConfig={{ groupId, userId, ticketId, businessObjectCode }}
        data={{ user, group }}
        searchValue={searchValue}
        trigger={mutate}
        setSearchValue={setSearchValue}
      />
      <View className={styles.body}>
        {isLoading && <Loading className={styles.loading} />}
        {!isLoading && isEmpty && <YqEmpty style={{ background: '#fff' }} description={intl.formatMessage({ id: 'yqc.mobile.no.participants.and.groups.yet', defaultMessage: '暂无参与人/组' })} />}
        {!isLoading && !isEmpty && (
          <React.Fragment>
            {['user', 'group'].map((t: ParticipantsType) => (
              <ParticipantsCard
                pData={pData}
                key={t}
                intl={intl}
                type={t}
                record={t === 'user' ? user : group}
                trigger={mutate}
                openId={openId}
                searchValue={searchValue}
                setOpenId={setOpenId}
                businessObjectCode={businessObjectCode}
                ticketId={ticketId}
              />
            ))}
          </React.Fragment>
        )}
      </View>
    </View>
  );
};

export default observer(Participants);
