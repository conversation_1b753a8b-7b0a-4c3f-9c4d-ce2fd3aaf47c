import { useRef, useState } from 'react';
import { useIntl } from '@/util/intl';
import { Radio } from '@taroify/core';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import appStore from '@/store/app';
import { fetch } from '@/util';
import Modal from '@/components/yq-modal';
import styles from './PressBotton.module.less';

function ChildrenList({ data, setPressReason }) {
  const intl = useIntl();
  const [reason, setReason] = useState('other'); // 原因（可以是非汉字）
  return (
    <View className={styles.listPreviewPopup}>
      <View className={styles.listPreviewPopupList}>
        <Radio.Group
          defaultValue="other"
          onChange={(value) => {
            setReason(value);
            setPressReason(value);
          }}
        >
          {(data || []).filter(item => item.enabledFlag).map((item) => {
            return (
              <Radio name={item.value} style={{ marginBottom: '20px' }}>
                {item.value}
              </Radio>
            );
          })}
          <Radio name="other">{intl.formatMessage({ id: 'yqc.mobile.button.press.reason.other', defaultMessage: '其他' })}</Radio>
        </Radio.Group>
        {reason === 'other' ? (
          <textarea
            className={styles.listPreviewPopupTextarea}
            placeholder={intl.formatMessage({ id: 'yqc.mobile.button.press.input', defaultMessage: '请输入催办内容' })}
            onBlur={(e) => {
              setPressReason(e.target.value);
            }}
          />
        ) : null}
      </View>
    </View>
  );
}
const useOpenPress = (businessObjectCode, id) => {
  const intl = useIntl();
  const pressReason = useRef(); // 具体催办原因（汉字）
  const fun = async (_businessObjectCode, _id) => {
    if (_businessObjectCode && !businessObjectCode) {
      businessObjectCode = _businessObjectCode;
    }
    if (_id && !id) {
      id = _id;
    }
    const setPressReason = (value) => {
      pressReason.current = value;
    };
    const reasonUrl = `hpfm/v1/${appStore.tenantId === '0' ? '' : `${appStore.tenantId}/`}lookup/queryByCode/batch?REMINDER_TICKET_MESSAGE=REMINDER_TICKET_MESSAGE`;
    const postUrl = `itsm/v1/${appStore.tenantId}/ticket/reminders`;
    const { REMINDER_TICKET_MESSAGE } = await fetch(reasonUrl, {}, 'get');
    const modal = Modal.open({
      popupProps: { style: { height: '80vh', zIndex: 999 } },
      backdropProps: { style: { zIndex: 998 } },
      title: intl.formatMessage({ id: 'yqc.mobile.button.press.choose', defaultMessage: '选择催办内容' }),
      onOk: () => {
        if (businessObjectCode) {
          const postParams = {
            message: pressReason.current || intl.formatMessage({ id: 'yqc.mobile.button.press.reason.other', defaultMessage: '其他' }),
            ticketInfo: [{ businessObjectCode, id }],
          };
          Taro.atMessage({
            type: 'info',
            message: intl.formatMessage({ id: 'yqc.mobile.button.press.please.wait', defaultMessage: '正在努力催办' }),
          });
          fetch(postUrl, postParams, 'post').then((res) => {
            if (res.success) {
              Taro.atMessage({
                type: 'success',
                message: intl.formatMessage({ id: 'yqc.mobile.button.press.success', defaultMessage: '已成功催办' }),
              });
            } else {
              Taro.atMessage({
                type: 'error',
                message: res.message,
              });
            }
          });
        } else {
          Taro.atMessage({
            type: 'error',
            // message: intl.formatMessage({ id: 'yqc.mobile.button.press.cannot.press', defaultMessage: '该工单无法催办' }),
            message: intl.formatMessage({ id: 'yqc.mobile.button.press.cannot.press.because', defaultMessage: '该工单的businessObjectCode为' }) + businessObjectCode,
          });
        }
        modal.close();
      },
      onCancel: () => {
        modal.close();
      },
      children: <ChildrenList data={REMINDER_TICKET_MESSAGE} setPressReason={setPressReason} />,
    });
  };

  return fun;
};

export default useOpenPress;
