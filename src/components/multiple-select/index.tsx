import { FC, useEffect, useMemo, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { ScrollView, View } from '@tarojs/components';
import { List, Loading, Navbar, Search } from '@taroify/core';
import classNames from 'classnames';
import throttle from 'lodash/throttle';
import isArray from 'lodash/isArray';
import { useIntl } from '@/util/intl';
import { _ } from '@/util';
import DOMPurify from '@/util/dompurify';
import YqIcon from '@/components/yq-icon';
import YqEmpty from '@/components/yq-empty';
import YqCheckboxWithIcon from '@/components/yq-checkbox-with-icon';
import YqCloseableTag from '@/components/yq-closeable-tag';
import EmptyTips from '@/components/empty-tips';
import YqBreadcrumb from '@/components/yq-breadcrumb';
import useCommonPage from '@/hooks/use-common-page';
import useAutoScrollTop from '@/hooks/use-auto-scrollTop';
import SelectedList from './SelectedList';
import { MultipleSelectProps, Params, RequestStore } from './MultipleSelect';
import styles from './MultipleSelect.module.less';

const { sanitize } = DOMPurify;

const MultipleSelect: FC<MultipleSelectProps> = function MultipleSelect(props) {
  const {
    title,
    selectedTitle,
    search = true,
    feFilter = true,
    searchPrefix = 'search_name',
    url = '',
    params = {},
    method = 'get',
    valueField = 'id',
    displayField = 'name',
    records: _data = [],
    modal,
    showCheckbox = true,
    checkBoxDisabled = false,
    store = {} as RequestStore,
    breadcrumb = false,
    confirmTips: _confirmTips = '',
    checkBoxClass = '',
    wrapperClass = '',
    briefClass = '',
    onSearch = undefined,
    onConfirm = () => { },
    renderCell = () => { },
    onRowClick = () => true,
    onChange = () => { },
    onBreadcrumbClick = () => { },
    hiddenHeader = false,
    pageSize = 20,
    isRichText = false,
    swrOptions = {},
    showSelectedList = true,
  } = props;
  const realDisplayField = Array.isArray(displayField) ? displayField[0] : displayField;

  const intl = useIntl();
  const isPC = _.isPc();

  const [searchValue, setSearchValue] = useState<string>('');
  const [records, setRecords] = useState<Params[]>(() => _data);
  const [selectedRecords, setSelectedRecords] = useState<Params[]>(() => _data);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [showSelected, setShowSelected] = useState<boolean>(false);

  const _url = store.url || url;
  const _method = store.method || method;
  const _params = store.params || params;
  const _showCheckbox = typeof store.showCheckbox === 'boolean' ? store.showCheckbox : showCheckbox;
  const breadcrumbs = store.breadcrumbs || []; // 只在store里传，不然模态框里无法更新面包屑

  const curUrl = useMemo(() => {
    if (_method !== 'get') return _url;
    else return `${_url}${searchValue && searchPrefix ? `?${searchPrefix}=${searchValue}` : ''}`;
  }, [_url, searchValue, searchPrefix, _method]);

  const curParams = useMemo(() => {
    const body = { ..._params };
    if (_method !== 'get' && searchPrefix) {
      const isLov = body.params && typeof body.params === 'object';
      if (!searchValue) {
        if (searchPrefix in body) delete body[searchPrefix];
        if (isLov && searchPrefix in body.params) delete body.params[searchPrefix];
      } else {
        Object.assign(body, { [searchPrefix]: searchValue });
        if (isLov) {
          Object.assign(body.params, { [searchPrefix]: searchValue });
        }
      }
    }
    return body;
  }, [_method, _params, searchPrefix, searchValue]);

  const { data: fetchData, isLoading, size, setSize, hasMore: _hasMore } = useCommonPage(curUrl, curParams, _method, pageSize, swrOptions);
  let data = Array.isArray(store.options) ? store.options : fetchData;
  let hasMore = Array.isArray(store.options) ? false : _hasMore;

  if (Array.isArray(store.filteredOptions) && store.filteredOptions.length > 0 && data.length === 0) {
    data = Array.isArray(store.filteredOptions) ? store.filteredOptions : fetchData;
    hasMore = Array.isArray(store.filteredOptions) ? false : _hasMore;
  }
  if (searchValue && search && feFilter) {
    if (isArray(displayField)) {
      data = data.filter(item => displayField.some(f => item[f]?.toLowerCase().includes(searchValue.toLowerCase())));
    } else {
      data = data.filter(item => item[realDisplayField]?.toLowerCase().includes(searchValue.toLowerCase()));
    }
  }

  const confirmTips = useMemo(() => {
    if (_confirmTips) return _confirmTips;
    return `${intl.formatMessage({ id: 'yqc.mobile.confirm', defaultMessage: '确认' })}${showSelected ? '' : `(${records.length})`}`;
  }, [_confirmTips, intl, records.length, showSelected]);

  useAutoScrollTop();

  const handleConfirm = async () => {
    if (showSelected) {
      setRecords(selectedRecords);
      setShowSelected(false);
      return;
    }
    setConfirmLoading(true);
    await onConfirm(records, modal);
    setConfirmLoading(false);
  };

  const handleCancel = () => {
    if (showSelected) {
      setSelectedRecords(records);
      setShowSelected(false);
    } else modal?.close?.();
  };

  const handleLoadMore = () => {
    if (isLoading || !hasMore) return null;
    else setSize(size + 1);
  };

  const handleChangeRecord = (checked: boolean, record) => {
    const changeFlag = onRowClick(record, setRecords, setSelectedRecords);
    if (!changeFlag) return;
    if (checked) {
      setRecords((preRecords) => preRecords.filter(r => r[valueField] !== record[valueField]));
      setSelectedRecords((preRecords) => preRecords.filter(r => r[valueField] !== record[valueField]));
    } else {
      setRecords((preRecords) => [...preRecords, record]);
      setSelectedRecords((preRecords) => {
        const changedRecords = [...preRecords, record];
        onChange(changedRecords);
        return changedRecords;
      });
    }
  };

  const handleChangeSelectedRecord = (record) => {
    setSelectedRecords((preRecords) => preRecords.filter(r => r[valueField] !== record[valueField]));
  };

  const renderListItem = (item) => {
    const checked = records.some(record => record[valueField] === item[valueField]);

    const disabled = typeof checkBoxDisabled === 'function' ? checkBoxDisabled(item) : checkBoxDisabled;

    return (
      <View className={classNames(styles.cell, wrapperClass)} key={item[valueField]} onClick={() => handleChangeRecord(checked, item)}>
        {_showCheckbox && <YqCheckboxWithIcon className={classNames(styles.checkbox, checkBoxClass)} checked={checked} disabled={disabled} />}
        <View className={classNames(styles.brief, briefClass, { [styles.checked]: checked })}>
          {/* 让调用这个组件的渲染，不给默认渲染逻辑 */}
          {renderCell(item, displayField)}
        </View>
      </View>
    );
  };
  // 单选和多选及多对一关联的弹出框
  return (
    <View className={styles.multipleSelect}>
      {!hiddenHeader && <Navbar className={styles.navbar}>
        <Navbar.NavLeft icon={null} className={styles.cancel} onClick={handleCancel}>
          {intl.formatMessage({ id: 'yqc.mobile.cancel', defaultMessage: '取消' })}
        </Navbar.NavLeft>
        <Navbar.Title className={classNames(styles.title, 'taroify-ellipsis--l2')}>
          {showSelected ? (selectedTitle || title) : title}
        </Navbar.Title>
        <Navbar.NavRight className={styles.confirm} onClick={handleConfirm}>
          {confirmLoading ? <Loading /> : confirmTips}
        </Navbar.NavRight>
      </Navbar>}
      {search && (
        <Search
          className={classNames(styles.search, { [styles.empty]: records.length === 0 })}
          placeholder={intl.formatMessage({ id: 'yqc.mobile.search.options', defaultMessage: '搜索选项' })}
          icon={<YqIcon type="icon-search" size={36} fill="#12274d" opacity={0.45} />}
          // value={searchValue}
          clearable={!isPC}
          onSearch={(e) => {
            const value = e.detail.value?.trim();
            typeof onSearch === 'function' && onSearch(value);
            setSearchValue(value);
          }}
          onChange={throttle((e) => {
            const value = e.detail.value;
            typeof onSearch === 'function' && onSearch(value);
            setSearchValue(value);
          }, 500, { trailing: true, leading: false })}
          onClear={() => {
            typeof onSearch === 'function' && onSearch('');
            setSearchValue('');
          }}
        />
      )}
      {records.length > 0 && !hiddenHeader && showSelectedList !== false && (
        <View className={styles.selectWrapper}>
          <ScrollView scrollX className={styles.selected}>
            {records.map((record) => {
              let showText;
              if (Array.isArray(displayField)) {
                showText = record[displayField.find(f => record[f]) || 'name'];
              } else if (isRichText) {
                showText = <View style={{ overflow: 'hidden' }} dangerouslySetInnerHTML={{ __html: sanitize(record[displayField]) }} />;
              } else {
                showText = record[displayField];
              }
              return (
                <YqCloseableTag
                  closeable
                  className={styles.closeableTag}
                  key={record[valueField]}
                  onClose={() => handleChangeRecord(true, record)}
                >
                  {showText}
                </YqCloseableTag>
              );
            })}
          </ScrollView>
          <View className={styles.selectedIcon} onClick={() => setShowSelected(true)}>
            <YqIcon type="right" size={36} fill="#12274d" opacity={0.65} />
          </View>
        </View>
      )}
      {/* 搜索不展示面包屑 */}
      {breadcrumb && !searchValue && (
        <ScrollView scrollX>
          <YqBreadcrumb separator="/">
            {breadcrumbs.length > 0 ? breadcrumbs.map(b => (
              <YqBreadcrumb.Item key={typeof b.value === 'object' ? b.value?.key : b.value} value={b.value} onClick={onBreadcrumbClick}>
                {b.name || '-'}
              </YqBreadcrumb.Item>
            )) : null}
          </YqBreadcrumb>
        </ScrollView>
      )}
      <ScrollView
        scrollY
        className={styles.list}
        onScrollToLower={handleLoadMore}
        key={`${JSON.stringify(curUrl)}${JSON.stringify(curParams)}`}
      >
        <List loading={isLoading} hasMore={hasMore}>
          {data.length > 0 ? data.map(renderListItem) : <YqEmpty isLoading={isLoading} />}
          <List.Placeholder className={styles.placeholder}>
            {isLoading && <Loading />}
            {!isLoading && !hasMore && data.length > 0 && <EmptyTips />}
          </List.Placeholder>
        </List>
      </ScrollView>
      <SelectedList
        show={showSelected}
        displayField={displayField}
        valueField={valueField}
        records={selectedRecords}
        renderCell={renderCell}
        onDelete={handleChangeSelectedRecord}
      />
    </View>
  );
};

export default observer(MultipleSelect);
