import React from 'react';
import classNames from 'classnames';
import { observer } from 'mobx-react-lite';
import { ScrollView, View } from '@tarojs/components';
import YqEmpty from '@/components/yq-empty';
import YqIcon from '@/components/yq-icon';
import { SelectedListProps } from './MultipleSelect';
import styles from './MultipleSelect.module.less';

const SelectedList: React.FC<SelectedListProps> = function SelectedList(props) {
  const { show, records, displayField, valueField, renderCell = () => {}, onDelete = () => {} } = props;

  const handleDelete = (record) => {
    onDelete(record);
  };

  const renderSelectedCell = (record) => {
    return (
      <View className={styles.selectedCell} key={record[valueField]}>
        <View className={styles.selectedBrief}>
          {renderCell(record, displayField, true)}
        </View>
        <View className={styles.delete} onClick={() => handleDelete(record)}>
          <YqIcon type="close-one" size={40} fill="#8a94a5" theme="filled" />
        </View>
      </View>
    );
  };

  return (
    <ScrollView scrollY className={classNames(styles.selectedList, { [styles.show]: show })}>
      {records.length > 0 ? records.map(renderSelectedCell) : <YqEmpty />}
    </ScrollView>
  );
};

export default observer(SelectedList);
