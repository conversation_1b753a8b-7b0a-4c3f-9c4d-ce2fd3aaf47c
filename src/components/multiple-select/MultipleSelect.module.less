.multipleSelect {
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

.navbar {
  height: 104px;
  flex-shrink: 0;
  box-shadow: 0px 0px 0px 0px #ddd;
  font-family: PingFangSC-Regular, PingFang SC;
  font-size: 32px;
  line-height: 32px;
  border-bottom: 0.5px solid #cbd2dc;

  .confirm {
    --navbar-text-color: #2979FF;
  }

  .cancel {
    --navbar-text-color: rgba(18, 39, 77, 0.65);
  }

  .title {
    max-width: 50%;
    font-family: PingFangSC-Medium, PingFang SC;
    word-break: break-word;
    --navbar-title-color: #12274d;
  }
}

.search {
  height: 80px;
  flex-shrink: 0;
  padding: 12px 24px 0 24px !important;

  &.empty {
    //height: 92px;
    --search-padding: 12px 24px;
  }
}

.selectWrapper {
  height: 93px;
  flex-shrink: 0;
  padding: 16px 24px;
  line-height: 61px;
  display: flex;
  align-items: center;
  border-bottom: 0.5px solid #cbd2dc;

  .selected {
    width: 0;
    height: 100%;
    flex-grow: 1;
    word-break: keep-all;
    white-space: nowrap;

    .closeableTag {
      max-width: 90%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-break: keep-all;
      display: inline-block;
      line-height: 60px;
      position: relative;
      padding-right: 48px !important;

      :global {
        .taroify-tag__close {
          position: absolute;
          right: 18px;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
  }

  .selectedIcon {
    width: 50px;
    height: 100%;
    flex-shrink: 0;
    margin-right: -10px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    box-shadow: -5px 0 10px -7px rgba(0, 0, 0, 0.2);
  }
}

.list {
  height: 0;
  flex-grow: 1;
  padding: 0 24px;

  .placeholder {
    height: 100px;
  }
}

.cell {
  min-height: 100px;
  display: flex;
  align-items: center;

  .checkbox {
    margin-right: 28px;
    flex-shrink: 0;
  }

  .brief {
    width: 0;
    flex-grow: 1;

    &.checked {
      color: #2979ff;
    }
  }
}

.selectedList {
  width: 100%;
  height: calc(100% - 104px);
  position: absolute;
  transform: translateX(100%);
  transition-duration: 200ms;
  top: 104px;
  right: 0;
  z-index: 1021;
  background-color: #fff;

  &.show {
    transform: translateX(0);
  }

  .selectedCell {
    padding-left: 24px;
    display: flex;

    .selectedBrief {
      width: 0;
      flex-grow: 1;
    }

    .delete {
      flex-shrink: 0;
      display: flex;
      padding-left: 16px;
      padding-right: 24px;
      align-items: center;
      border-bottom: 1px solid rgba(203, 210, 220, 0.5);
    }
  }
}
