import { SWRInfiniteConfiguration } from 'swr/infinite';

type DisplayField = string | string[];

interface Params {
  [key: string]: any;
}

interface Breadcrumb {
  value: any;
  name: string;
}

export interface RequestStore {
  url: string;
  params: Params;
  method: string;
  showCheckbox?: boolean;
  displayField?: string;
  valueField?: string;
  breadcrumbs?: Breadcrumb[];
  options?: Record<string, any>[]; // 自定义数据集
  filteredOptions?: Record<string, any>[]; // 自定义数据集
}

export interface MultipleSelectProps {
  onChange?: Function
  key?: string;
  title: string; // 标题
  selectedTitle?: string; // 选择框的标题，不传就是title
  url?: string; // 请求值列表的url
  search?: boolean; // 是否有搜索框
  feFilter?: boolean; // 在设置为值列表时，要支持模糊搜索，不需要前端过滤
  params?: Params; // 请求值列表的url参数
  method?: string; // 请求值列表的方法 'get' 'post'等
  searchPrefix?: string; // 搜索的前缀
  displayField?: DisplayField; // 显示值的key
  valueField?: string; // 选用值的key
  modal?: any; // 模态框的引用
  records: Params[]; // 选择的值列表
  customDataSet?: Params[]; // 自定义的总数据
  breadcrumb?: boolean; // 是否启用面包屑
  showCheckbox?: boolean; // 是否显示checkbox
  checkBoxDisabled?: boolean | ((record: Record<string, any>) => boolean); // checkbox是否禁用
  confirmTips?: string // 确定按钮的问题
  checkBoxClass?: string // checkbox样式
  wrapperClass?: string // wrapper cell 样式
  briefClass?: string // brief 样式
  store?: RequestStore;
  renderCell: (value: Params, displayField: DisplayField) => any; // 渲染每个子节点的方法
  onConfirm?: (records: Params[], modal?: any) => Promise<any>; // 确认方法
  onRowClick?(record: Record<string, any>, setR?: any, setSR?: any): boolean; // 自定义的点击子节点方法，前提是传tree, 返回为false，则不更改选中的值
  onBreadcrumbClick?(value: any): void; // 面包屑的点击事件
  onSearch?(v: string): void; // 搜索事件
  hiddenHeader: boolean;
  pageSize: number; // 默认分页大小
  isRichText: boolean; // 展示字段富文本
  swrOptions?: SWRInfiniteConfiguration;
  showSelectedList?: boolean;
}

export interface SelectedListProps {
  show: boolean;
  records: Params[];
  displayField: DisplayField;
  valueField: string;
  renderCell: (value: object, displayField: DisplayField, selectPage?: boolean) => any;
  onDelete: (record: Params) => any;
}
