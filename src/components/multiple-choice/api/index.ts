import { useCallback, useMemo } from 'react';
import useSWR from 'swr';
import queryString from 'query-string';
import appStore from '@/store/app';
import { fetch } from '@/util';

interface QueryProperties {
  multipleRelatedFieldCode?: string;
  multipleTargetFieldCode?: string;
  multipleModelId?: string;
  ticketId?: string;
  nameField?: string;
}

function useMultipleValues(props: QueryProperties) {
  const { multipleRelatedFieldCode, multipleTargetFieldCode, multipleModelId, ticketId, nameField } = props;

  const tenantId = appStore.tenantId;

  const queryUrl = `lc/v1/engine/${tenantId}/dataset/searchByParamsForFields`;

  const query = {
    fields: `${multipleRelatedFieldCode},${multipleTargetFieldCode},${multipleTargetFieldCode}:${nameField}`,
    businessObjectId: multipleModelId,
  };

  const body = useMemo(() => (multipleRelatedFieldCode ? ({ [multipleRelatedFieldCode]: ticketId }) : {}), [multipleRelatedFieldCode, ticketId]);

  const url = nameField ? `${queryUrl}?${queryString.stringify(query)}#${JSON.stringify(body)}` : '';

  const fetcher = useCallback((_url) => {
    const [fetchUrl] = _url?.split('#') || [];
    return fetch(fetchUrl, body, 'POST');
  }, [body]);

  return useSWR(url, fetcher, { revalidateOnFocus: false, revalidateIfStale: true });
}

async function updateMultipleChoice({
  multipleRelatedFieldCode,
  multipleTargetFieldCode,
  multipleModelId,
  titleModuleId,
  viewId,
  name,
  body,
}) {
  const tenantId = appStore.tenantId;

  const query = {
    fields: `${multipleRelatedFieldCode},${multipleTargetFieldCode}`,
    multipleRelatedFieldCode,
    multipleTargetFieldCode,
    businessObjectId: multipleModelId,
    name,
    viewId,
    titleModuleId,
  };

  const url = `lc/v1/engine/${tenantId}/dataset/submitByParamsForFields?${queryString.stringify(query)}`;

  const res = await fetch(url, body, 'post').catch(err => err);

  return res;
}

export { useMultipleValues, updateMultipleChoice };
