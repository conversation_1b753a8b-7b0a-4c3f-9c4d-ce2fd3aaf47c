import { CommonFieldProps } from '@/types/CommonField';
import { DisplayField } from '@/types/LovConfig';

interface MultipleChoiceWidgetConfig {
  multipleLovVariableCode: string; // 值列表 eg: "191128377610997760"
  multipleModelId: string; // 业务对象 eg: "308045373115465728"
  multipleRelatedFieldCode: string; // 关联字段 eg: "incident_id"
  multipleTargetFieldCode: string; // 目标字段 eg: "problem_id"
  condition: any;
}

interface MultipleChoiceField {
  id: string;
  code: string;
  name: string;
  placeHolder?: string;
  widgetConfig: MultipleChoiceWidgetConfig;
  widgetType: string;
}

export interface MultipleChoiceProps extends CommonFieldProps {
  field: MultipleChoiceField;
}

export interface MultipleChoiceCellProps {
  record: Record<string, any>;
  nameField: string[];
  displayFields?: DisplayField[] | string[];
  selfPage?: boolean;
}
