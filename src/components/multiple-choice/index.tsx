import React, { ReactElement, useCallback, useEffect, useState, useMemo } from 'react';
import { observer, useLocalObservable } from 'mobx-react-lite';
import { runInAction } from 'mobx';
import { ITouchEvent } from '@tarojs/components';
import Taro from '@tarojs/taro';
import MbRow from '@/components/mb-row';
import Modal from '@/components/yq-modal';
import MultipleSelect from '@/components/multiple-select';
import YqCloseableTag from '@/components/yq-closeable-tag';
import useLovConfig from '@/hooks/use-lov-config';
import appStore from '@/store/app';
import MultipleChoiceCell from './components/MultipleChoiceCell';
import { updateMultipleChoice, useMultipleValues } from './api';
import { MultipleChoiceProps } from './MultipleChoice';
import styles from './MultipleChoice.module.less';

const MultipleChoice: React.FC<MultipleChoiceProps> = function MultipleChoice(props) {
  const { viewId, title, disabled, required, cardMode, field, ticketId: propsTicketId, values, placeholder, onChange } = props;
  const tenantId = appStore.tenantId;
  const params = useMemo(() => Taro.getCurrentInstance()?.router?.params, []);

  const [ticketId, setTicketId] = useState(propsTicketId);

  useEffect(() => {
    setTicketId(ticketId || values?.id || values?._id);
  }, [values?._id, values?.id]);

  const {
    multipleLovVariableCode,
    multipleModelId,
    multipleRelatedFieldCode,
    multipleTargetFieldCode,
    condition,
  } = field?.widgetConfig || {};

  const [config] = useLovConfig(multipleLovVariableCode);

  const { data, mutate } = useMultipleValues({
    multipleModelId,
    multipleRelatedFieldCode,
    multipleTargetFieldCode,
    ticketId: ticketId || params?.ticketId,
    nameField: config?.nameField,
  });

  const store = useLocalObservable(() => ({
    url: '',
    method: 'POST',
    records: [] as Record<string, any>[],
    params: {
      conditions: condition,
      params: {
        __page_params: {},
        _id: values?._id,
      },
    },
  }));

  useEffect(() => {
    runInAction(() => {
      store.url = `lc/v1/engine/${tenantId}/options/${multipleLovVariableCode}/queryWithCondition`;
    });
  }, [multipleLovVariableCode, store, tenantId]);

  useEffect(() => {
    if (Array.isArray(data) && config?.idField) {
      runInAction(() => {
        data.forEach(record => {
          // 单据可能已经处理过了，就不处理了
          if (!record.originId) {
            const proprity = { [config.idField]: record[multipleTargetFieldCode], originId: record.id };
            Object.assign(record, proprity);
          }
        });
        store.records = data;
        onChange(data.length > 0 ? data : undefined);
      });
    }
  }, [config, data, multipleTargetFieldCode, store]);

  const handleClose = useCallback((event: ITouchEvent, record: Record<string, any>) => {
    event.stopPropagation();
    updateMultipleChoice({
      multipleModelId,
      multipleRelatedFieldCode,
      multipleTargetFieldCode,
      viewId,
      name: field.name,
      titleModuleId: field.id,
      body: [{ ...record, id: record.originId, _status: 'delete' }],
    }).finally(() => {
      mutate();
    });
  }, [multipleModelId, multipleRelatedFieldCode, multipleTargetFieldCode, mutate]);

  const renderBrief = useCallback<() => ReactElement[]>(() => {
    const nameFieldCode = config?.nameField || 'name'; // 'name' 还是 'id'? 先写 name
    return store.records.map((v) => (
      <YqCloseableTag key={v[multipleTargetFieldCode]} color="primary" size="medium" closeable={!disabled} onClose={(e) => handleClose(e, v)}>
        {v[`${multipleTargetFieldCode}:${nameFieldCode}`]}
      </YqCloseableTag>
    ));
  }, [config, store, handleClose, multipleTargetFieldCode]);

  const renderCell = useCallback((record: Record<string, any>, nameField: string[], selfPage?: boolean) => (
    <MultipleChoiceCell
      record={record}
      nameField={nameField}
      selfPage={selfPage}
      displayFields={config?.displayFields}
    />
  ), [config]);

  const handleConfirm = async (records: Record<string, any>[], modal?: any) => {
    const idField = config?.idField || 'id';
    const nameField = config?.nameField || 'name';
    const fixRecords = records.map(record => ({
      _status: 'create',
      [multipleRelatedFieldCode]: ticketId,
      [multipleTargetFieldCode]: record[idField],
      [nameField]: record[nameField],
    }));
    const removedData: any[] = store.records.filter(v => !fixRecords.some(innerV => innerV[multipleTargetFieldCode] === v[multipleTargetFieldCode])).map(v => ({ ...v, id: v.originId, _status: 'delete' }));
    const createdData = fixRecords.filter(v => !store.records.some(innerV => innerV[multipleTargetFieldCode] === v[multipleTargetFieldCode])).map(v => ({ ...v, _status: 'create' }));
    const res = {
      multipleModelId,
      multipleRelatedFieldCode,
      multipleTargetFieldCode,
      viewId,
      name: field.name,
      titleModuleId: field.id,
      body: removedData.concat(createdData),
    };
    onChange(res);
    await updateMultipleChoice(res).finally(async () => {
      await mutate();
      modal.close();
    });
  };

  const handleClear = () => {
    const removedData: any[] = store.records.map(v => ({ ...v, id: v.originId, _status: 'delete' }));
    const res = {
      multipleModelId,
      multipleRelatedFieldCode,
      multipleTargetFieldCode,
      viewId,
      name: field.name,
      titleModuleId: field.id,
      body: removedData,
    };
    onChange(res);
    updateMultipleChoice(res).finally(() => {
      mutate();
    });
  };

  const handleOpen = () => {
    Modal.open({
      popupProps: { style: { height: '80vh' } },
      children: (
        <MultipleSelect
          title={title}
          valueField={config?.idField}
          displayField={[`${multipleTargetFieldCode}:${config?.nameField}`, config?.nameField || '']}
          method={config?.method}
          search={config?.searchable}
          searchPrefix={config?.searchField}
          records={store.records}
          store={store}
          checkBoxClass={styles.checkbox}
          wrapperClass={styles.wrapper}
          briefClass={styles.brief}
          renderCell={renderCell}
          onConfirm={handleConfirm}
        />
      ),
    });
  };

  return (
    <MbRow
      clearButton
      hiddenRight
      label={title}
      disabled={disabled}
      required={required}
      cardMode={cardMode}
      field={field}
      values={values}
      onClick={handleOpen}
      handleClear={handleClear}
      placeholder={cardMode ? '' : (placeholder || field?.placeHolder)}
      brief={renderBrief()}
    />
  );
};

export default observer(MultipleChoice);
