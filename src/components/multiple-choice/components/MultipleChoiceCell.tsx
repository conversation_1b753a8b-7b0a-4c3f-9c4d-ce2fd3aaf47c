import React, { useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import classNames from 'classnames';
import YqCell from '@/components/yq-cell';
import YqOutput from '@/components/yq-output';
import { DisplayField } from '@/types/LovConfig';
import { MultipleChoiceCellProps } from '../MultipleChoice';
import styles from './MultipleChoiceCell.module.less';

const MultipleChoiceCell: React.FC<MultipleChoiceCellProps> = function MultipleChoiceCell(props) {
  const { record, nameField, displayFields, selfPage } = props;

  const renderTitle = useCallback(() => {
    if (typeof nameField === 'string') return record[nameField];
    else return record[nameField.find(f => record[f]) || 'name'];
  }, [nameField, record]);

  const renderField = useCallback((field: DisplayField | string) => {
    if (typeof field === 'string') return record[field];
    return record[field.code] ? (
      <YqOutput
        key={field.code}
        className={styles.output}
        label={field.label}
        titleClassName={styles.outputTitle}
        contentClassName={styles.outputContent}
      >
        {record[field.code]}
      </YqOutput>
    ) : null;
  }, [record]);

  return (
    <YqCell
      className={classNames(styles.cell, { [styles.selfPage]: selfPage })}
      briefClassName={styles.brief}
      topClassName={classNames(styles.name, { [styles.onlyName]: !displayFields?.length })}
      title={renderTitle()}
    >
      {Array.isArray(displayFields) && displayFields.length > 0 && displayFields.map(renderField)}
    </YqCell>
  );
};

export default observer(MultipleChoiceCell);
