import React from 'react';
import Modal from '@/components/yq-modal';
import { fetch } from '@/util';
import appStore from '@/store/app';
import ModalView from './ModalView';
import Style from './Index.module.less';

const openCommonPhrase = async ({ historyInputComponentId, handleChange }) => {
  const res = await fetch(`/lc/v1/${appStore?.tenantId}/common_phrases/list?components=${`${historyInputComponentId}`}&size=3`);
  if (res && !res?.failed && res?.length) {
    let instanceId;
    Modal.open({
      children: (
        <ModalView tenantId={appStore?.tenantId} phraseData={res} historyInputComponentId={historyInputComponentId} handleChange={handleChange} />
      ),
      popupProps: {
        placement: 'center',
  
      },
      header: null,
      footer: null,
      bodyStyle: { padding: 0 },
      wrapperClassName: Style.commonPhrase,
    });
  }
};

export default openCommonPhrase;
