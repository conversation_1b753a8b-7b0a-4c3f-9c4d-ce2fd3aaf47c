import React, { useState, useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { Textarea, Input, Toast } from '@taroify/core';
import classnames from 'classnames';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { _, fetch } from '@/util';
import { useIntl } from '@/util/intl';
import Icon from '../yq-icon';
import Style from './Index.module.less';

function ModalView({ tenantId, modal, instanceId: Iid, phraseData, historyInputComponentId, handleChange = () => {}, QuillModalFlag, ChangeDisplayFn = () => {} }) {
  const [data, setData] = useState(phraseData);
  const intl = useIntl();
  useEffect(() => {
    if (QuillModalFlag) {
      queryPhrasesList();
    }
  }, [QuillModalFlag]);
  const queryPhrasesList = async () => {
    const res = await fetch(`/lc/v1/${tenantId}/common_phrases/list?components=${`${historyInputComponentId}`}&size=3`);
    if (res && !res.failed) {
      setData(res);
    }
  };
  const deletePhrasesList = async (i) => {
    const res = await fetch(`/lc/v1/${tenantId}/common_phrases/delete`, { [i?.component]: i?.phrase }, 'delete');
    if (res && !res.failed) {
      queryPhrasesList();
    }
  };

  const handlefillinPhrases = (text) => {
    handleChange(text);
  };

  const handleChangeDisplay = () => {
    try {
      ChangeDisplayFn();
      modal?.close();
    } catch (error) {
      modal?.close();
    }
  };

  function extractTextFromHTMLDOM(htmlString) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlString, 'text/html');
    return doc.body.textContent || doc.body.innerText;
  }
  if (!data || !data?.length) {
    return null;
  }

  return <div className={Style.wrapper}>
    {data?.map(i => {
      return <div className={Style.item}>
        <div className={Style.text} onClick={() => handlefillinPhrases(i?.phrase)}>{i?.phrase}</div>
        <Icon type="close" size={30} onClick={() => deletePhrasesList(i)} fill="#12274D" />
      </div>;
    })}
    <div className={Style.open} onClick={handleChangeDisplay}>
      <span>{intl.formatMessage({ id: 'yqc.mobile.components.commonPhrases.put', defaultMessage: '收起' })}</span>
      <Icon type="down" size={30} onClick={() => {}} fill="#12274D" />
    </div>
  </div>;
}

export default observer(ModalView);
