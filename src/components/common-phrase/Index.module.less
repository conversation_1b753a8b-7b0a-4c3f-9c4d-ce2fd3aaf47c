.commonPhrase {
  height: 200px;
  width: 100%;
  background-color: #fff;
  :global {
    .taroify-popup {
      height: 100%;
      width: 100%;
      border-radius: 0 !important;
    }
    .taroify-backdrop {
      background-color: transparent !important;
    }
  }
}
.wrapper {
  border-top: 1px solid #E1E5EB;
  display: flex;
  flex-wrap: wrap;
  padding: 30px;
  position: relative;
  .item {
    background-color: #F2F3F5;
    padding: 2px 20px;
    border-radius:4px;
    display: flex;
    align-items: center;
    margin: 2px 4px;
    .text {
      margin-right: 8px;
      max-width: 360px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .open {
    position: absolute;
    right: 20px;
    bottom: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 20px;
  }
}
.commonPhraseHidden {
  display: none;
}