import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { fetch as yqFetch } from '../../util';
import { FAILURE, SUCCESS } from './PermissionStatus';

const DELAY = 500;

class PermissionProvider extends Component {
  // eslint-disable-next-line react/static-property-placement
  static childContextTypes = {
    permission: PropTypes.object,
  };

  delayId = 0;

  permissions = new Map();

  queue = new Set();

  handlers = new Set();

  getChildContext() {
    return {
      permission: this,
    };
  }

  fetch(id, service) {
    const handlers = Array.from(this.handlers);
    yqFetch(`iam/yqc/v1/${id}/menus/check-permissions`, service, 'post')
      .then((data) => {
        data.forEach(({ code, resourceType, tenantId, approve }) => {
          const key = JSON.stringify(this.judgeService(code, tenantId));
          this.permissions.set(key, approve ? SUCCESS : FAILURE);
        });
        handlers.forEach(([props, handler]) => this.check(props, handler, true));
      });
  }

  start({ tenantId, service }) {
    if (this.delayId) {
      clearTimeout(this.delayId);
    }
    this.delayId = setTimeout(() => {
      this.fetch(tenantId, service);
      this.queue.clear();
      this.handlers.clear();
    }, DELAY);
  }

  check(props, handler, flag) {
    if (!props.service || props.service.length === 0) {
      handler(SUCCESS);
    } else {
      const queue = new Set();
      if (this.judgeServices(props).every((item) => {
        if (item) {
          const key = JSON.stringify(item);
          const status = this.permissions.get(key);
          if (status === SUCCESS) {
            handler(status);
            return false;
          } else if (status !== FAILURE) {
            this.queue.add(key);
            queue.add(key);
          }
        }
        return true;
      })
      ) {
        if (queue.size > 0 && !flag) {
          this.handlers.add([props, handler]);
          this.start(props);
        } else {
          handler(FAILURE);
        }
      }
    }
  }

  judgeServices({ service, type, tenantId, projectId }) {
    return service
      .map(code => this.judgeService(code, tenantId));
  }

  judgeService(code, tenantId) {
    return { code };
  }

  render() {
    return this.props.children;
  }
}

export default PermissionProvider;
