import React, { Children, cloneElement, Component, createElement, isValidElement } from 'react';
import PropTypes from 'prop-types';
import { inject, observer } from 'mobx-react';
import omit from 'object.omit';
import appStore from '../../store/app';
import { FAILURE, PENDING, SUCCESS } from './PermissionStatus';

@observer
class Permission extends Component {
  static propTypes = {
    service: PropTypes.arrayOf(PropTypes.string).isRequired,
    type: PropTypes.string,
    projectId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    tenantId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    defaultChildren: PropTypes.node,
    noAccessChildren: PropTypes.node,
    onAccess: PropTypes.func,
    onDeny: PropTypes.func,
  };

  static contextTypes = {
    permission: PropTypes.object,
  };

  state = {
    status: PENDING,
  };

  componentWillMount() {
    this.check(this.props, this.context);
  }

  componentWillReceiveProps(nextProps, nextContext) {
    this.check(nextProps, nextContext);
  }

  componentDidMount() {
    this.triggerAccess();
  }

  componentDidUpdate(preProps, preState) {
    this.triggerAccess(preState);
  }

  triggerAccess(preState = {}) {
    const { status } = this.state;
    const { onAccess, onDeny } = this.props;
    if (status === SUCCESS && preState.status !== SUCCESS && typeof onAccess === 'function') {
      onAccess();
    } else if (status === FAILURE && preState.status !== FAILURE && typeof onDeny === 'function') {
      onDeny();
    }
  }

  check(props, context) {
    if (context.permission) {
      context.permission.check(this.getPermissionProps(props), this.handlePermission);
    }
  }

  handlePermission = (status) => {
    this.setState({
      status,
    });
  };

  getPermissionProps(props) {
    const { tenantId: tenantIdState } = appStore || {};
    const {
      service,
      type = 'tenant',
      tenantId = tenantIdState,
    } = props;
    return {
      service,
      type,
      tenantId,
    };
  }

  extendProps(children, props) {
    if (isValidElement(children)) {
      return Children.map(children, (child) => {
        if (isValidElement(child)) {
          return cloneElement(child, props);
        } else {
          return child;
        }
      });
    } else {
      return children;
    }
  }

  render() {
    const { defaultChildren, children, noAccessChildren } = this.props;
    const otherProps = omit(this.props, [
      'service', 'type', 'tenantId', 'defaultChildren',
      'noAccessChildren', 'children', 'onAccess', 'AppState',
    ]);
    const { status } = this.state;
    if (status === SUCCESS) {
      return this.extendProps(children, otherProps);
    } else if (status === FAILURE && (noAccessChildren || defaultChildren)) {
      return this.extendProps(noAccessChildren || defaultChildren, otherProps);
    } else if (status === PENDING && defaultChildren) {
      return this.extendProps(defaultChildren, otherProps);
    } else {
      return null;
    }
  }
}

export default Permission;
