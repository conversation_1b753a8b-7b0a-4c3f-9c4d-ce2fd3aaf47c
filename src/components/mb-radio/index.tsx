import { useEffect, useState, useRef } from 'react';
import Taro from '@tarojs/taro';
import { Radio } from '@taroify/core';
import { View } from '@tarojs/components';
import MbRow from '@/components/mb-row';
import Icon from '@/components/yq-icon';
import appStore from '@/store/app';
import { _, fetch } from '@/util';
import styles from './Radio.module.less';

function MbRadio(props) {
  const verticalFlag = props?.field?.widgetConfig?.verticalFlag || props.verticalFlag;
  const [state, _setState] = useState({
    data: props.data || [],
    page: -1,
    size: 20,
    keyMap: props.keyMap,
    loading: undefined,
    open: undefined,
  });
  const stateRef = useRef(state);
  const noMore = useRef(false);

  function setState(newState) {
    stateRef.current = { ...stateRef.current, ...newState };
    _setState(stateRef.current);
  }

  // 这个方法从 masterdetail 拷过来的
  async function fetchData(isInit = false) {
    if (isInit) setState({ page: 0 });
    const page = isInit ? 0 : state.page + 1;
    const { size } = state;
    const { url, data, type, sourceKey } = props.api;
    data.page = page;
    const { lovCode } = props;
    if (lovCode) {
      const lovConfig = await fetch(`lc/v1/${appStore.tenantId}/object_options/id/${lovCode}?page=${page}&size=${size}`, undefined, 'get').catch(() => {});
      setState({
        keyMap: {
          key: state.keyMap.key,
          value: JSON.parse(lovConfig.jsonData)?.nameFieldCode || JSON.parse(lovConfig.jsonData)?.dataSource?.nameFieldCode || state.keyMap.value,
        },
      });
    }
    setState({ loading: true });
    const { variableFilter } = props.field?.widgetConfig || {};
    const variableParams = {};
    variableFilter?.map(({ variable, relatedFieldCode }) => {
      variableParams[variable] = props.values?.[relatedFieldCode]?.id || props.values?.[relatedFieldCode];
      return variable;
    });
    Object.keys(variableParams).forEach((key) => {
      if (typeof variableParams[key] === 'object' && variableParams[key] !== null && !key.endsWith(':_variable')) variableParams[key] = undefined;
    });
    const lovParams = {
      __page_params: variableParams,
      ..._.transformFormValue(props.values || {}),
    };
    const result = await fetch(`${url}${url.includes('?') ? '&' : '?'}page=${page}&size=1000`, type?.toLowerCase() === 'post' ? {
      conditions: props.field?.widgetConfig?.condition || [],
      params: lovParams,
    } : {}, type, false, {}, false).catch(() => {});
    setState({
      loading: false,
    });
    if (!result) {
      Taro.atMessage({
        type: 'warning',
        message: 'Some request failed when [ Mb Radio ] component init!',
      });
    } else {
      const resultArr = sourceKey ? result[sourceKey] : result;
      if (!resultArr?.length) {
        if (page !== 0) {
          Taro.atMessage({
            type: 'info',
            message: props.intl.formatMessage({ id: 'yqc.mobile.no.more', defaultMessage: '没有更多了' }),
          });
        }
        noMore.current = true;
      } else {
        setState({
          page,
          data: isInit ? resultArr : state.data.concat(resultArr),
        });
      }
    }
  }

  useEffect(() => {
    if (props.api) fetchData(true);
  }, []);

  const onChange = (val) => {
    props?.onChange(val);
  };

  const data = props?.api ? state?.data : props?.data;

  const radioGroup = (
    <View
      style={{
        display: 'flex',
        justifyContent: verticalFlag ? 'left' : 'right',
      }}
    >
      <Radio.Group
        className={verticalFlag ? '' : styles.radioGroup}
        direction={verticalFlag ? 'vertical' : 'horizontal'}
        value={props?.value}
        disabled={props.disabled}
        onChange={onChange}
      >
        {data?.map(item => {
          if (item.enabledFlag === true || item.enabledFlag === undefined) {
            return (
              <Radio
                icon={item[props.keyMap.key] === props.value ? (
                  <Icon type="icon-checkone" size={40} />
                ) : <Icon type="round" size={40} fill="#12274d" opacity={0.2} />}
                key={item[props.keyMap.key]}
                name={item[props.keyMap.key]}
              >{item[props.keyMap.value]}</Radio>
            );
          }
          return null;
        })}
      </Radio.Group>
    </View>
  );

  const renderBrief = () => {
    if (verticalFlag) {
      if (props.cardMode) {
        return data?.filter(d => d[props.keyMap.key] === props.value)?.[props.keyMap.value];
      } else {
        return radioGroup;
      }
    }
    return null;
  };

  const renderValue = () => {
    if (verticalFlag) return '';
    else if (props.cardMode) {
      return data?.filter(d => d[props.keyMap.key] === props.value)?.[0]?.[props.keyMap.value];
    } else {
      return radioGroup;
    }
  };

  return (
    <MbRow
      label={props.title}
      required={props.required}
      error={props.field?.error}
      field={props.field}
      values={props.values}
      value={props.value}
      disabled={props.disabled}
      cardMode={props.cardMode}
      hiddenRight={verticalFlag}
      brief={renderBrief()}
    >{renderValue()}</MbRow>
  );
}

export default MbRadio;
