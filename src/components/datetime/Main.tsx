import { useState, useMemo } from 'react';
import { DatetimePicker, Popup, Picker } from '@taroify/core';
import { observer } from 'mobx-react-lite';
import classname from 'classnames';
import dayjs from 'dayjs';
import { useIntl } from '@/util/intl';
import appStore from '@/store/app';
import MbRow from '@/components/mb-row';
import { addAction } from '@/util/guance';
import styles from './Datetime.module.less';
import sectionField from '../../util/sectionField';
import { DateTimeProps, ModeMap } from './DataTime';
import _ from '../../util/util';

const DATE_FIELDS = ['DateTime', 'Date', 'Time'];

const modeMap: ModeMap = {
  date: {
    type: 'date',
    fields: ['year', 'month', 'day'],
  },
  datetime: {
    type: 'date-minute',
    fields: ['year', 'month', 'day', 'hour', 'minute'],
  },
  time: {
    type: 'time',
    fields: ['hour', 'minute'],
  },
  'YYYY-MM': {
    type: 'year-month',
    fields: ['year', 'month'],
  },
};

function getFieldLimit(valueType, value, expression, fieldCode, fieldProps) {
  const { record, funcConfig, widgetType } = fieldProps;
  const dateFlag = DATE_FIELDS.includes(widgetType);

  if (valueType === 'FIXED_VALUE' && value !== undefined && value !== '') {
    if (dateFlag) {
      return value ? dayjs(value) : undefined;
    }
    return value;
  } else if (valueType === 'VARIABLE' && expression) {
    if (dateFlag) {
      if (expression) {
        const date = _.executeExpression({ expression }, record, funcConfig);
        return date instanceof Date ? dayjs(date) : undefined;
      }
    }
    return _.executeExpression({ expression }, record, funcConfig);
  } else if (valueType === 'FIELD' && fieldCode && record.fieldCode) {
    // TODO 字段code的值
    return undefined;
  }
  return undefined;
}

export default observer((props: DateTimeProps) => {
  const intl = useIntl();
  const {
    disabled,
    title,
    placeholder,
    required,
    mode, // date|time|datetime
    value: propsValue,
    format,
    field,
    values,
    onChange = () => { },
    cardMode = false,
  } = props;
  const [show, setShow] = useState(false);

  const value = useMemo(() => {
    const curValue = (mode === 'time' && propsValue) ? `2022/01/01 ${propsValue}` : propsValue;
    if (mode === 'time') return curValue ? curValue.replaceAll('-', '/') : undefined;
    else return curValue ? dayjs(curValue).format('YYYY/MM/DD HH:mm:ss') : undefined;
  }, [mode, propsValue, format]);

  const { field: { widgetConfig, widgetType } } = props;
  const { maxValueType, maxValue, maxExpression, maxFieldCode, passTimeFlag, minValueType, minValue, minExpression, minFieldCode } = widgetConfig || {};
  const funcConfig = appStore.self;
  const start = passTimeFlag ? '1970-01-01' : dayjs().toDate();
  const max = getFieldLimit(maxValueType, maxValue, maxExpression, maxFieldCode, { record: props.field, widgetType, funcConfig })?.toDate() || dayjs('2100-01-01 23:59:59').toDate();
  const min = getFieldLimit(minValueType, minValue, minExpression, minFieldCode, { record: props.field, widgetType, funcConfig })?.toDate() || dayjs('1970-01-01 00:00:00').toDate();

  const handleSelect = () => {
    // eslint-disable-next-line no-chinese/no-chinese
    addAction('选择日期组件');
    setShow(true);
  };

  const handleClear = () => {
    onChange('');
    sectionField.validator(field, undefined, values, intl);
  };

  const handleConfirm = (date: Date): void => {
    const valueFormatMap = { date: 'YYYY-MM-DD', datetime: 'YYYY-MM-DD HH:mm:00', time: 'HH:mm:ss' };
    const newValue = dayjs(date).format(valueFormatMap[mode]);
    onChange(newValue);
    sectionField.validator(field, newValue, values, intl);
  };

  function getMinMaxTime(t) {
    if (mode === 'time') {
      // return undefined;
      return dayjs(t)
        .set('year', 2022).set('month', 1).set('day', 1)
        .toDate();
    } else {
      return t;
    }
  }

  const currentYear = dayjs().year();
  const yearOptions = Array.from(
    { length: 2100 - currentYear + 1 },
    (__, index) => ({
      meaning: currentYear + index,
      value: currentYear + index,
    }),
  );

  return (
    <>
      <MbRow
        cardMode={cardMode}
        error={field.error}
        handleClear={handleClear}
        placeholder={cardMode ? '' : placeholder}
        onClick={handleSelect}
        label={title}
        disabled={disabled}
        required={required}
        value={value ? dayjs(value).format(format) : ''}
        clearButton
      />
      <Popup open={show} placement="bottom" onClose={() => setShow(false)}>
        {format === 'YYYY' ? (
          <Picker
            value={value ? new Date(value) : undefined}
            defaultValue={new Date()}
            onConfirm={(e) => { setShow(false); handleConfirm(e); }}
            onCancel={() => setShow(false)}
            // onChange={setValue}
          >
            <Picker.Toolbar>
              <Picker.Button>{intl.formatMessage({ id: 'yqc.mobile.cancel', defaultMessage: '取消' })}</Picker.Button>
              <Picker.Title>{title}</Picker.Title>
              <Picker.Button>{intl.formatMessage({ id: 'yqc.mobile.confirm', defaultMessage: '确认' })}</Picker.Button>
            </Picker.Toolbar>
            <Picker.Column>
              {yearOptions.map(option => (
                <Picker.Option value={option.value}>{option.meaning}</Picker.Option>
              ))}
            </Picker.Column>
          </Picker>
        ) : (
          <DatetimePicker
            {...(format === 'YYYY-MM'
              ? modeMap['YYYY-MM']
              : modeMap[mode || 'date'])}
            value={value ? new Date(value) : undefined}
            defaultValue={new Date()}
            className={classname({ [styles.hiddenSecond]: format === 'HH:mm' })}
            onConfirm={(e) => { setShow(false); handleConfirm(e); }}
            onCancel={() => setShow(false)}
            min={getMinMaxTime((passTimeFlag || mode === 'time') ? min : start)}
            max={getMinMaxTime(max)}
            formatter={(type, val) => {
              if (type === 'year') return `${val}${intl.formatMessage({ id: 'yqc.mobile.year', defaultMessage: '年' })}`;
              if (type === 'month') return `${val}${intl.formatMessage({ id: 'yqc.mobile.month', defaultMessage: '月' })}`;
              if (type === 'day') return `${val}${intl.formatMessage({ id: 'yqc.mobile.day', defaultMessage: '日' })}`;
              if (format.startsWith('HH')) {
                if (type === 'hour') return `${val}${intl.formatMessage({ id: 'yqc.mobile.hour', defaultMessage: '时' })}`;
                if (type === 'minute') return `${val}${intl.formatMessage({ id: 'yqc.mobile.minute', defaultMessage: '分' })}`;
                if (type === 'second') return `${val}${intl.formatMessage({ id: 'yqc.mobile.second', defaultMessage: '秒' })}`;
              }
              return val;
            }}
          >
            <DatetimePicker.Toolbar>
              <DatetimePicker.Button>{intl.formatMessage({ id: 'yqc.mobile.cancel', defaultMessage: '取消' })}</DatetimePicker.Button>
              <DatetimePicker.Title>{title}</DatetimePicker.Title>
              <DatetimePicker.Button>{intl.formatMessage({ id: 'yqc.mobile.confirm', defaultMessage: '确认' })}</DatetimePicker.Button>
            </DatetimePicker.Toolbar>
          </DatetimePicker>
        )}

      </Popup>
    </>
  );
});
