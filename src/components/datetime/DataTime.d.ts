import { DatetimePickerColumnType } from '@taroify/core/datetime-picker/datetime-picker.shared';

interface DateMap {
  type: 'date';
  fields: Array<DatetimePickerColumnType>;
}

interface DateTimeMap {
  type: 'date-minute';
  fields: Array<DatetimePickerColumnType>;
}

interface TimeMap {
  type: 'time';
  fields: Array<DatetimePickerColumnType>;
}

interface ModeMap {
  date: DateMap;
  datetime: DateTimeMap;
  time: TimeMap;
  [key: string]: any;
}

interface widgetConfig {
  maxValueType: string;
  maxValue: string;
  maxExpression: string;
  maxFieldCode: string;
  passTimeFlag: string;
  minValueType: string;
  minValue: string;
  minExpression: string;
  minFieldCode: string;
}

interface Field {
  widgetType: string;
  widgetConfig: widgetConfig;
  error: string;
}

interface DateTimeProps {
  disabled: boolean;
  title: string;
  placeholder: string;
  required: boolean;
  mode: 'date' | 'datetime' | 'time';
  value: string;
  format: string;
  field: Field;
  values: Date;
  onChange: Function;
  cardMode?: boolean;
}

export { Field, widgetConfig, DateTimeProps, ModeMap };
