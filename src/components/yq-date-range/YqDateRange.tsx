import React, { useEffect, useState, useMemo } from 'react';
import { View } from '@tarojs/components';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import { observer } from 'mobx-react-lite';
import GlobalPlace from '@/components/yq-modal/GlobalPlace';
import MbRow from '@/components/mb-row';
import { useIntl } from '@/util/intl';
import DateRangePopup from '@/components/yq-date-range/DateRangePopup';
import '../yq-calendar/index.less';

type Range = {
  start: Date | undefined
  end: Date | undefined
}
dayjs.extend(isBetween);
export interface YqDateRangeProps {
  title?: string
  value: Range
  field
  placeholder?: string
  disabled?: boolean
  required?: boolean
  cardMode?: boolean;
  onChange: (range: Range) => any
  handleClearTimeRange: () => {}
}
const YqDateRange: React.FunctionComponent<YqDateRangeProps> = function (props) {
  const { title, value, field, placeholder, required, disabled, onChange, handleClearTimeRange } = props;
  const [show, setShow] = useState(false);
  const intl = useIntl();

  const [showDate, setShowDate] = useState(true);
  const dateValue = { start: value[field.widgetConfig?.startFieldCode], end: value[field.widgetConfig?.endFieldCode] };
  // 这里,处理点击关闭按钮
  const handleClear = () => {
    onChange({ start: undefined, end: undefined });
    setShowDate(false);
    handleClearTimeRange();
  };
  const handleClick = () => {
    setShow(true);
    setShowDate(true);
  };

  useEffect(() => {
    // onChange({ start: '2022-08-04 16:00:00' });
  }, []);
  const renderValue = () => {
    if (!dateValue?.start || !showDate) return undefined;
    return (
      <View>
        <View><span style={{ opacity: 0.32 }}>{intl.formatMessage({ id: 'yqc.mobile.date.form', defaultMessage: '从' })}</span> {dayjs(dateValue?.start).format('YYYY/MM/DD HH:mm')}</View>
        <View><span style={{ opacity: 0.32 }}>{intl.formatMessage({ id: 'yqc.mobile.date.to', defaultMessage: '至' })}</span> {dayjs(dateValue?.end).format('YYYY/MM/DD HH:mm')}</View>
      </View>
    );
  };
  const popup = useMemo(() => <DateRangePopup title={title} value={value} field={field} onChange={onChange} setShow={setShow} show={show} />, [show]);
  return (
    <>
      <MbRow
        cardMode={props.cardMode}
        error={field.error}
        handleClear={() => handleClear()}
        placeholder={placeholder}
        onClick={handleClick}
        label={title}
        disabled={disabled}
        required={required}
        value={renderValue()}
        clearButton
      />
      <GlobalPlace>
        {popup}
      </GlobalPlace>
    </>
  );
};

YqDateRange.defaultProps = {
};

export default observer(YqDateRange);
