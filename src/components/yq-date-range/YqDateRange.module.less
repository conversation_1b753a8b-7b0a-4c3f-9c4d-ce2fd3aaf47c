.range {

}
.controls {
  display: flex;
  justify-content: space-between;
  padding: 17px 30px;
  border-top: 1px solid #cbd2dc;
  border-bottom: 1px solid #cbd2dc;
  .tag {
    margin-right: 20px;
    border-radius: 100px;
    font-size: 28px;
    line-height: 42px;
    padding: 9px 24px;

    background: #f6f7f9;
    &.selected {
      background: rgba(41,121,255,0.1);
      color: #2979ff;
    }
  }
}
.controlDateTime {
  display: flex;
}
.timePicker {
  :global {
    .taroify-picker-column:nth-child(3) {
      display: none;
    }
  }
}
