import React from 'react';
import { View } from '@tarojs/components';
import Icon from '@/components/yq-icon';
import styles from './DateNav.module.less';

export interface DateNavProps {
  title?: string
  monthOffset
  step
  onChange
}
const DateNav: React.FunctionComponent<DateNavProps> = function (props) {
  const { monthOffset, title, step, onChange } = props;
  function curryNavMonth(offset) {
    return () => {
      monthOffset[step] += offset;
      onChange([...monthOffset]);
    };
  }
  return (
    <View className={styles.navigator}>
      <View className={styles.navButtons}>
        <View className={styles.navButton} onClick={curryNavMonth(-12)}><Icon type="left" fill="#333333" size={40} /><Icon className={styles.offsetLeft} type="left" fill="#333333" size={40} /> </View>
        <View className={styles.navButton} onClick={curryNavMonth(-1)}><Icon type="left" fill="#333333" size={40} /> </View>
      </View>
      <View className={styles.title}>{title}</View>
      <View className={styles.navButtons}>
        <View className={styles.navButton} onClick={curryNavMonth(1)}><Icon type="right" fill="#333333" size={40} /> </View>
        <View className={styles.navButton} onClick={curryNavMonth(12)}><Icon type="right" fill="#333333" size={40} /><Icon className={styles.offsetLeft} type="right" fill="#333333" size={40} /> </View>
      </View>
    </View>
  );
};

DateNav.defaultProps = {
};

export default DateNav;
