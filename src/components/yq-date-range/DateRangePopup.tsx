import React, { useCallback, useState } from 'react';
import { View } from '@tarojs/components';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import { observer } from 'mobx-react-lite';
import { Popup, Navbar, Calendar, DatetimePicker } from '@taroify/core';
import classnames from 'classnames';
import { useIntl } from '@/util/intl';
import { addAction } from '@/util/guance';

import '../yq-calendar/index.less';
import Toggle from './Toggle';
import DateNav from './DateNav';
import styles from './YqDateRange.module.less';

type Range = {
  start: Date | undefined
  end: Date | undefined
}
dayjs.extend(isBetween);
export interface YqDateRangePopupProps {
  title?: string
  value: Range
  field
  onChange: (range: Range) => any
  show
  setShow
}
const YqDateRangePopup: React.FunctionComponent<YqDateRangePopupProps> = function (props) {
  const { title, value, field, onChange, show, setShow } = props;
  const dateValue = { start: value[field.widgetConfig?.startFieldCode], end: value[field.widgetConfig?.endFieldCode] };
  const intl = useIntl();
  const { start, end } = dateValue;
  const startValue: (Date | undefined) = start ? dayjs(start).toDate() : undefined;
  const endValue: (Date | undefined) = end ? dayjs(end).toDate() : undefined;
  const [step, setStep] = useState<0 | 1>(0);
  const [currentType, setCurrentType] = useState<'date' | 'time'>('date');
  const [monthOffset, setMonthOffset] = useState<number[]>([0, 0]);
  const currentDateMonth = [startValue, endValue][step] ? dayjs([startValue, endValue][step]) : dayjs();

  const handleCloseCalendar = () => {
    setShow(false);
    setMonthOffset([0, 0]);
  };
  const handleConfirm = () => {
    setShow(false);
    setMonthOffset([0, 0]);
  };
  const dayFormatter = useCallback((day: Calendar.DayObject & { topIcon, text, disabled }) => {
    if (!day.value) {
      return day;
    }
    const date = day.value.getDate();
    day.top = undefined;
    day.bottom = undefined;
    if (step === 0) {
      day.className = dayjs(day.value).isBetween(endValue || dayjs('2099-01-01'), dayjs('2099-01-01'), 'day', '()') ? 'disabled' : undefined;
    } else {
      day.className = dayjs(day.value).isBetween(dayjs('1970-01-01'), startValue || dayjs('1970-01-01'), 'day', '()') ? 'disabled' : undefined;
    }
    day.children = (
      <View
        className={classnames('yq-day revert', day.className, { disabled: day.disabled })}
      >
        {day.topIcon && <View className="yq-day-top">{day.topIcon}</View>}
        {date}
        <View className="yq-day-bottom">{day.text}</View>
      </View>
    );
    return day;
  }, [startValue, endValue, step, monthOffset]);

  return (
    <Popup key={field.id} className="yq-calendar yq-range" open={show} placement="bottom" onClose={handleCloseCalendar}>
      <Navbar title={title}>
        <Navbar.NavLeft icon={false} onClick={handleCloseCalendar}>{intl.formatMessage({ id: 'yqc.mobile.cancel', defaultMessage: '取消' })}</Navbar.NavLeft>
        <Navbar.NavRight onClick={handleConfirm}>{`${intl.formatMessage({ id: 'yqc.mobile.confirm', defaultMessage: '确认' })}`}</Navbar.NavRight>
      </Navbar>
      <Toggle
        step={step}
        onChange={(v) => {
          setStep(v);
          setCurrentType('date');
        }}
        value={[startValue, endValue]}
      />
      <View className={styles.controls}>
        <View className={styles.controlDateTime}>
          <View className={classnames(styles.tag, { [styles.selected]: currentType === 'date' })} onClick={() => setCurrentType('date')}>{dayjs((step ? endValue : startValue))?.format('YYYY/MM/DD')}</View>
          <View className={classnames(styles.tag, { [styles.selected]: currentType === 'time' })} onClick={() => setCurrentType('time')}>{dayjs((step ? endValue : startValue))?.format('HH:mm')}</View>
        </View>
        {currentType === 'date' ? <View
          onClick={() => {
            if (step === 0) {
              onChange({
                start: dayjs().toDate(),
                end: endValue,
              });
            } else {
              onChange({
                end: dayjs().toDate(),
                start: startValue,
              });
            }
          }}
          style={{ border: '1px solid #2979ff', color: '#2979ff', background: '#fff' }}
          className={classnames(styles.tag)}
        >{intl.formatMessage({ id: 'yqc.mobile.today', defaultMessage: '今天' })}</View> : <View />}
      </View>
      {currentType === 'date' && <>
        <DateNav step={step} onChange={setMonthOffset} title={currentDateMonth.add(monthOffset[step], 'month').format('YYYY/MM')} monthOffset={monthOffset} />
        <Calendar
          firstDayOfWeek={1}
          type="single"
          value={step ? endValue : startValue}
          formatter={dayFormatter}
          onChange={v => {
            dateValue[['start', 'end'][step]] = v;
            setMonthOffset([0, 0]);
            onChange(dateValue);
          }}
          subtitle={false}
          min={currentDateMonth.startOf('month').add(monthOffset[step], 'month').toDate()}
          max={currentDateMonth.endOf('month').add(monthOffset[step], 'month').toDate()}
        />
      </>}
      {currentType === 'time' && <DatetimePicker
        className={styles.timePicker}
        type="time"
        fields={['hour', 'minute']}
        // defaultValue={defaultValue}
        onChange={date => {
          // eslint-disable-next-line no-chinese/no-chinese
          addAction('使用日期范围组件');
          if (step === 0) {
            onChange({
              start: dayjs(startValue).set('hour', dayjs(date).get('hour')).set('minute', dayjs(date).get('minute')).toDate(),
              end: endValue,
            });
          } else {
            onChange({
              end: dayjs(end).set('hour', dayjs(date).get('hour')).set('minute', dayjs(date).get('minute')).toDate(),
              start: startValue,
            });
          }
        }}
        // min={minDate}
        // max={maxDate}
        formatter={(type, val) => {
          if (type === 'hour') {
            return `${val}${intl.formatMessage({ id: 'yqc.mobile.hour', defaultMessage: '时' })}`;
          }
          if (type === 'minute') {
            return `${val}${intl.formatMessage({ id: 'yqc.mobile.minute', defaultMessage: '分' })}`;
          }
          return val;
        }}
      />}
    </Popup>
  );
};

YqDateRangePopup.defaultProps = {
};

export default observer(YqDateRangePopup);
