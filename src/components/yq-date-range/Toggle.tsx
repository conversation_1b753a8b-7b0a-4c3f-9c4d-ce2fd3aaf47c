import React from 'react';
import dayjs from 'dayjs';
import { View } from '@tarojs/components';
import classnames from 'classnames';
import { useIntl } from '@/util/intl';
import styles from './Toggle.module.less';

export interface ToggleProps {
  label?: string[]
  value?: (Date|undefined)[]
  placeholder?: string
  step: 0|1
  onChange
}
const Toggle: React.FunctionComponent<ToggleProps> = function (props) {
  const intl = useIntl();
  const {
    step,
    onChange,
    placeholder = intl.formatMessage({ id: 'yqc.mobile.please.choose', defaultMessage: '请选择' }),
    value, label = [intl.formatMessage({ id: 'yqc.mobile.starttime', defaultMessage: '开始时间' }), intl.formatMessage({ id: 'yqc.mobile.endtime', defaultMessage: '结束时间' })],
  } = props;

  return (
    <View className={styles.wrapper}>
      <View className={styles.content}>
        {[0, 1].map(i => (
          <View onClick={() => { onChange(i); }} className={classnames(styles.item, step === i && styles.selected)}>
            <View className={styles.title}>{label[i]}</View>
            <View className={value?.[i] ? styles.date : styles.placeholder}>{value?.[i] ? dayjs(value?.[i]).format('YYYY/MM/DD HH:mm') : placeholder}</View>
          </View>
        ))}
      </View>
    </View>
  );
};

Toggle.defaultProps = {
};

export default Toggle;
