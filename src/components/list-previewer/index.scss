.list-preview {
  height: 100%;
  display: flex;
  flex-direction: column;
  &-new-buttons {
    width: 150px;
    flex: unset;
    flex-shrink: 0;
    background-color: #fff;
    font-size: 20px;
  }
  &-new-buttons-more {
    width: 60px;
    flex: unset;
    flex-shrink: 0;
    background-color: #fff;
    font-size: 20px;
  }
  &-buttons {
    box-sizing: border-box;
    width: 100%;
    height: 140px;
    margin-top: 5px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-top: 1px solid #e5e5e5;
    .taroify-popup--bottom {
      overflow: visible;
    }
    &-left {
      width: 164px;
      height: 100%;
      justify-content: center;
      flex-shrink: 0;
    }
    &-right {
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;
      position: relative;
      &-popup {
        min-width: 240px;
        position: absolute;
        bottom: 132px;
        right: 0;
        background-color: rgba(0, 0, 0, 0.5);
      }
    }
    .taroify-checkbox__icon {
      font-size: 44px;
    }
    .taroify-checkbox__label {
      font-size: 28px;
      color: #595959;
      margin-left: 16px;
    }
  }
  &-popup {
    flex: 1;
    min-height: 100px;
    display: flex;
    flex-direction: column;

    &-header {
      height: 84px;
      line-height: 84px;
      text-align: center;
      font-size: 32px;
      font-weight: 500;
      color: #262626;
      flex-shrink: 0;
      padding: 0 24px;
      word-break: keep-all;
      overflow-x: auto;
      border-bottom: 2px solid #e5e5e5;
    }
    &-list {
      max-height: calc(90vh - 216px);
      padding: 20px;
    }
    &-content {
      flex: 1;
      max-height: calc(90vh - 216px);
      height: 100px;
      overflow: auto;
      z-index: 1;
      &-description {
        border-top: 1px solid #e5e5e5;
        margin: 0 24px;
      }
    }
    &-footer {
      padding: 24px;
      display: flex;
      flex-shrink: 0;
      border-top: 2px solid #e5e5e5;
      justify-content: center;
      align-items: center;
      .taroify-button--outlined {
        margin-right: 22px;
      }
    }
  }
  &-dialog {
    //width: 540px;
    background-color: #ffffff;
    border-radius: 12px;
    .taroify-dialog__message {
      padding: 50px 30px 40px 30px;
    }
    .taroify-dialog__content--isolated {
      display: block;
      min-height: unset;
    }
    &-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      line-height: 48px;
      font-size: 32px;
      font-weight: 500;
      color: #262626;
      &-text {
        margin-top: 27px;
      }
      .yq-mb-icon > svg {
        position: unset;
      }
    }
    &-content {
      margin-top: 20px;
      //width: 480px;
      font-size: 28px;
      color: #595959;
      line-height: 38px;
    }
  }
  &-button {
    box-sizing: border-box;
    &-rest,
    &-item {
      background-color: #fff !important;
      border-radius: 8px !important;
      border: 2px solid #2979ff !important;
    }
    &-item {
      max-width: calc(100%);
      flex: 1;
      margin-right: 20px;
    }
    &-rest {
      width: 92px;
    }
    &-restitem {
      background-color: inherit !important;
      border-radius: 0 !important;
    }
  }
}

