import { View } from '@tarojs/components';
import { useIntl } from '@/util/intl';
import { observer } from 'mobx-react-lite';
import { useCallback } from 'react';
import YqButton from '@/components/yq-button';
import { MoreButtonPopup } from '@/components/yq-component-popup';
import styles from './ListButton.module.less';

export default observer(({ buttons, selectStore, handleButtonClick }: any) => {
  const intl = useIntl();
  const restButtons: Array<any> = [];
  const curButtons: Array<any> = [];
  if (buttons.length > 2) {
    curButtons.push(...buttons.slice(0, 3));
    restButtons.push(...buttons.slice(3));
  } else {
    curButtons.push(...buttons);
  }
  // 隐藏按钮的点击事件
  // function handleRestButtonClick(e) {
  //   e.stopPropagation();
  //   selectStore.setShowRestButton(!selectStore.showRestButton);
  // }

  const getButtonList = useCallback(() => {
    return restButtons.map((btn) => {
      return {
        btn,
        icon: btn.icon,
        text: btn.name,
        click: (e) => handleButtonClick(e, btn),
      };
    });
  }, [restButtons]);
  return (
    <View className="list-preview-buttons-right">
      {curButtons.map(button => {
        const { icon, name, id } = button;
        const iconProps = { type: icon, fill: '#595959', size: 48 };
        return (
          <YqButton
            structure="tb"
            fontColor="#12274d"
            className="list-preview-new-buttons"
            key={id}
            loading={selectStore.loading}
            iconProps={iconProps}
            name={name}
            onClick={(e) => handleButtonClick(e, button)}
          />
        );
      })}
      {restButtons.length > 0 && (
        <View className={styles.moreButton}>
          <YqButton
            className="list-preview-new-buttons"
            iconProps={{ type: 'More', fill: '#595959', size: 48 }}
            structure="tb"
            fontColor="#12274d"
            // onClick={handleRestButtonClick}
            name={intl.formatMessage({ id: 'yqc.mobile.more', defaultMessage: '更多' })}
          />
          <MoreButtonPopup actions={getButtonList()}>
            <View className={styles.fakeButton} />
          </MoreButtonPopup>
        </View>
      )}
    </View>
  );
});
