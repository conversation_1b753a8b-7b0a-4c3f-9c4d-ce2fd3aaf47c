import Taro from '@tarojs/taro';
import { View } from '@tarojs/components';
import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { observer, useLocalObservable } from 'mobx-react-lite';
import get from 'lodash/get';
import { Checkbox } from '@taroify/core';
import { toJS } from 'mobx';
import queryString from 'query-string';
import omit from 'lodash/omit';
import { useIntl } from '@/util/intl';
import useOpenPress from '@/components/press-botton';
import { DialogPopup } from '@/components/yq-component-popup';
import appStore from '@/store/app';
import { addAction } from '@/util/guance';
import YqModal from '@/components/yq-modal';
import PageLoader from '@/components/page-loader';
import { BaseWidgetConfig } from '@/types/PageJson';
import YqList from '@/components/yq-list';
import { fetch, _, sectionField } from '@/util';
import BatchAction from '../batch-action';
import './index.scss';
import ListButton from './list-buttons';

interface ListPreviewerProps {
  store?: any
  tableFieldRecord?: any
  formValues?: any
  clickLoader?: any
  listViewPrefix?: boolean
  innerList?: boolean
  fromTab?: boolean
  pcFlag?: boolean
  hiddenHeader?: boolean
  disabledRefresh?: boolean
  maxLength?: number
  emptyTips?: string | React.ReactNode
  listStyle?: React.CSSProperties
  style?: any
  ticketId?: string
  variableFlag?: boolean
}

const ListPreviewer: React.FC<ListPreviewerProps> = observer((props) => {
  const {
    store,
    tableFieldRecord: _tableFieldRecord,
    formValues,
    clickLoader,
    listViewPrefix,
    innerList = false,
    fromTab = false,
    pcFlag = false,
    hiddenHeader,
    disabledRefresh,
    maxLength,
    emptyTips,
    listStyle = {},
    style = {},
    ticketId,
    variableFlag,
  } = props;
  const intl = useIntl();
  const params = useMemo(() => Taro.getCurrentInstance()?.router?.params, []);
  const [selectAll, setSelectAll] = useState(false);
  const [open, setOpen] = useState(false);
  const [openFeedback, setOpenFeedback] = useState(false);
  const [refreshId, refresh] = useState(_.uuid());
  const [cfm, setCfm] = useState({ open: false, title: '', content: '', okText: '', cancelText: '', btn: null, listItem: null });
  const supportTenantId = params?.supportTenantId;

  // 全部、中处理、... 这样的筛选器
  const tableFilters = get(store, 'data.filters', []);
  const businessObjectId = get(store, 'businessObjectId', '');
  const businessObjectCode = get(store, 'businessObjectCode', '');
  const tenantId = get(appStore, 'tenantId', '');
  // 列表视图 section 唯一，section 下的 fields 唯一
  const tableFieldRecord = _tableFieldRecord || store.jsonData.sections[0].fields[0];
  const widgetConfig = tableFieldRecord ? get(tableFieldRecord, 'widgetConfig', { fields: [], filters: [] }) : get(store, 'jsonData.sections[0].fields[0].widgetConfig', { fields: [], filters: [] });
  const { fields, filters: customFilters, tableLinkFlag, viewId: _viewId, pageSize, relatedFieldCode, multipleable = true, lineButtons: _lineButtons, tableLinks, showRelativeBtn, subTaskH5Flag, subTaskBusinessObjectCode, modelId, subTaskBusinessObjectId, selectMultipleObject } = widgetConfig;

  const fuzzyRecords = customFilters?.filter(field => {
    if (!field.filterFlag) {
      return false;
    }
    // 兼容历史数据：模糊标识未设置并且在可模糊搜索类型中，也属于模糊搜索字段
    if (field.fuzzyFlag || (field.fuzzyFlag === undefined)) {
      return true;
    }
    return false;
  });
  const basicRecords = customFilters?.filter(field => {
    if (!field.filterFlag) {
      return false;
    }
    // quickFlag未设置也认为是基本搜索
    return field.quickFlag || (field.quickFlag === undefined && !field.fuzzyFlag);
  });
  const advancedRecords = customFilters?.filter(field => {
    if (!field.filterFlag) {
      return false;
    }
    return field.quickFlag === false;
  });

  const multipleFlag = widgetConfig.multipleFlag;
  const viewId = _viewId || store.viewId;
  const filterParams = relatedFieldCode ? {
    [`search_${relatedFieldCode}`]: formValues?.id,
    __condition: [],
    __page_params: {
      __parent_id: params?.ticketId,
    },
  } : {};
  const searchable = !!(customFilters?.length || tableFilters?.length);

  // const basicFilters = customFilters.filter(filter => filter.basicFlag); // 基础搜索字段
  // const fuzzyFilters = customFilters.filter(filter => filter.fuzzyFlag); // 模糊搜索字段
  // const advancedFilters = customFilters.filter(filter => filter.advancedFlag); // 高级搜索字段

  const datasets = get(store.jsonData, 'datasets', []);
  const datasetId = datasets.find(dataset => dataset?.id === tableFieldRecord?.id)?.id || datasets.find(dataset => dataset.tag === 'Table')?.id;
  const lineDataset = datasets.find(dataset => dataset?.id === tableFieldRecord?.id) || datasets.find(dataset => dataset.tag === 'Table');
  let url = store.data.id && datasetId ? `lc/v1/engine/${appStore.tenantId}/dataset/${store.data.id}/${datasetId}/query` : '';
  let lineSubmitUrl = store.data.id && datasetId ? `lc/v1/engine/${appStore.tenantId}/dataset/${store.data.id}/${datasetId}/submit` : '';
  // 变量视图头行数据查询url
  if (variableFlag && innerList && store?.requestItemConfig?.instanceId) {
    url = lineDataset?.businessObjectId ? `lc/v1/engine/${appStore.tenantId}/dataset/variable/${lineDataset?.businessObjectId}/query?field=${relatedFieldCode}&value=${store.requestItemConfig.instanceId}` : '';
    lineSubmitUrl = store.data.id && datasetId ? `lc/v1/engine/${appStore.tenantId}/dataset/variable/${lineDataset?.businessObjectId}/submit` : '';
  }
  if (url && supportTenantId) {
    url = `/itsm/v1/support_center/engine/${appStore.tenantId}/dataset/${store.data.id}/${datasetId}/query`;
  }
  // 查询自定义列表的状态和优先级集合
  const notFromDesigner = appStore.fromDesigner !== 'true';
  const funConfig = { person: appStore.self.person, personId: appStore.self.personId };
  const buttons = getButtons();

  const showSelectAll = (buttons.length > 0 && buttons.some(item => item.module === 'MobileTicketBatchProcess')) || multipleFlag;

  // const params = useMemo(() => Taro.getCurrentInstance()?.router?.params, []);
  const path = useMemo(() => Taro.getCurrentInstance()?.router?.path, []);
  const isTicketDetail = useMemo(() => path && path.includes('/pages/task-detail/index') && innerList, [path, innerList]);
  const isModulesPage = useMemo(() => path && path.includes('/pages/modules/index'), [path]);
  const isCreateOrderPage = useMemo(() => path && path.includes('/pages/create-order/index'), [path]);

  const selectStore = useLocalObservable(() => ({
    currentChildrenData: {} as any,
    enabledBatch: params?.hiddenFooter,
    loading: false,
    submitLoading: false,
    businessObject: [] as any,
    actions: [] as any,
    curAction: {} as any,
    restFields: [] as any,
    selectedList: [] as any,
    formFieldValues: {} as any,
    failedTickets: [] as any,
    numbers: [] as any,
    showRestButton: false,
    isSubmit: false,
    filterBubbles: [] as any,
    filterNumbers: [] as any,
    get viewType() {
      return store.data.viewType;
    },
    setShowRestButton: (flag: boolean) => {
      selectStore.showRestButton = flag;
    },
    setSelectedList: (arr: Array<any>) => {
      selectStore.selectedList = arr;
    },
    fetch: async (_businessObjectId: string, _tenantId: string) => {
      selectStore.loading = true;
      const actionsUrl = `lc/v1/${_tenantId}/lc_actions/list/${_businessObjectId}`;
      const objUrl = `lc/v1/${_tenantId}/object_fields/all/${_businessObjectId}`;
      await Promise.all([fetch(actionsUrl), fetch(objUrl)]).then((res) => {
        const [actionsRes, objRes] = res;
        selectStore.actions = Array.isArray(actionsRes) ? actionsRes : [];
        selectStore.businessObject = Array.isArray(objRes) ? objRes : [];
      }).catch((e) => {
        Taro.atMessage({
          type: 'error',
          message: e || intl.formatMessage({ id: 'yqc.mobile.api.wrong', defaultMessage: '接口出现报错，请联系管理员' }),
        });
      }).finally(() => {
        selectStore.loading = false;
      });
    },
    // eslint-disable-next-line no-shadow,@typescript-eslint/no-shadow
    submit: async (_tenantId, _businessObjectId, _viewId, params) => {
      const submitUrl = `lc/v1/${_tenantId}/lc_actions/${_businessObjectId}/${_viewId}/batch`;
      selectStore.submitLoading = true;
      const failedTickets = await fetch(submitUrl, params, 'post').catch(() => {});
      selectStore.submitLoading = false;
      selectStore.isSubmit = true;
      if (failedTickets) {
        selectStore.failedTickets = Array.isArray(failedTickets) ? failedTickets : [];
      }
    },
    submitInline: async (tableId, data) => {
      const submitUrl = `lc/v1/engine/${tenantId}/dataset/${viewId}/${tableId}/submit`;
      selectStore.submitLoading = true;
      const failedTickets = await fetch(submitUrl, [data], 'post').catch(() => {});
      selectStore.submitLoading = false;
      selectStore.isSubmit = true;
      if (failedTickets) {
        selectStore.failedTickets = Array.isArray(failedTickets) ? failedTickets : [];
      }
    },
    fetchBubbles: async (_url: string, _fields, filterId: string = '') => {
      const data = await fetch(`${_url}${filterId ? `?filter_id=${filterId}` : ''}`, {}, 'post').catch(() => {});
      let statusList: any[] = [];
      let priorityList: any[] = [];
      if (data && !data.failed) {
        const { state, priority } = data;
        statusList = Array.isArray(state) ? state : [];
        priorityList = Array.isArray(priority) ? priority : [];
      }
      const basicFilters = pcFlag ? basicRecords : customFilters?.filter?.(filter => filter.basicFlag); // 基本搜索字段
      basicFilters.forEach(filter => {
        // 基础搜索特殊处理，当有状态和优先级的多对一字段时，将其平铺展示
        const bubble = { name: filter.name, code: filter.code, isField: true, field: filter };
        if (filter.code === 'state_id') {
          Object.assign(bubble, { options: statusList, isField: false });
        }
        if (filter.code === 'priority_id') {
          Object.assign(bubble, { options: priorityList, isField: false });
        }
        if (filterId) {
          const filterBubble = selectStore.filterBubbles.find(b => b.code === filter.code);
          Object.assign(filterBubble, bubble);
        } else {
          selectStore.filterBubbles.push(bubble);
        }
      });
    },
    fetchNumbers: async (searchParams: string) => {
      // 是否展示筛选器数量
      if (widgetConfig?.showFilterNumber) {
        const data = await fetch(`itsm/v1/${appStore.tenantId}/work_bench/filter/number?viewId=${store.viewId || viewId}${searchParams ? `&${ searchParams}` : ''}`).catch(() => {});
        const filterIndexMap = new Map<string, number>(tableFilters.map((item, index) => [item?.id, index]));
        selectStore.filterNumbers = data?.sort((a: any, b: any) => {
          const aIndex = filterIndexMap.get(a?.id);
          const bIndex = filterIndexMap.get(b?.id);
          return (aIndex !== undefined && bIndex !== undefined) ? (aIndex - bIndex) : 0;
        }).map(r => r.totalCount);
      }
    },
  }));

  const viewType = selectStore.viewType;

  useEffect(() => {
    // 反馈列表不要查询
    if (appStore.tenantId && !params?.supportTenantId) {
      const bubbleUrl = datasetId && store.viewId && `itsm/v1/${appStore.tenantId}/work_bench/${store.viewId}/${datasetId}/collect`;
      // const numberUrl = datasetId && store.viewId && `itsm/v1/${appStore.tenantId}/work_bench/${store.viewId}/${datasetId}/collect`;
      selectStore.fetchBubbles(bubbleUrl, fields);
      // selectStore.fetchNumbers();
      if (selectStore.filterNumbers?.length === 0) {
        selectStore.filterNumbers = [];
      }
    }
  }, [store.viewId]);

  function getButtons() {
    const _buttons: any[] = widgetConfig.buttons || [];
    return _buttons.filter(btn => !_.calculateHidden(btn, formValues || {}, funConfig));
  }

  const getLineButtons = useCallback(() => {
    // 把_buttons经过两次处理后，开始渲染
    let _buttons: any[] = _lineButtons || [];
    // console.log('cbjtest', _buttons);
    // 第一次：给每行的催办按钮，根据reminder_flag，加一个isDisplay属性，决定其是否显示
    _buttons = _buttons.map((oneButton) => {
      const newButton = { ...oneButton };
      if (newButton.tag === 'PressTicket') { // 控制行内按钮显隐的逻辑在这里
        newButton.isDisplay = (item) => { // 专门控制催办按钮显隐的函数
          return item.reminder_flag;
          // return true // 为了debug时能看到这个按钮留下的
        };
      }
      return newButton;
    });
    // 第二次
    _buttons = _buttons.filter(btn => !_.calculateHidden(btn, formValues || {}, funConfig));

    return _buttons;
  }, [funConfig, formValues]);

  const lineButtons = getLineButtons();

  // 列表刷新得到数据后
  function handleRefresh() {
    if (selectStore.isSubmit) {
      selectStore.isSubmit = false;
      return;
    }
    selectStore.failedTickets = [];
  }

  function openInlineEditor(data, buttonTableWidgetConfig, button) {
    const filterInlineFields = !widgetConfig?.inlineFields ? (_field) => true : (field) => {
      const inlineFieldSet = new Set();
      try {
        JSON.parse(widgetConfig?.inlineFields).forEach(f => inlineFieldSet.add(f.code));
        if (inlineFieldSet.has(field.code)) {
          return true;
        }
        return false;
      } catch (e) {
        console.error(e);
      }
    };
    const simpleJsonData = {
      sections: [{
        xPosition: 0,
        name: 'section',
        tag: 'Section',
        id: '1',
        fields: fields.filter(filterInlineFields).map(v => ({ ...v, tag: 'Field', widgetConfig: { ...v.widgetConfig, ...commonConfig } })),
      }],
    };
    let editorJsonData = simpleJsonData;

    const commonConfig: BaseWidgetConfig = {
      visibleType: 'ALWAYS_VISIBLE',
      readonlyType: 'ALWAYS_NOT_EDIT',
      readonlyAction: 'READONLY',
      readonlyCondition: [],
      defaultValueType: 'FIXED_VALUE',
      visibleAction: 'SHOW',
      visibleCondition: [],
      editAction: 'EDITABLE',
      editCondition: [],
      requiredAction: 'REQUIRED',
      requiredCondition: [],
    };
    selectStore.currentChildrenData = data;
    const isInline = button.type !== 'CREATE';
    const pageLoaderProps = isInline ? {
      isStatic: true,
      jsonData: simpleJsonData,
    } : {
      viewId: button.viewId,
      setFormJsonData: jsonData => { editorJsonData = jsonData; },
    };
    const handleModalOk = async () => {
      if (selectStore.currentChildrenData._status === 'update') {
        // console.log('cbjtest handleModalOk', widgetConfig.modelId, widgetConfig, selectStore, data, button);
        await selectStore.submitInline(button.parentId, selectStore.currentChildrenData);
        modal.close();
        refresh(_.uuid());
        return;
      }
      if (!formValues._children) {
        formValues._children = [];
      }
      let currentChildrenData = formValues._children.find(r => r.relatedFieldCode === widgetConfig.relatedFieldCode);
      if (!currentChildrenData) {
        formValues._children.push({ relatedFieldCode: widgetConfig.relatedFieldCode, data: [], businessObjectId: widgetConfig.modelId });
        currentChildrenData = formValues._children.find(r => r.relatedFieldCode === widgetConfig.relatedFieldCode);
      }
      // formValues = [...store.formValue._children, selectStore.currentChildrenData];
      if (
        !sectionField.validatorAll(
          editorJsonData,
          selectStore.currentChildrenData,
          intl,
        )?.isPassed
      ) {
        return;
      }
      // 详情中的头行和pc一样调接口新建，不再存_children
      if (innerList && ticketId) {
        const result = await fetch(lineSubmitUrl, [{ ...selectStore.currentChildrenData, [relatedFieldCode]: ticketId, _parentId: ticketId }], 'POST');
        refresh(_.uuid());
      } else {
        currentChildrenData.data.push(selectStore.currentChildrenData);
      }
      modal.close();
    };
    const handleModalCancel = () => {
      modal.close();
    };
    const modal = YqModal.open({
      title: button.viewName || ' ',
      autoFull: true,
      onOk: isInline ? handleModalOk : undefined,
      onCancel: isInline ? handleModalCancel : undefined,
      wrapperClassName: 'flex-column-popup',
      children: (
        <PageLoader
          // isStatic
          modalClose={handleModalCancel}
          onModalOk={handleModalOk}
          formFieldValues={selectStore.currentChildrenData}
          {...pageLoaderProps}
          needCalculate

        />
      ),
    });
  }

  // 列表每一项的点击事件
  function handleClick(item, buttonTableWidgetConfig, btn) {
    // TODO:
    if (viewType === 'INSERT') {
      // TODO: 行内编辑
      openInlineEditor(item, buttonTableWidgetConfig, btn);
    } else {
      let linkViewId = viewId;
      if (tableLinks && tableLinkFlag && notFromDesigner) {
        const conditionLink = tableLinks.find((tableLink) => {
          return _.calculateCondition(undefined, tableLink.condition, item);
        });
        linkViewId = conditionLink?.viewId || viewId;
      }
      const linkId = item[widgetConfig?.tableLinkFieldCode || 'id'];
      if (linkId && tableLinkFlag && linkViewId && notFromDesigner) {
        _.navigateTo({
          url: `/pages/task-detail/index?viewId=${linkViewId}&ticketId=${linkId}${supportTenantId ? `&supportTenantId=${supportTenantId}` : ''}`,
        });
      }
    }
  }
  // 按钮或自定义按钮的点击事件
  async function handleButtonClick(_e, button, buttonTableWidgetConfig) {
    // e.stopPropagation();
    selectStore.setShowRestButton(false);
    if (button.type === 'INLINECREATE' || (button.type === 'CREATE' && (isCreateOrderPage || store?.data?.viewType === 'INSERT'))) {
      // 行内新增
      openInlineEditor({
        _status: 'create',
      }, buttonTableWidgetConfig, button);
      return;
    }
    if (button.tag === 'CustomButton' && button.module === 'MobileTicketBatchProcess') { // 批量处理
      if (selectStore.selectedList.length === 0) {
        Taro.atMessage({
          type: 'warning',
          message: intl.formatMessage({ id: 'yqc.mobile.select.one.ticket', defaultMessage: '至少选中一个单据' }),
        });
        return;
      } else {
        await selectStore.fetch(businessObjectId, tenantId);
        setOpen(true);
      }
    }
    if (button.type === 'EXPRESSION') {
      try {
        const result = await fetch(`lc/v1/engine/${tenantId}/dataset/${store.data?.id}/executeButton/${button.id}`, {
          ...selectStore.selectedList?.[0],
          __selected_ids: selectStore.selectedList.map(v => v.id),
        }, 'POST');
        if (!result?.failed) {
          Taro.atMessage({
            type: 'success',
            message: intl.formatMessage({ id: 'yqc.mobile.submit.success', defaultMessage: '提交成功' }),
          });
        }
        Taro.eventCenter.once('refreshList', () => {
          refresh(_.uuid());
        });
      } catch (err) {
        // eslint-disable-next-line no-console
        console.error('表达式执行', err);
      } finally {
        refresh(_.uuid());
      }
    } else if (button.type === 'REFRESH') {
      refresh(_.uuid());
    }
    if (button.action === 'OPEN_VIEW') {
      _.navigateTo({
        url: `/packageOther/pages/task-detail/index?viewId=${button.viewId}&parentId=${formValues?.id}&parentParams=${encodeURIComponent(JSON.stringify({
          [tableFieldRecord?.widgetConfig?.relatedFieldCode]: formValues?.id,
        }))}`,
      });
      Taro.eventCenter.once('refreshList', () => {
        refresh(_.uuid());
      });
    }
  }
  // 全选/不全选的勾选框事件
  function handleSelectChange(checked) {
    setSelectAll(checked);
  }
  // 校验批量操作的字段是否为空
  function validate() {
    const { curAction, formFieldValues, restFields } = selectStore;
    if (!curAction.id) {
      Taro.atMessage({
        type: 'warning',
        message: intl.formatMessage({ id: 'yqc.mobile.select.your.actions', defaultMessage: '请选择要执行的操作' }),
      });
      return false;
    }
    const requiredList: Array<any> = [];
    const showField = _.isJSON(curAction.windowShowField) ? JSON.parse(curAction.windowShowField) : [];
    if (showField.length > 0) {
      showField.forEach(field => {
        if (field.required && ['', undefined, null].includes(formFieldValues[field.field])) {
          const { name } = restFields.find(item => item.code === field.field) || {}; // 一般情况下一定会有
          requiredList.push(name);
        }
      });
    }
    if (requiredList.length > 0) {
      Taro.atMessage({
        type: 'warning',
        message: `${intl.formatMessage({ id: 'yqc.mobile.please.input', defaultMessage: '请输入' })}${requiredList.join(' , ')}`,
      });
      return false;
    }
    return true;
  }
  // 批量操作的提交事件
  async function handleConfirm() {
    // eslint-disable-next-line no-chinese/no-chinese
    addAction('批量操作');
    // 先校验是否有必填项没填
    if (!validate()) return;
    const { curAction, formFieldValues } = selectStore;
    // eslint-disable-next-line no-shadow
    const params: Array<object> = [];
    // 一定有数据，否则不会进来
    Object.keys(formFieldValues).forEach(key => {
      if (typeof formFieldValues[key] === 'object' && !Array.isArray(formFieldValues[key])) {
        formFieldValues[key] = formFieldValues[key].id || formFieldValues[key].code;
      }
    });
    selectStore.selectedList.forEach((selected: any) => {
      const { number: code, id, object_version_number: objectVersionNumber } = selected;
      const param = { code, id, object_version_number: objectVersionNumber, _action_id: curAction.id, _status: 'update' };
      Object.assign(param, formFieldValues);
      params.push(param);
    });

    await selectStore.submit(tenantId, businessObjectId, viewId, params);
    const all = selectStore.selectedList.length;
    const success = selectStore.selectedList.length - selectStore.failedTickets.length;
    const failed = selectStore.failedTickets.length;
    selectStore.numbers.push(...[all, success, failed]);
    // selectStore.firstSelectedItem = {};
    selectStore.selectedList = [];
    handleClear();
    setOpen(false);
    setOpenFeedback(true);
    setSelectAll(false);
    refresh(_.uuid());
  }
  // 清除批量操作内部存储的数据
  function handleClear() {
    selectStore.curAction = {};
    selectStore.restFields = [];
    selectStore.formFieldValues = {};
    // selectStore.failedTickets = [];
  }
  // 取消批量操作
  function handleCancel() {
    setOpen(false);
    handleClear();
  }
  // 选择某一个操作的变更事件
  function handleActionsChange(value) {
    Object.assign(selectStore.curAction, value);
    const showField = _.isJSON(value.windowShowField) ? JSON.parse(value.windowShowField) : [];
    const restFields: Array<any> = [];
    if (selectStore.businessObject.length > 0) {
      selectStore.businessObject.forEach((obj: any) => {
        showField.forEach(field => {
          if (field.field === obj.code) {
            Object.assign(obj, { required: field.required });
            restFields.push(obj);
          }
        });
      });
    }
    selectStore.restFields = restFields;
    selectStore.formFieldValues = {};
  }
  // 批量操作的关联字段值变更事件
  function handleRestFieldChange(value, name) {
    selectStore.formFieldValues[name] = value;
  }
  // 关闭提交完成后feedback事件
  function onCloseFeedback() {
    selectStore.numbers = [];
  }

  const handleOpenPress = useOpenPress();
  // 行操作按钮api调用
  async function rowBtnDataPost(btn, listItem) {
    if (btn?.tag === 'PressTicket') { // 催办按钮
      handleOpenPress(listItem.source_table || store.businessObjectCode, listItem.id);
    } else if (btn?.type === 'EXPRESSION' && btn?.lineButton) {
      // TODO 目前仅支持表格行上的表达式按钮
      await fetch(`lc/v1/engine/${tenantId}/dataset/${viewId}/executeButton/${btn.id}`, listItem, 'post').catch(() => {});
      refresh(_.uuid());
    } else if (btn?.type === 'INLINEEDIT') {
      // console.log('cbjtest rowBtnDataPost', listItem, formValues);

      openInlineEditor({
        ...listItem,
        _status: 'update',
      }, undefined, btn);
    } else {
      await store.submitData(btn.type, listItem);
    }
  }

  async function handleRowButtonClick(_e, btn, listItem) {
    if (btn?.confirmFlag) {
      setCfm({
        open: true,
        title: btn.name,
        content: btn.confirmText,
        okText: btn.okText,
        cancelText: btn.cancelText,
        btn,
        listItem,
      });
    } else {
      await rowBtnDataPost(btn, listItem);
    }
  }

  function handleTabClick(id: string) {
    if (id !== 'draft' && !params?.supportTenantId) {
      const bubbleUrl = datasetId && store.viewId && `itsm/v1/${appStore.tenantId}/work_bench/${store.viewId}/${datasetId}/collect`;
      selectStore.fetchBubbles(bubbleUrl, fields, id);
    }
    // TODO：小程序中处理
    if (process.env.TARO_ENV === 'h5') {
      const searchParams = queryString.parse(window.location.href.split('?')[1]);
      window.history.replaceState(null, '', `${window.location.href.split('?')[0]}?${queryString.stringify(omit(searchParams, 'filterId'))}`);
    }
  }
  const advancedFilters = pcFlag ? advancedRecords.map(r => ({ ...r, code: `search_${r.code}` })) : customFilters?.filter(filter => filter.advancedFlag);
  return (
    // <View style={{ height: fromDesigner ? 'calc(100% - 100px)' : 'calc(100% - 50px)' }}>
    <View className="list-preview" onClick={() => selectStore.setShowRestButton(false)} style={{ ...style }}>
      <YqList
        pageSize={maxLength || pageSize || 20}
        forceShowEmptyTips={maxLength}
        emptyTips={emptyTips}
        disabledRefresh={disabledRefresh || !!_tableFieldRecord}
        pcFlag={pcFlag}
        fromTab={fromTab}
        fixFilterFlag={!hiddenHeader}
        refreshKey={refreshId}
        identify="CUSTOMVIEW"
        emptyStyle={listStyle}
        // 当为新建视图的时候不查询数据，（单据详情里的服务项视图组件需要查询）
        url={(viewType === 'INSERT' || (!ticketId && innerList)) ? null : url}
        formValues={formValues}
        queryMethod="post"
        showCheckbox={showSelectAll}
        selectAll={selectAll}
        selectedList={selectStore.selectedList}
        setSelectedList={selectStore.setSelectedList}
        failedList={selectStore.failedTickets}
        afterGetList={handleRefresh}
        canSearch={searchable && !hiddenHeader && fuzzyRecords.length > 0}
        searchFieldName="fuzzy_params_"
        hasFilter={searchable}
        filterData={tableFilters}
        customFilters={advancedFilters}
        swipe={!!lineButtons?.length}
        rightSwipes={lineButtons?.length ? lineButtons : []}
        rightSwipesType={lineButtons?.length ? 'lineButton' : ''}
        filterParams={filterParams}
        disablePullRefresh={disabledRefresh}
        clickLoader={clickLoader}
        filterBubbles={selectStore.filterBubbles}
        fields={fields}
        onClick={handleClick}
        fixButtonsFlag={!!isTicketDetail}
        showRelativeBtn={showRelativeBtn} // 右下角的显示关联单据的小按钮
        fixTopButtonFlag={!isModulesPage} // 右上角的按钮只有在单据详情或工单页才有可能出现, 在modules页面不出现
        buttons={isTicketDetail && !store?.requestItemConfig?.instanceId ? buttons : []}// 变量视图头行表格不让操作
        listButtons={isTicketDetail ? [] : buttons}
        selectStore={selectStore}
        handleButtonsClick={handleButtonClick}
        handleRowButtonClick={handleRowButtonClick}
        handleTabClick={handleTabClick}
        hasCustomPrefix={listViewPrefix}
        pageStore={store}
        showIcon
        subTaskH5Flag={subTaskH5Flag}
        subTaskBusinessObjectCode={subTaskBusinessObjectCode}
        selectMultipleObject={selectMultipleObject}
        subTaskBusinessObjectId={subTaskBusinessObjectId}
        modelId={modelId}
        businessObjectCode={businessObjectCode}
        widgetConfig={widgetConfig}
        rowData={formValues?._children && formValues?._children?.find(r => r?.relatedFieldCode === widgetConfig?.relatedFieldCode)?.data}
        ticketId={ticketId}
      />
      {multipleable && buttons?.length > 0 && selectStore.enabledBatch && (
        <View className="list-preview-buttons">
          {showSelectAll && (
            <Checkbox className="list-preview-buttons-left" checked={selectAll} onChange={handleSelectChange}>
              {intl.formatMessage({ id: 'yqc.mobile.select.all', defaultMessage: '全选' })}
            </Checkbox>
          )}
          {!isTicketDetail && <ListButton buttons={buttons} handleButtonClick={handleButtonClick} selectStore={selectStore} />}
          <BatchAction
            open={open}
            setOpen={setOpen}
            handleCancel={handleCancel}
            handleConfirm={handleConfirm}
            selectStore={selectStore}
            handleActionsChange={handleActionsChange}
            handleRestFieldChange={handleRestFieldChange}
            openFeedback={openFeedback}
            onCloseFeedback={onCloseFeedback}
            setOpenFeedback={setOpenFeedback}
          />
        </View>

      )}
      <DialogPopup
        open={cfm.open}
        handleClickOutside={() => setCfm({ ...cfm, open: true })}
        header={cfm.title}
        type="noImage"
        description={cfm.content}
        actions={[
          {
            click: () => setCfm({ ...cfm, open: false }),
            text: cfm.cancelText,
          },
          {
            click: () => { setCfm({ ...cfm, open: false }); rowBtnDataPost(cfm.btn, cfm.listItem); },
            text: cfm.okText,
          },
        ] as any}
      />
    </View>
  );
});

export default ListPreviewer;
