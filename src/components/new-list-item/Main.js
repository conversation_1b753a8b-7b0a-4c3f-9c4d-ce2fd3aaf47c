import { useEffect } from 'react';
import { Cell } from '@taroify/core';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { injectIntl } from '@/util/intl';
import { _, constants } from '@/util';
import './index.scss';
import YqStatus from '../yq-status';

dayjs.extend(relativeTime);

const surveyState = constants.SURVEY_STATE;

function NewListItem(props) {
  const { identify } = props;

  const handleClickItem = () => {
    switch (identify) {
      case 'REQUEST':
        _.navigateTo({
          url: `/pages/event-detail/index?itemId=${props.id}&ticketId=${props.sourceId || props.ticketId || props.wfInstanceId}`,
        });
        break;
      case 'TASK':
        _.navigateTo({
          url: `/pages/task-detail/index?ticketId=${props.sourceId || props.id}&viewCode=${_.getTicketViewMapping(props.sourceTable || props.tableName, props.number)}`,
        });
        break;
      case 'SURVEY':
        _.navigateTo({
          url: `/pages/survey-detail/index?instanceId=${props.id}`,
        });
        break;
      default:
        break;
    }
  };

  const getAllMessage = () => {
    const { creationDate } = props;
    const { ticketObjectsList } = props;
    let stateName = ticketObjectsList ? ticketObjectsList['state_id:name'] : props.stateName;
    let stateColor = ticketObjectsList ? ticketObjectsList.state_color : props.stateColor;
    let shortDescription = ticketObjectsList ? ticketObjectsList.short_description : props.shortDescription;
    let number = props.ticketNumber || props.number;
    const priorityName = ticketObjectsList ? ticketObjectsList.priority_name : props.priorityName;
    const scItemName = props.scItemName;
    // 若是移动调查，则显示的不一样
    if (identify === 'SURVEY') {
      shortDescription = props.surveyName;
      number = props.number;
      ({ stateName, stateColor } = surveyState[props.state]);
    }
    return {
      stateColor,
      stateName,
      creationDate,
      shortDescription,
      number,
      priorityName,
      scItemName,
      submittedName: props.submittedName,
    };
  };

  const {
    shortDescription,
    stateName,
    stateColor,
    number,
    creationDate,
    priorityName,
    scItemName,
    submittedName,
  } = getAllMessage();

  return (
    <Cell
      className="new-listitem-cell"
      title={shortDescription}
      brief={
        <>
          <View>{scItemName}</View>
          <View>{number}</View>
          <View>{priorityName}</View>
        </>
      }
      onClick={handleClickItem}
    >
      <YqStatus color={stateColor}>
        {stateName}
      </YqStatus>
      <View className="new-listitem-date">{dayjs(creationDate).fromNow()}</View>
      <View className="new-listitem-submitname">{submittedName}</View>
    </Cell>
  );
}

export default injectIntl(NewListItem);
