import React from 'react';
import { View, Image } from '@tarojs/components';
import { site } from '@config';
import { _ } from '@/util';
import YqIcon from '@/components/yq-icon';
import appStore from '@/store/app';
import './index.scss';

interface IconImageRendererProps {
  type: 'icon' | 'image'
  icon: string
  fileKey: string
  backgroundColor: string
  round?: boolean // 优先级比borderRadius高
  iconSize?: number
  theme?: 'outline' | 'filled'
  wrapperSize?: number
  borderRadius?: number
  iconFill?: string
  className?: string
  noStyle?: boolean
}

const IconImageRenderer: React.FC<IconImageRendererProps> = function IconImageRenderer({
  type,
  icon,
  fileKey,
  backgroundColor,
  iconSize,
  theme = 'outline',
  wrapperSize,
  borderRadius,
  iconFill = '#fff',
  round = false,
  className = '',
  noStyle = false,
}) {
  const wrapperStyle = `
    background-color: ${backgroundColor || '#2979ff'};
    width: ${_.pxToRem((wrapperSize || 40) * 2)};
    height: ${_.pxToRem((wrapperSize || 40) * 2)};
    border-radius: ${round ? '50%' : _.pxToRem((borderRadius || 4) * 2)};
  `;

  if (type === 'icon') {
    return icon ? (
      <View className={`yq-icon-image-wrapper ${className}`} style={wrapperStyle}>
        <YqIcon type={icon} size={iconSize || 40} fill={iconFill} theme={theme} />
      </View>
    ) : null;
  }

  const imageStyle = `
    width: ${_.pxToRem((wrapperSize || 40) * 2)};
    height: ${_.pxToRem((wrapperSize || 40) * 2)};
    border-radius: ${round ? '50%' : (borderRadius || 4)}px;
  `;

  if (fileKey) {
    const src = fileKey?.startsWith('http') ? fileKey : `${site}hfle/yqc/v1/0/files/download-by-key?fileKey=${fileKey}&access_token=bearer ${appStore.accessToken}`;
    return src ? <Image className={className} src={src} style={noStyle ? undefined : imageStyle} /> : null;
  }

  return null;
};

export default IconImageRenderer;
