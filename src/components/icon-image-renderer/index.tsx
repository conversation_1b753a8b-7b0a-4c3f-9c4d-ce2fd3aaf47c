import React, { Suspense, useMemo } from 'react';

export default function IconImageRenderer(props) {
  const DynamicComponent = useMemo(() => React.lazy(
    () => import('./Main'),
  ), []);
  if (props.wrapper) {
    return React.cloneElement(props.wrapper, { children: (
      <Suspense fallback={<span />}>
        <DynamicComponent {...props} />
      </Suspense>
    ) });
  } else {
    return (
      <Suspense fallback={<span />}>
        <DynamicComponent {...props} />
      </Suspense>
    );
  }
}
