import { useEffect, useState, useMemo } from 'react';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useLocalObservable, observer } from 'mobx-react-lite';
import dayjs from 'dayjs';
import { Skeleton } from '@taroify/core';
import { injectIntl } from '@/util/intl';
import appStore from '@/store/app';
import { _, download, fetch } from '@/util';
import KnowledgeAmEditor from '@/components/am-editor';
import KnowledgeExcel from '@/components/yq-excel';
import KnowledgeOnlyoffice from '@/components/yq-preview';
import YqAvatar from '@/components/yq-avatar';
import KnowledgeInteract from '../../packageOther/components/knowledge-interact';
import styles from './KnowledgeDetailCom.module.less';

function KnowledgeDetail(props) {
  const params = useMemo(() => Taro.getCurrentInstance().router.params, []);
  const { knowledgeId, values, intl, setEngine = () => {}, isThumbsUp = false } = props;
  const id = knowledgeId || values.element_id?.id || params?.id;

  const store = useLocalObservable(() => ({
    loading: true,
    knowledgeInfo: {},
    spaceInfo: {},
    relationTickets: [],

    // 获取知识的相关信息
    async fetchKnowledgeInfo(onlyState = false) {
      let url = `knowledge/v1/${appStore.tenantId}/know/${id}/portal?getAll=true${onlyState ? '&isPublish=false' : ''}`;
      if (appStore.isPublicUrl) {
        url = `knowledge/v1/${appStore.tenantId}/know/${Taro.getCurrentInstance().router.params.id}/${params.spaceId}/website${onlyState ? '?isPublish=false' : ''}`;
      }
      // eslint-disable-next-line no-console
      const res = await fetch(url).catch(() => {});
      if (!res || res.failed) {
        Taro.atMessage({
          type: 'error',
          message: intl.formatMessage({ id: 'yqc.mobile.api.wrong', defaultMessage: '接口出现报错，请联系管理员' }),
        });
        return;
      }
      const { like, favorite, spaceId } = res;

      store.knowledgeInfo = res;
      store.loading = false;
      let spaceRes;
      if (appStore.isPublicUrl) {
        spaceRes = await fetch(`knowledge/v1/${appStore.tenantId}/know/space/${spaceId}/website`);
      } else {
        spaceRes = await fetch(`knowledge/v1/${appStore.tenantId}/know/space/${spaceId}/portal`);
      }

      if (!spaceRes || spaceRes.failed) {
        Taro.atMessage({
          type: 'error',
          message: intl.formatMessage({ id: 'yqc.mobile.api.wrong', defaultMessage: '接口出现报错，请联系管理员' }),
        });
        return;
      }
      // Object.assign(store.spaceInfo, spaceRes);
      store.spaceInfo = {
        ...store.spaceInfo,
        ...spaceRes,
      };
    },
  }));
  const renderKnowledgeHead = () => {
    const { knowledgeInfo, loading } = store;
    return loading ? (
      <>
        <Skeleton animation="wave" style={{ width: '200px', margin: '16px 0 10px 0' }} />
        <Skeleton animation="wave" style={{ width: '100%', height: '24px', marginBottom: '10px' }} />
        <View style={{ display: 'flex' }}>
          {[1, 2, 3].map((item) => (
            <Skeleton key={item} animation="wave" style={{ width: '40px', height: '20px', marginRight: '5px' }} />
          ))}
        </View>
      </>
    ) : (
      <>
        <View className={styles.title}>{knowledgeInfo.name}</View>
        <View className={styles.headerBrief}>
          <View className={styles.headerPerson}>
            <YqAvatar personId={knowledgeInfo.createdBy} size="small" style={{ width: _.pxToRem(40), height: _.pxToRem(40) }}>
              {knowledgeInfo.createdByName}
            </YqAvatar>
            <View className={styles.updateBy} style={{ marginLeft: _.pxToRem(12), width: '100px', wordBreak: 'break-all', flex: 1 }}>
              {knowledgeInfo.createdByName}
              {knowledgeInfo.createdByName && ' · '}
              {dayjs(knowledgeInfo.lastUpdateDate).format('YYYY-MM-DD')}
            </View>
          </View>
          <KnowledgeInteract readQty={knowledgeInfo.readQty} likeQty={knowledgeInfo.likeQty} commentQty={knowledgeInfo.commentQty} isThumbsUp={isThumbsUp} />
        </View>
        <View className={styles.updateBy}>
          {knowledgeInfo.fdLabelDTOS.map((item) => (
            <View className={styles.labelItem}>{item.name}</View>
          ))}
        </View>
      </>
    );
  };
  const renderKnowledgeContent = () => {
    const { knowledgeInfo, spaceInfo } = store;
    const watermarkFlag = knowledgeInfo.watermarkFlag || spaceInfo.watermarkFlag; // 优先使用知识本身的水印权限
    const extraProps = {
      intl,
      knowledgeInfo,
      watermarkFlag,
    };
    if (!knowledgeInfo) return null;
    const type = knowledgeInfo.fileType || knowledgeInfo.type;
    if (type === 'DOCUMENT') {
      // etherpad
      return <KnowledgeAmEditor {...extraProps} tenantId={appStore.tenantId} afterRenderContent={(v) => setEngine(v)} />;
    } else if (type === 'EXCEL') {
      // luckly-sheet
      return <KnowledgeExcel {...extraProps} />;
    } else if (type === 'UPLOAD') {
      // 上传的
      return <KnowledgeOnlyoffice {...extraProps} fileKey={knowledgeInfo.fileKey} sourceId={id} />;
    }
  };

  useEffect(() => {
    store.fetchKnowledgeInfo();
  }, []);

  return (
    <View className={styles.frame}>
      <View className={styles.detailHead}>{renderKnowledgeHead()}</View>
      {renderKnowledgeContent()}
    </View>
  );
}

export default injectIntl(observer(KnowledgeDetail));
