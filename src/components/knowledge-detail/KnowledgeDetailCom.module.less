.frame {
  width: 100%;
  flex-grow: 1;
  width: 100%;
  position: relative;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  font-family: PingFangSC-Medium, PingFang SC;
  .taroify-dialog {
    width: 540px;
    border-radius: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    font-size: 28px;
    color: #333333;
    text-shadow: 0px 0px 50px rgba(0, 0, 0, 0.05);
  }

  .detailHead {
    width: 100%;
    padding: 0 5%;
    padding-bottom: 32px;
    display: flex;
    flex-direction: column;
    font-weight: 400;
    position: relative;

    &::after {
      content: ' ';
      left: 30px;
      right: 30px;
      bottom: 0;
      position: absolute;
      height: 1px;
      border-bottom: 1px solid rgba(203, 210, 220, 0.5);
    }
  }
}
.labelItem {
  display: inline-block; // 这么神奇。
  margin-right: 12px;
  margin-bottom: 5px;
  margin-top: 24px;
  color: #2979ff;
  background-color: #f0f8ff;
  border-radius: 4px;
  font-size: 24px;
  line-height: 40px;
  padding: 2px 21px;
}
.title {
  width: 100%;
  font-size: 36px;
  font-weight: 500;
  color: #262626;
  line-height: 44px;
  margin-top: 32px;
  margin-bottom: 24px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 10;
  word-break: break-all;
}
.updateBy {
  font-size: 28px;
  font-family: PingFangSC-Regular, PingFang SC;
  color: rgba(18, 39, 77, 0.45);
  line-height: 40px;
}

.headerBrief {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.headerPerson {
  display: flex;
  width: 100px;
  flex: 1;
}

.rightSide {
  left: auto !important;
  right: 0 !important;
  width: 80%;
  height: 100%;
}

.movable {
  width: 74px;
  height: 80px;
  background: #ffffff;
  box-shadow: 0px 6px 18px 0px rgba(18, 39, 77, 0.14);
  border-radius: 37px 0px 0px 37px;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;

  :global {
    .yq-movable-content {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.movableArea {
  position: fixed !important;
  z-index: 10;
  pointer-events: none;
  height: 100vh;
  width: 74px;
  right: 0;
}

.ticketWrapper {
  min-height: unset;
  padding-bottom: 24px;
}

.ticket {
  padding: 10px 24px;
  font-size: 28px;
  font-family: PingFangSC-Regular, PingFang SC;
  color: #2979ff;
  display: flex;
  align-items: center;
}
