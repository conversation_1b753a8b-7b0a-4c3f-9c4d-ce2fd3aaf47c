import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { observer } from 'mobx-react';
import Taro from '@tarojs/taro';
import queryString from 'query-string';
import { useIntl } from '@/util/intl';
import Yq<PERSON><PERSON>lov from '@/components/yq-treelov';
import { addAction } from '@/util/guance';
import appStore from '@/store/app';
import { fetch } from '@/util';
import platformLovStore from '@/store/platformLov';

interface WidgetConfig {
  customConfig: any[];
  hideQuantity?: boolean;
  hidePrice?: boolean;
}

interface Field {
  code: string;
  description: string; // 映射的lov字段
  name: string;
  customConfig: any[];
  widgetConfig: WidgetConfig;
}

interface CustomTreeLovProps {
  values: any;
  field: Field;
  onChange: Function;
  title: string;
  disabled: boolean;
  required: boolean;
  cardMode?: boolean;
}

const LANGUAGEMAP = { zh: 'zh_CN', en: 'en_US' };

function CustomTreeLov(props: CustomTreeLovProps): React.ReactElement {
  const { values, field, onChange = () => {}, title, disabled, required, cardMode = false } = props;

  const value = values?.[field?.description];

  const intl = useIntl();

  const [customerId, setCustomerId] = useState('');
  const [url, setUrl] = useState('');
  const [keyMap, setKeyMap] = useState({ key: 'id', value: 'name', displayKey: 'name' });

  const lovPara: any = useMemo(() => {
    const { customConfig: _customConfig, widgetConfig: { customConfig } } = field;

    const configs = customConfig || _customConfig || [];
    const result = { lovCode: 'CSM_TENANT_PRODUCT_SUBMIT' };
    if (configs.length) {
      configs.forEach(config => {
        result[config.key] = config.value;
      });
    }
    return result;
  }, [field]);

  const dep = values?.[lovPara?.parentId || lovPara?.customerId];

  const queryParams = useMemo(() => {
    const { id } = dep || {};
    return lovPara?.parentId ? { parentId: id } : { parentId: '0', customerId: id };
  }, [dep]);

  const errorMessage: string | null = useMemo(() => {
    const msg: object = queryString.parse(lovPara?.intlMessage || '');
    return msg[LANGUAGEMAP[appStore.language]] || intl.formatMessage({ id: 'yqc.mobile.customer.treelov.error.message', defaultMessage: '此产品不在可选择范国内，请选择父级产品或子产品' });
  }, [lovPara, intl]);

  useEffect(() => {
    const { customerId: id, parentId } = queryParams;

    if (customerId && customerId !== '0' && ((id && (customerId !== id)) || (parentId !== '0' && parentId !== customerId))) {
      onChange('', true, field.description);
    }
    setCustomerId(parentId || id);
  }, [queryParams, customerId, onChange, field]);

  useEffect(() => {
    if (lovPara?.lovCode) {
      fetchLovConfig(lovPara.lovCode);
    }
  }, [lovPara?.lovCode]);

  const fetchParentPath = useCallback(async (_url: string, parentId: string, _customerId: string, id: string) => {
    const params: any = {
      id,
    };
    if (parentId) {
      params.parentId = parentId;
    }
    if (_customerId) {
      params.customerId = _customerId;
    }
    const res = await fetch(_url, params).catch(() => {});
    if (res && !res.failed) {
      const record = res.content[0];
      onChange({ ...value, [keyMap.displayKey]: record[keyMap.displayKey] }, true, field.description);
    }
  }, [onChange, value, keyMap, field]);

  useEffect(() => {
    if (url && (queryParams.customerId || queryParams.parentId)) {
      if (value && !value[keyMap.displayKey] && value.id) {
        fetchParentPath(url, queryParams.parentId, queryParams.customerId, value.id);
      }
    }
  }, [url, queryParams, value, keyMap, fetchParentPath]);

  const fetchLovConfig = async (code: string) => {
    const config = await platformLovStore.getLovConfig(code);
    if (config) {
      const { requestUrl, valueField, displayField } = config; // parentId?
      let curUrl: string = requestUrl.replace('{tenantId}', appStore.tenantId);
      if (curUrl?.startsWith?.('/')) curUrl = curUrl.slice(1, curUrl.length);
      setUrl(curUrl);
      setKeyMap({ key: valueField, value: 'name', displayKey: lovPara?.textField || displayField });
    }
  };

  const handleBeforePopup = (): boolean => {
    const { id } = values?.[lovPara?.customerId] || {};
    const { id: _id } = values?.[lovPara?.parentId] || {};
    if (!id && !_id) {
      Taro.atMessage({
        message: intl.formatMessage({ id: 'yqc.mobile.customid.is.empty', defaultMessage: '关联ID为空' }),
        type: 'error',
      });
      return false;
    } else if (!url) {
      Taro.atMessage({
        message: intl.formatMessage({ id: 'yqc.mobile.data.wrong', defaultMessage: '数据错误' }),
        type: 'error',
      });
      return false;
    } else {
      return true;
    }
  };

  const handleChange = useCallback((v) => {
    // eslint-disable-next-line no-chinese/no-chinese
    addAction('treeLov选择');
    onChange(v, true, field.description);
  }, [onChange, field]);

  return appStore.tenantId && (
    <YqTreelov
      cardMode={cardMode}
      disabled={disabled}
      required={required}
      title={title}
      value={value}
      onChange={handleChange}
      keyMap={keyMap}
      url={url}
      queryParams={queryParams}
      searchFieldName="search_name"
      onPopup={handleBeforePopup}
      disabledErrorMsg={errorMessage || ''}
    />
  );
}

export default observer(CustomTreeLov);
