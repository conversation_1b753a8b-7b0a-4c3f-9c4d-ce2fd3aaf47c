import Taro, { useRouter } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { useEffect, useState, useRef } from 'react';
import mimeTypes from 'mime/lite';
import { injectIntl } from '@/util/intl';
import { observer } from 'mobx-react-lite';
import { Circle } from 'rc-progress';
import { site } from '@config';
import YqPreview from '@/components/yq-preview';
import YqIcon from '@/components/yq-icon';
import Modal from '@/components/yq-modal';
import appStore from '@/store/app';
import quickOrderStore from '@/store/quick-order';
import { PopupHeader } from '@/components/yq-component-popup';
import Icon from '../yq-icon';
// eslint-disable-next-line import/no-cycle
import { MbRow } from '../index';
import { _, chooseFile } from '../../util';
import './index.scss';

let uploadMap = {};
let uploadControllerMap = {};
function getPreviewUrl(fileKey) {
  return `${site}hfle/yqc/v1/0/files/download-by-key?fileKey=${fileKey}&access_token=${appStore.accessToken}`;
}
function UploadItem(props) {
  const { file, filesRef, onChange, disabled, intl, cameraId } = props;
  const [progress, setProgress] = useState(0);
  const fileKey = typeof file === 'string' ? file : file.fileKey;
  const extension = `.${(file?.name || fileKey || '.other').split('.').slice(-1)[0]}`;
  const mime = mimeTypes.getType(extension) || '';
  const isVideo = mime?.includes('video');
  const isImage = mime.includes('image');
  const route = useRouter();
  useEffect(() => {
    if (file.blobPath) {
      const create = route.onShow.includes('create-order');
      if (!create && cameraId.current !== file.cameraId) return;
      _.uploadFile(file.blobPath, file.name, undefined, ({ progress: _progress }, uploadTask) => {
        uploadControllerMap[file.blobPath] = uploadTask;
        setProgress(_progress > 99 ? 99 : _progress);
      }).then(({ data }) => {
        uploadMap[data.fileKey] = file.blobPath;
        let changedFiles = [];
        if (data.failed) {
          changedFiles = filesRef.current?.filter(f => {
            return f.blobPath === file.blobPath;
          });
        } else {
          changedFiles = filesRef.current?.map(f => {
            return f.blobPath === file.blobPath ? data : f;
          });
        }

        filesRef.current = changedFiles;
        onChange(JSON.stringify(changedFiles));
      });
    }
  }, [file.blobPath]);

  const handleClear = () => {
    if (file.blobPath && uploadControllerMap[file.blobPath]) {
      uploadControllerMap[file.blobPath].abort();
    }
    const changedFiles = filesRef.current?.filter(f => {
      return !((f.blobPath && f.blobPath === file.blobPath) || (f.fileKey && f.fileKey === fileKey));
    });
    filesRef.current = changedFiles;
    onChange(JSON.stringify(changedFiles));
  };

  const handlePreview = () => {
    if (!fileKey) return;
    if (process.env.TARO_ENV === 'weapp' && isImage) {
      _.previewFile(fileKey);
      return;
    }
    const modal = Modal.open({
      popupProps: {
        style: { height: '88%', backgroundColor: '#fff', borderTopRightRadius: '16px', borderTopLeftRadius: '16px' },
      },
      children: (
        <View>
          <PopupHeader title={intl.formatMessage({ id: 'yqc.mobile.show.form', defaultMessage: '预览' })} noCancel handleOk={() => modal.close()} />
          <YqPreview blobUrl={uploadMap[fileKey]} fileKey={fileKey} />
        </View>

      ),
    });
  };

  return (
    <View className="camera-img-item">
      <View className="camera-img-item-inner" onClick={handlePreview}>
        <img src={isVideo ? `${getPreviewUrl(fileKey)}&snapshot=true` : (file.blobPath || uploadMap[fileKey] || getPreviewUrl(fileKey))} alt="" />
        {isVideo && !file.blobPath && (
          <View className="camera-img-item-inner video">
            <View className="video-inner">
              <YqIcon type="Play-one" size={56} fill="#fff" theme="filled" />
            </View>
          </View>
        )}
      </View>
      {file.blobPath && <>
        <View className="camera-img-item-inner progress-circle" style={{ backgroundColor: 'rgba(18,39,77,0.6)' }}>
          <Circle className="circle" strokeWidth="10" trailWidth="10" strokeColor="#fff" trailColor="transparent" percent={progress} />
        </View>
        <View className="camera-img-item-inner progress-tips">
          <View style={{ color: '#fff' }}>{`${intl.formatMessage({ id: 'yqc.mobile.upload', defaultMessage: '上传' })}${progress}%`}</View>
        </View>
      </>}
      {!disabled && (
        <View onClick={handleClear} className="camera-img-clear">
          <YqIcon size={20} type="icon-shutdown" />
        </View>
      )}
    </View>
  );
}
function YqCamera(props) {
  const files = _.isJSON(props.value) ? JSON.parse(props.value) : (props.value || []);
  const filesRef = useRef(files);
  const cameraId = useRef(_.uuid());
  const ocrImgType = 'image/jpeg,image/png,image/heic,image/jpg,image/gif,image/bmp,image/pdf,image/tiff';
  const fileFormat = props.fileFormat || props.field?.widgetConfig?.fileFormat;

  useEffect(() => {
    return () => {
      filesRef.current = files;
      uploadMap = {};
      uploadControllerMap = {};
    };
  }, []);
  const handleAdd = async () => {
    props.commonFocus?.();
    if (props.disabled) return;
    // 回复里面的ocr需要延迟200ms再点击
    if (props.from === 'reply') {
      await new Promise((resolve) => {
        setTimeout(() => {
          resolve();
        }, 200);
      });
    }
    if (props?.whenClick) {
      props?.whenClick();
    }
    let config = {};
    if (fileFormat === 'single' && files.length >= 1) {
      Taro.atMessage({
        type: 'warning',
        message: props.intl.formatMessage({ id: 'yqc.mobile.only.choose.one', defaultMessage: '只能上传一个附件' }),
      });
      return;
    }
    if (props.ocr) {
      config = {
        ocr: true,
        count: 1,
        type: 'file',
        fileId: props.fileId || 'ocr-file',
        maxSize: 10,
        accept: ocrImgType,
        allowMimeList: ['image'],
      };
    } else {
      let defaultAccept = 'image/*,video/*';
      const { userAgent } = _.getMobileOperatingSystem();
      if (userAgent.toLocaleLowerCase().includes('vivo')) {
        defaultAccept = 'image/*'; // vivo自带的浏览器真奇葩，必须这样传才能同时调用相机和相册
      }
      config = {
        count: fileFormat === 'single' ? 1 : 8,
        type: 'file',
        fileId: props.fileId,
        accept: props.accept ? props.accept : defaultAccept,
        maxSize: 1000000 || (props.field?.widgetConfig?.fileSizeLimit > 1000 ? 1000 : props.field?.widgetConfig?.fileSizeLimit),
        allowMimeList: ['image', 'video'],
      };
    }

    // 小程序chooseFile是promise
    if (process.env.TARO_ENV === 'weapp') {
      // 修复快捷入口无法选择视频
      if (config.accept?.includes('video') && config.accept?.includes('image')) {
        config.type = 'all';
      } else if (config.accept?.includes('video')) {
        config.type = 'video';
      } else if (config.accept?.includes('image')) {
        config.type = 'image';
      }
      const res = await chooseFile(config);
      Taro.hideLoading();
      if (!res.errFiles?.length) {
        handleChange({ ocrResults: res.ocr, tempFiles: res.tempFiles }, res);
      }
    } else {
      // h5 chooseFile是callback
      config.success = async (res) => {
        Taro.hideLoading();
        if (!res.errFiles?.length) {
          handleChange({ ocrResults: res.ocr, tempFiles: res.tempFiles }, res);
        }
      };
      chooseFile(config);
    }
  };

  function jumpToOcrMiddlePage(res, type) {
    // 保存绑定信息
    quickOrderStore.setItemInfo(props.scItemId, props.ocrField);
    quickOrderStore.setImgSrc(res?.tempFiles[0]?.path);

    Taro.eventCenter.once('page:init', () => {
      Taro.eventCenter.trigger('ocrFileData', res);
    });
    _.navigateTo({
      url: `/pages/ocr/index?hiddenFooter=true&enteranceType=${type}`,
    });
  }

  function isHandleFromEnterance(res) {
    // 兼容下一个if需要根据type判断类型
    if (process.env.TARO_ENV === 'weapp') {
      const arr = res.tempFiles?.[0]?.path?.split('.');
      const extension = `.${arr[arr.length - 1]}`;
      const mime = mimeTypes.getType(extension) || '';
      if (mime?.includes('image')) {
        res.tempFiles[0].type = 'image/';
      } else if (mime?.includes('video')) {
        res.tempFiles[0].type = 'video/';
      } else if (mime?.includes('audio')) {
        res.tempFiles[0].type = 'audio/';
      }
    }

    if (props?.enteranceType) {
      // 从快捷入口来的，需要根据文件类型跳了
      if (props.enteranceType === 'BOTH') {
        if (
          res.tempFiles?.[0]?.type.includes('video/')
          || !ocrImgType.includes(res.tempFiles?.[0]?.type)
        ) {
          // 视频文件一定是拍照提单
          props.onChange(res);
        } else {
          // 图片或拍照只能去中间页判断了
          jumpToOcrMiddlePage(res, props.enteranceType);
        }
        return true;
      } else if (props.enteranceType === 'OCR') {
        // OCR去中间页,并且只有ocr识别按钮
        jumpToOcrMiddlePage(res, props.enteranceType);
        return true;
      } else if (props.enteranceType === 'CAMERA') {
        // 拍照提单不去中间页
        props.onChange(res);
        return true;
      }
    }
    return false;
  }

  // 这里逻辑巨多，小心维护！！！ —— 哪天首页快捷入口功能干掉了，就可以删掉
  // res : ocr识别需要的数据
  // allRes: 原始数据
  function handleChange(res, allRes) {
    const changedFiles = files.concat(...allRes.tempFilePaths.map((blobPath, index) => ({
      blobPath,
      name: allRes.tempFiles[index].originalFileObj.name,
      cameraId: cameraId.current,
    })));
    filesRef.current = changedFiles;
    // 将拍照提单使用的数据挂到res上
    res.changedFiles = JSON.stringify(changedFiles);

    // 存储拍照的数据，以防中间页要用
    quickOrderStore.setChangedFiles(res.changedFiles);

    // bugfix 判断太多了，有的地方忘记更新这个了
    if (props.from !== 'field') {
      quickOrderStore.setImgSrc(res?.tempFiles[0]?.path);
    }

    // 快捷入口处理
    if (isHandleFromEnterance(res)) {
      return;
    }
    // 页面组件内的不跳转，用于区分其他页面的选文件操作（直接跳走了）
    if (props?.inPage) {
      props.onChange(res);
      return;
    }

    // 保存绑定信息
    quickOrderStore.setItemInfo(props.scItemId, props.ocrField);
    quickOrderStore.setImgSrc(res?.tempFiles[0]?.path);

    Taro.eventCenter.once('page:init', () => {
      Taro.eventCenter.trigger('ocrFileData', res);
    });
    _.navigateTo({
      url: '/pages/ocr/index?hiddenFooter=true',
    });
  }

  function renderAddIcon() {
    return (
      <View onClick={handleAdd} className="camera-img-item add">
        <View className="camera-img-item-add-icon">
          <Icon type="plus" size={64} fill="#8F99AB" />
        </View>
      </View>
    );
  }

  function renderItems() {
    return (
      <View className="camera-img-list">
        {files.map(file => (
          <UploadItem cameraId={cameraId} onChange={props.onChange} key={file.fileKey || file.blobPath || `${Math.random()}`} file={file} filesRef={filesRef} disabled={props.disabled} intl={props.intl} />
        ))}
        {!props.disabled && renderAddIcon()}
      </View>
    );
  }
  try {
    if (props.type === 'module') {
      return (
        <View className={props.className} onClick={handleAdd}>
          {props.children}
        </View>
      );
    }
    return (
      <MbRow
        copyable={false}
        hiddenRight
        cardMode={props.cardMode}
        value={props.value}
        field={props.field}
        error={props.field?.error}
        required={props.required}
        label={props.title}
        disabled={props.disabled}
        brief={renderItems()}
      />
    );
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error('Camera Error', e);
    return props.intl.formatMessage({ id: 'yqc.mobile.api.wrong', defaultMessage: '接口出现报错，请联系管理员' });
  }
}

YqCamera.defaultProps = {
  ocr: false,
};

export default injectIntl(observer(YqCamera));
