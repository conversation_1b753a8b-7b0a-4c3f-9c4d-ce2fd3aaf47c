import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import { View } from '@tarojs/components';
import { useIntl } from '@/util/intl';
import { _ } from "@/util";
import YqEmpty from '@/components/yq-empty';
import { ChatRecordsProps } from './ChatRecords';
import { useChatRecordsByTicket, useChatRecords } from './api';
// 引用客服中心资源
import ChatBubble from '../../packageIntelligent/components/chat-bubble';
import '../../packageIntelligent/pages/chat-room/index.scss';

const ChatRecords: React.FC<ChatRecordsProps> = function ChatReocrds(props) {
  const { itemId, ticketFlag, defaultData, hiddenReadIcon = false } = props;
  const [showTime, setShowTime] = useState(-1); // 判断是否显示某一条消息的时间
  const [playVoice, setPlayVoice] = useState(-1); // 显示播放哪一行的语音

  const intl = useIntl();
  const { data } = defaultData || (ticketFlag ? useChatRecordsByTicket({
    ticketId: itemId,
    agentFlag: true,
  }) : useChatRecords({
    id: itemId,
    agentFlag: true,
  }));
  function renderBubble(item, index) {
    const { payload, full_name: fullName, withdraw_flag: withdrawFlag } = item;
    // eslint-disable-next-line no-nested-ternary
    const realPayload = _.isJSON(payload?.value) ? JSON.parse(payload.value) : _.isJSON(payload) ? JSON.parse(payload) : payload;
    const msgContent = realPayload?.msgContent || realPayload;
    const msg = _.isJSON(msgContent) ? JSON.parse(msgContent) : msgContent;
    return (msg?.type === 'user_withdraw' || withdrawFlag === 1) ? (
      <View className="chat-room-withdrawal">
        {fullName === 'BOT'
          ? intl.formatMessage({ id: 'yqc.mobile.retracted.message', defaultMessage: '你撤回了一条消息' })
          : intl.formatMessage({ id: 'yqc.mobile.user.retracted.message', defaultMessage: '对方撤回了一条消息' })}
      </View>
    ) : (
      <ChatBubble
        key={item.id || _.uuid()}
        item={item}
        index={index}
        showTime={showTime}
        setShowTime={setShowTime}
        playVoice={playVoice}
        setPlayVoice={setPlayVoice}
        readOnly
        hiddenReadIcon={hiddenReadIcon}
      />
    );
  }

  function dealData(originData) {
    if (!originData || originData.length === 0) {
      return [];
    }
    return originData.slice().reverse().map((item => {
      return {
        ...item,
        msg_id: item.id,
        send_time: item.sent_on,
        nickname: item.agent_real_name || data.botName,
        image_url: item.agent_image_url || data.botAvatar,
        bot_flag: item.msg_origin === 'BOT' || item.bot_flag === 1,
        userRealName: item.user_real_name,
      };
    }));
  }
  return (
    <View className="chat-room-content">
      {data?.messageList?.length ? dealData(data?.messageList).map(renderBubble) : <YqEmpty />}
    </View>
  );
};

export default observer(ChatRecords);
