import useSWR from 'swr';
import { fetch } from '@/util';
import appStore from '@/store/app';

function useChatRecordsByTicket({ ticketId, agentFlag }) {
  const tenantId = appStore.tenantId;
  const url = `intelligent/v1/${tenantId}/conversion/messagesByTicket?agentFlag=${agentFlag || true}&ticketId=${ticketId}`;
  return useSWR(url, fetch, { revalidateOnFocus: false, revalidateIfStale: true });
}

function useChatRecords({ id, agentFlag }) {
  const tenantId = appStore.tenantId;
  const url = `intelligent/v1/${tenantId}/conversion/${id}/messages?agentFlag=${agentFlag || true}`;
  return useSWR(url, fetch, { revalidateOnFocus: false, revalidateIfStale: true });
}


export { useChatRecordsByTicket, useChatRecords };
