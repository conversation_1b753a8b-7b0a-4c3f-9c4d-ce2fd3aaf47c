@import "../../style/setting.scss";

.ocr-select {
  .option-bar {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16px 0;

    .hengxian {
      width: 140px;
      height: 12px;
      background: #d9d9d9;
      border-radius: 6px;
    }
  }

  .option-bar.option-bar-moving {
    .hengxian {
      background-color: $color-brand;
    }
  }

  .ocr-select-header {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 32px;
    height: 48px;
    color: $black;
    font-weight: 400;

    .close,
    .blank {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 80px;
      height: 100%;
    }
  }

  .taroify-search.search-box {
    padding: 20px 2vw;
    height: 88px;
  }

  .tool-box {
    position: absolute;
    background-color: white;
    width: 100%;
    bottom: 0px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // padding: 24px 24px;
    color: $color-brand;
    font-size: 32px;
    z-index: 100;

    // h5
    .left.hydrated {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24px 12px 24px 24px;
      width: 50%;

      >.hydrated {
        width: 50%;

        .btn {
          width: 100%;
          padding: 0;
        }
      }

      >.h5-view {
        .btn {
          width: 100%;
        }
      }
    }

    .right.hydrated {
      padding: 24px 24px 24px 12px;
      width: 50%;

      .btn.hydrated {
        width: 100%;
        flex: inherit;
      }
    }

    // 小程序
    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24px 12px 24px 24px;
      width: 50%;

      >.hydrated {
        width: 50%;

        .btn {
          width: 100%;
        }
      }

      >.h5-view {
        width: 50%;

        .btn {
          width: 100%;
        }
      }
    }

    .right {
      padding: 24px 24px 24px 12px;
      width: 50%;

      .h5-view.btn {
        width: 100%;
      }
    }


  }

  .ocr-content {
    margin-top: 10px;
    overflow: auto;
  }

  .ocr-box {
    position: relative;
    margin: 0 auto;
    width: 96vw;
    height: 100%;
    background-color: antiquewhite;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;

    .ocr-scale-box {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: auto;
      overflow-x: hidden;
    }

    input {
      z-index: 999;
    }

    img {
      display: inline-block;
      max-height: 100%;
      max-width: 100%;
    }

    // h5
    svg {
      position: absolute;

      .py {
        fill: rgba(255, 149, 0, 0.05);
        stroke: rgba(255, 149, 0, 1);
        stroke-width: 1px;
      }

      .py-checked {
        fill: rgba(255, 149, 0, 0.5);
        stroke: rgba(255, 149, 0, 1);
        stroke-width: 1px;
      }
    }

    // 小程序
    .svg {
      position: absolute;

      .py {
        position: absolute;
        z-index: 99;
        border: 2px solid rgba(255, 149, 0, 1);
        background-color: rgba(255, 149, 0, 0.05);
      }

      .py-checked {
        border: 2px solid rgba(255, 149, 0, 0.5);
        background-color: rgba(255, 149, 0, 0.5);
      }
    }
  }
}