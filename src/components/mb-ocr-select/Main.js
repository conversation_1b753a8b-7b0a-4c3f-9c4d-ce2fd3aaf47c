import { useEffect, useState, useRef } from 'react';
import { View, Image } from '@tarojs/components';
import { observer } from 'mobx-react';
import { injectIntl } from '@/util/intl';
import Taro from '@tarojs/taro';
import { Search, Checkbox, Button, Toast } from '@taroify/core';
import config from '@config';
import { addAction } from '@/util/guance';

import { startOcr, _ } from '@/util';
import Drawer from '../drawer';
import YqCamera from '../yq-camera';
import './index.scss';
import YqIcon from '../yq-icon';

const popHeight = '85vh';
const dis = 0.2; // 单位缩放比率

function OcrSelect(props) {
  const { show, intl } = props;
  const [style, setStyle] = useState({});
  const [value, setValue] = useState('');
  const [data, setData] = useState([]);
  const [json, setJson] = useState(null);
  const [imgLoaded, setImgLoaded] = useState(false);
  const [imgSrc, setImgSrc] = useState('');
  const [bound, setBound] = useState(null);
  const [isAll, setIsAll] = useState(false);
  const [scale, setScale] = useState(1);
  const [scaling, setScaling] = useState(false);
  const [sd, setSd] = useState();
  const [moving, setMoving] = useState(false);
  const [point, setPoint] = useState(undefined);
  const [translate, setTranslate] = useState([0, 0]);
  const [movingHeader, setMovingHeader] = useState(false);
  const [pointHeader, setPointHeader] = useState(undefined);
  const [translateHeader, setTranslateHeader] = useState([0, 0]);
  const [fix, setFix] = useState(0);

  // 小程序
  const [wxImgInfo, setWxImgInfo] = useState(null);

  const imgRef = useRef();
  let first = true;
  const { screenHeight: bodyHeight } = Taro.getSystemInfoSync();
  const isWeapp = process.env.TARO_ENV === 'weapp';

  function resetSth() {
    setBound(null);
    setJson(null);
    setData([]);
    setImgLoaded(false);
    setScale(1);
    setTranslate([0, 0]);
    setFix(0);
  }

  /**
   * 外部传入数据处理
   * - 初始化识别数据
   */
  useEffect(() => {
    if (props.json) {
      setJson(props.json);
    }
  }, [props.json]);

  // 图片地址有了先预览出来
  useEffect(() => {
    if (props.src) {
      setImgSrc(props.src);
      if (!first) {
        resetSth();
      }
    }
  }, [props.src]);

  function handleCancel() {
    const _data = [...data];
    _data?.map(v => {
      v.checked = false;
      v.fromSearch = false;
    });
    setValue('');
    setData(_data);
    if (typeof props.onCancel === 'function') {
      props.onCancel();
    }
  }

  function handleConfirm() {
    if (typeof props.onOk === 'function') {
      // eslint-disable-next-line no-chinese/no-chinese
      addAction('使用ocr功能');
      props.onOk(data.filter(v => v.checked));
    }
  }

  function handleSelectAll() {
    const _data = [...data];
    _data.map(item => {
      item.checked = !isAll;
      item.fromSearch = false;
    });
    setIsAll(!isAll);
    setData([..._data]);
    if (typeof props.onChange === 'function') {
      // props?.onChange(_data.filter(v => v.checked))
    }
  }

  function onSelect(item) {
    const _data = [...data];
    const index = _data.findIndex(v => v.id === item.id);
    if (~index) {
      _data[index].checked = !_data[index].checked;
    }
    if (_data.findIndex(v => !v.checked) === -1) {
      setIsAll(true);
    } else {
      setIsAll(false);
    }
    setData([..._data]);
    if (typeof props.onChange === 'function') {
      // props?.onChange(_data.filter(v => v.checked))
    }
  }

  async function onSearch(_value) {
    const _data = [...data];
    _data.map(v => {
      if (_value && v.text.indexOf(_value) > -1) {
        v.checked = true;
        v.fromSearch = true;
      } else if (v.fromSearch) {
        v.checked = false;
        v.fromSearch = false;
      }
    });
    setValue(_value);
    setData([..._data]);
    if (typeof props.onChange === 'function') {
      // props?.onChange(_data.filter(v => v.checked))
    }
  }

  function onload() {
    const _bound = imgRef.current.getBoundingClientRect();
    setBound(_bound);

    if (first) {
      startDrawSvg(_bound);
    }
  }

  function startDrawSvg(_bound) {
    const rate = json.rotated_image_width / (_bound.width / scale);
    const isRotated = json.image_angle / 90 % 2 !== 0;
    setStyle({
      zIndex: 99,
      // 旋转的照片宽高要对调一下
      width: _bound[isRotated ? 'height' : 'width'] / scale,
      height: _bound[isRotated ? 'width' : 'height'] / scale,
    });
    const _data = [];
    json.lines.map(item => {
      _data.push({
        text: item.text,
        polygons: item.position,
      });
    });
    const _dataAf = [];
    _data.map(item => {
      const _polygons = [];
      item.polygons.map(v => {
        _polygons.push(v / rate);
      });
      const _item = { ...item };
      _item.id = _.uuid();
      _item.checked = false;
      _item.text = item.text;
      _item.polygons = item.polygons;
      _item.polygonsScale = [..._polygons];
      _item.polygonsText = `${_polygons[0]} ${_polygons[1]},${_polygons[2]} ${_polygons[3]},${_polygons[4]} ${_polygons[5]},${_polygons[6]} ${_polygons[7]}`;
      _dataAf.push(_item);
    });
    setData(_dataAf);
  }

  function whenClick() {
    first = false;
  }

  // 解决bug：取消后重选识别位置丢失，原因是图片加载完成比弹框打开快，导致bound不准
  useEffect(() => {
    if (props.show && imgLoaded && json && !isWeapp) {
      setTimeout(() => {
        onload();
      });
    }
    if (props.show && imgLoaded && json && isWeapp) {
      setTimeout(() => {
        startDrawSvg({
          width: getImageStyle().width,
          height: getImageStyle().height,
        });
      });
    }
  }, [props.show, imgLoaded, json, fix]);

  useEffect(() => {
    if (!isWeapp && imgLoaded && bound && json) {
      startDrawSvg(bound);
    }

    if (isWeapp && imgLoaded && wxImgInfo && json) {
      startDrawSvg({
        width: getImageStyle().width,
        height: getImageStyle().height,
      });
    }
  }, [bound, imgLoaded, json]);

  async function onOcrChange(message) {
    if (message) {
      const file = {
        info: message.tempFiles[0],
        ocr: message?.ocrResults?.[0],
      };
      Toast.loading({
        message: intl.formatMessage({ id: 'yqc.mobile.loading', defaultMessage: '加载中...' }),
        duration: 9999999,
      });
      resetSth();

      setImgSrc(file.info?.path);

      const _ocrData = await startOcr({
        accessKey: config.HH_AK,
        secretKey: config.HH_SK,
        fileData: file?.info?.compressFile || file?.info?.originalFileObj,
      });
      Toast.close();
      setJson({ ..._ocrData });
    }
  }

  // PC调试用
  function onWheel(e) {
    if (!_.isPc()) {
      return;
    }

    if (e.deltaY > 0) {
      const _scale = scale + dis / 10;
      setScale(_scale);
    } else {
      let _scale = scale - dis / 10;
      if (_scale < 0) {
        _scale = 0;
      }
      setScale(_scale);
    }
  }

  function onMouseDown(e) {
    if (!_.isPc()) {
      return;
    }
    setMoving(true);
    setPoint([e.clientX, e.clientY]);
  }

  function onMouseMove(e) {
    if (!_.isPc()) {
      return;
    }
    if (moving && point) {
      const tx = e.clientX - point[0];
      const ty = e.clientY - point[1];
      setTranslate([tx, ty]);
    }
  }

  function onMouseUp() {
    if (!_.isPc()) {
      return;
    }
    setMoving(false);
  }

  function onTouchStart(e) {
    if (_.isPc()) {
      return;
    }

    // 双指缩放
    if (e.touches.length === 2) {
      setScaling(true);
      const _sd = Math.hypot(
        e.touches[0].pageX - e.touches[1].pageX,
        e.touches[0].pageY - e.touches[1].pageY,
      );
      setSd(_sd);
    }
    // 单指长按移动
    if (e.touches.length === 1) {
      setMoving(true);
      setPoint([e.touches[0].clientX, e.touches[0].clientY]);
    }
  }

  function onTouchMove(e) {
    if (_.isPc()) {
      return;
    }
    // 双指缩放
    if (e.touches.length === 2 && scaling) {
      const _sd = Math.hypot(
        e.touches[0].pageX - e.touches[1].pageX,
        e.touches[0].pageY - e.touches[1].pageY,
      );
      if (sd !== undefined) {
        const isScaleBig = _sd > sd;
        if (isScaleBig) {
          const _scale = scale + dis / 10;
          setScale(_scale);
        } else {
          let _scale = scale - dis / 10;
          if (_scale < 0) {
            _scale = 0;
          }
          setScale(_scale);
        }
        setSd(_sd);
      }
    }
    // 单指移动
    if (e.touches.length === 1 && moving && point) {
      const tx = e.touches[0].clientX - point[0];
      const ty = e.touches[0].clientY - point[1];
      setTranslate([tx, ty]);
    }
  }

  function onTouchEnd(e) {
    if (_.isPc()) {
      return;
    }
    // if (e.touches.length === 2) {
    //   let _sd = Math.hypot(
    //     e.touches[0].pageX - e.touches[1].pageX,
    //     e.touches[0].pageY - e.touches[1].pageY
    //   )
    // }
    setScaling(false);
    setMoving(false);
  }

  // 唉，非要弹窗也拖拽
  function onTouchStartHeader(e) {
    // 单指长按移动
    if (e.touches.length === 1) {
      setMovingHeader(true);
      setPointHeader([e.touches[0].clientX, e.touches[0].clientY]);
    }
  }

  function onTouchMoveHeader(e) {
    // 单指移动
    if (e.touches.length === 1 && movingHeader && pointHeader) {
      const tx = e.touches[0].clientX - pointHeader[0];
      const ty = e.touches[0].clientY - pointHeader[1];
      // 防止触底
      if (bodyHeight - 200 > Math.abs(ty)) {
        setTranslateHeader([tx, ty]);
      }
    }
  }

  function onTouchEndHeader(e) {
    setMovingHeader(false);
  }

  function getImageRotate() {
    // 兼容大多exif图
    if (json?.image_angle % 360 === 90) {
      return -180;
    } else if (json?.image_angle % 360 === 180) {
      return -270;
    } else if (json?.image_angle % 360 === 270) {
      return -360;
    } else {
      // return -90
      // 处理0为了兼容不带exif的，社区exif0的处理
      return 0;
    }
  }

  function getRotate() {
    if (json?.image_angle % 360 === 90) {
      return -90;
    } else if (json?.image_angle % 360 === 180) {
      return -180;
    } else if (json?.image_angle % 360 === 270) {
      return -270;
    } else {
      return 0;
    }
  }

  function onWeImgLoad(e) {
    const width = parseInt(e.detail.width);
    const height = parseInt(e.detail.height);

    setWxImgInfo({
      width,
      height,
    });
    setImgLoaded(true);
  }

  function getImageStyle() {
    const defaultStyle = {
      width: 'inherit',
      height: 'inherit',
    };
    if (wxImgInfo) {
      const { screenWidth, screenHeight } = Taro.getSystemInfoSync();
      // 图片的容器盒子为60vh和横向padding: 0 4px; todo 后续如果封装这个盒子得从外面参数取
      const width = screenWidth - 8;
      const height = screenHeight * 0.6;
      // 盒子图片宽高比
      const rate = width / height;
      // 图片宽高比
      const imgRate = wxImgInfo.width / wxImgInfo.height;

      // 垂直型图片
      if (rate > imgRate) {
        const _scale = height > wxImgInfo.height ? 1 : height / wxImgInfo.height;
        return {
          width: wxImgInfo.width * _scale,
          height: wxImgInfo.height * _scale,
        };
      } else {
        // 横型扁图片
        const _scale = width > wxImgInfo.width ? 1 : width / wxImgInfo.width;
        return {
          width: wxImgInfo.width * _scale,
          height: wxImgInfo.height * _scale,
        };
      }
    } else {
      return defaultStyle;
    }
  }

  function getPloygon(pointer) {
    const arr = pointer.split(',');
    const position = {
      width: arr[1].split(' ')[0] - arr[0].split(' ')[0],
      height: arr[2].split(' ')[1] - arr[1].split(' ')[1],
      left: `${arr[0].split(' ')[0] }px`,
      top: `${arr[0].split(' ')[1] }px`,
    };
    return position;
  }

  if (isWeapp) {
    return (
      <Drawer
        show={show}
        popup
        rounded={false}
        height={`calc(${popHeight} - ${translateHeader[1]}px)`}
      >
        <View className="ocr-select">
          <View
            className={`option-bar ${movingHeader ? 'option-bar-moving' : ''}`}
            onTouchStart={onTouchStartHeader}
            onTouchMove={onTouchMoveHeader}
            onTouchEnd={onTouchEndHeader}
          >
            <View className="hengxian" />
          </View>
          <View className="ocr-select-header">
            <View
              className="blank"
              style={{ paddingLeft: Taro.pxTransform(12) }}
            />
            <View className="ocr-title">{intl.formatMessage({ id: 'yqc.mobile.ocr.result', defaultMessage: '识别结果' })}
              {/* {json && json?.image_angle} */}
            </View>
            <View className="close" onClick={() => handleCancel()}>
              <YqIcon
                type="Close"
                size={32}
                fill="#262626"
                theme="outline"
              />
            </View>

          </View>
          <Search
            className="search-box"
            value={value}
            placeholder={intl.formatMessage({ id: 'yqc.mobile.search', defaultMessage: '搜索' })}
            onChange={(e) => onSearch(e.detail.value)}
          />

          <View
            className="ocr-content"
            style={{
              maxHeight: `calc(${popHeight} - ${Taro.pxTransform('48px')} - ${Taro.pxTransform('48px')} - ${Taro.pxTransform('128px')} - ${Taro.pxTransform('104px')} - ${translateHeader[1]}px)`,
            }}
          >
            <View className="ocr-box">
              <View
                className="ocr-scale-box"
                // pc缩放事件
                onWheel={onWheel}
                // pc移动事件
                onMouseDown={onMouseDown}
                onMouseMove={onMouseMove}
                onMouseUp={onMouseUp}
                // 移动端缩放、移动事件
                onTouchStart={onTouchStart}
                onTouchMove={onTouchMove}
                onTouchEnd={onTouchEnd}
              >

                {imgSrc ? (
                  <Image
                    src={imgSrc}
                    ref={imgRef}
                    style={{
                      width: getImageStyle()?.width,
                      height: getImageStyle()?.height,
                      transform: `scale(${scale}) translateX(${translate[0]}px) translateY(${translate[1]}px) rotate(${getImageRotate()}deg)`,
                      transition: 'all cubic-bezier(0.5, 0.5, 0.5, 0.5) 0s',
                    }}
                    onLoad={onWeImgLoad}
                  />
                ) : null}

                {imgLoaded && data && <div
                  className="svg"
                  style={{
                    ...style,
                    transform: `scale(${scale}) translateX(${translate[0]}px) translateY(${translate[1]}px) rotate(${getRotate()}deg)`,
                    transition: 'all cubic-bezier(0.5, 0.5, 0.5, 0.5) 0s',
                  }}
                >
                  {data?.map(item => {
                    return <div
                      key={item.id}
                      data-text={item.text}
                      data-points={item.polygonsText}
                      className={item.checked ? 'py py-checked' : 'py'}
                      style={{
                        width: getPloygon(item.polygonsText).width,
                        height: getPloygon(item.polygonsText).height,
                        left: getPloygon(item.polygonsText).left,
                        top: getPloygon(item.polygonsText).top,
                      }}
                      onClick={() => onSelect(item)}
                    />;
                  })}
                </div>}
              </View>

            </View>
          </View>
          <View className="tool-box">
            <View className="left">
              <Checkbox checked={isAll} onClick={handleSelectAll}>
                {intl.formatMessage({ id: 'yqc.mobile.select.all', defaultMessage: '全选' })}
              </Checkbox>
              <YqCamera
                ocr
                inPage
                type="module"
                editable
                fileFormat="single"
                onChange={onOcrChange}
                whenClick={whenClick}
              >
                <Button className="btn" variant="outlined" color="primary">{intl.formatMessage({ id: 'yqc.mobile.ocr.reselect', defaultMessage: '重选' })}</Button>
              </YqCamera>
            </View>
            <View className="right">
              <Button className="btn" color="primary" onClick={() => handleConfirm()}>{intl.formatMessage({ id: 'yqc.mobile.ocr.insert', defaultMessage: '确定' })}</Button>
            </View>
          </View>
        </View>
      </Drawer>
    );
  }

  return <Drawer
    show={show}
    popup
    rounded={false}
    height={`calc(${popHeight} - ${translateHeader[1]}px)`}
  >
    <View className="ocr-select">
      <View
        className={`option-bar ${movingHeader ? 'option-bar-moving' : ''}`}
        onTouchStart={onTouchStartHeader}
        onTouchMove={onTouchMoveHeader}
        onTouchEnd={onTouchEndHeader}
      >
        <View className="hengxian" />
      </View>
      <View className="ocr-select-header">
        <View
          className="blank"
          style={{ paddingLeft: Taro.pxTransform(12) }}
        />
        <View className="ocr-title">{intl.formatMessage({ id: 'yqc.mobile.ocr.result', defaultMessage: '识别结果' })}
          {/* {json && json?.image_angle} */}
        </View>
        <View className="close" onClick={() => handleCancel()}>
          <YqIcon
            type="Close"
            size={32}
            fill="#262626"
            theme="outline"
          />
        </View>

      </View>
      <Search
        className="search-box"
        value={value}
        placeholder={intl.formatMessage({ id: 'yqc.mobile.search', defaultMessage: '搜索' })}
        onChange={(e) => onSearch(e.detail.value)}
      />

      <View className="ocr-content" style={{ maxHeight: `calc(${popHeight} - ${Taro.pxTransform('48px')} - ${Taro.pxTransform('48px')} - ${Taro.pxTransform('128px')} - ${Taro.pxTransform('104px')} - ${translateHeader[1]}px)` }}>
        <View className="ocr-box">
          <View
            className="ocr-scale-box"
            // pc缩放事件
            onWheel={onWheel}
            // pc移动事件
            onMouseDown={onMouseDown}
            onMouseMove={onMouseMove}
            onMouseUp={onMouseUp}
            // 移动端缩放、移动事件
            onTouchStart={onTouchStart}
            onTouchMove={onTouchMove}
            onTouchEnd={onTouchEnd}
          >
            {imgSrc ? <Image src={imgSrc} ref={imgRef} style={{ transform: `scale(${scale}) translateX(${translate[0]}px) translateY(${translate[1]}px) rotate(${getImageRotate()}deg)`, transition: 'all cubic-bezier(0.5, 0.5, 0.5, 0.5) 0s' }} onLoad={() => setImgLoaded(true)} /> : null}
            {imgLoaded && data && !isWeapp ? <svg
              style={{ ...style, transform: `scale(${scale}) translateX(${translate[0]}px) translateY(${translate[1]}px) rotate(${getRotate()}deg)`, transition: 'all cubic-bezier(0.5, 0.5, 0.5, 0.5) 0s' }}
            >
              {data?.map(item => {
                return <polygon
                  key={item.id}
                  data-text={item.text}
                  points={item.polygonsText}
                  className={item.checked ? 'py py-checked' : 'py'}
                  onClick={() => onSelect(item)}
                />;
              })}
            </svg> : null}
          </View>

        </View>
      </View>
      <View className="tool-box">
        <View className="left">
          <Checkbox checked={isAll} onClick={handleSelectAll}>
            {intl.formatMessage({ id: 'yqc.mobile.select.all', defaultMessage: '全选' })}
          </Checkbox>
          <YqCamera
            ocr
            inPage
            type="module"
            editable
            fileFormat="single"
            onChange={onOcrChange}
            whenClick={whenClick}
          >
            <Button className="btn" variant="outlined" color="primary">{intl.formatMessage({ id: 'yqc.mobile.ocr.reselect', defaultMessage: '重选' })}</Button>
          </YqCamera>
        </View>
        <View className="right">
          <Button className="btn" color="primary" onClick={() => handleConfirm()}>{intl.formatMessage({ id: 'yqc.mobile.ocr.insert', defaultMessage: '确定' })}</Button>
        </View>
      </View>
    </View>
  </Drawer>;
}
export default injectIntl(observer(OcrSelect));
