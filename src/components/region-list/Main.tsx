import { useEffect, useState } from 'react';
import { AreaPicker, Popup } from '@taroify/core';
import { observer } from 'mobx-react-lite';
import { useIntl } from '@/util/intl';
import MbRow from '@/components/mb-row';
import { fetch } from '@/util';
import appStore from '@/store/app';
import { addAction } from '@/util/guance';

function TransValueRegionList({ field, value, title, disabled, required, onChange, values, cardMode = false }) {
  const intl = useIntl();
  const [show, setShow] = useState(false);
  const [loading, setLoading] = useState(false);
  const { widgetConfig: { regionColumns } } = field;
  const columns = regionColumns.split(',')?.length;
  const [areaList, setAreaList] = useState({ province_list: {}, city_list: {}, county_list: {} });
  const allData = appStore.areaData?.district?.concat(appStore.areaData.city).concat(appStore.areaData.province) || [];

  useEffect(() => {
    if (!appStore.areaData) {
      fetchAll();
    } else {
      loadAreaData(appStore.areaData);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSelect = () => {
    // eslint-disable-next-line no-chinese/no-chinese
    addAction('使用省市区选择组件');
    setShow(true);
  };

  const handleClear = () => {
    onChange('');
  };

  const handleConfirm = (codeList: Array<string>) => {
    onChange(allData.find(c => c.code === codeList[columns - 1]).id);
    setShow(false);
  };

  const loadAreaData = ({ province, city, district }) => {
    const newList = { province_list: {}, city_list: {}, county_list: {} };
    function trans(data, type) { newList[type][data.code] = data.name; }
    province.forEach((d) => trans(d, 'province_list'));
    city.forEach((d) => trans(d, 'city_list'));
    district.forEach((d) => trans(d, 'county_list'));
    setAreaList(newList);
  };

  const fetchAll = async () => {
    setLoading(true);
    const promises = ['province', 'city', 'district'].map(level => fetch(`hpfm/v1/cn_address/list?level=${level}`));
    await Promise.all(promises).then(([province, city, district]) => {
      appStore.areaData = { province, city, district };
      loadAreaData(appStore.areaData);
    }).catch(e => {
      // eslint-disable-next-line no-console
      console.error(e);
    }).finally(() => {
      setLoading(false);
    });
  };

  return (
    <>
      <MbRow
        cardMode={cardMode}
        clearButton
        handleClear={handleClear}
        values={values}
        field={field}
        error={field.error}
        onClick={handleSelect}
        label={title}
        disabled={disabled}
        required={required}
        value={allData?.find(r => r.id === value)?.path}
      />
      <Popup open={show} placement="bottom" onClose={() => setShow(false)}>
        <AreaPicker
          loading={loading}
          defaultValue={allData?.find(r => r.id === value)?.code}
          depth={columns}
          onConfirm={handleConfirm}
          onCancel={() => setShow(false)}
        >
          <AreaPicker.Toolbar>
            <AreaPicker.Button>{intl.formatMessage({ id: 'yqc.mobile.cancel', defaultMessage: '取消' })}</AreaPicker.Button>
            <AreaPicker.Title>{title}</AreaPicker.Title>
            <AreaPicker.Button>{intl.formatMessage({ id: 'yqc.mobile.confirm', defaultMessage: '确认' })}</AreaPicker.Button>
          </AreaPicker.Toolbar>
          <AreaPicker.Columns>{areaList}</AreaPicker.Columns>
        </AreaPicker>
      </Popup>
    </>
  );
}

export default observer(TransValueRegionList);
