import { useState, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { Dialog, Button } from '@taroify/core';
import Taro from '@tarojs/taro';
import { View } from '@tarojs/components';
import { _ } from '@/util';
import appStore from '@/store/app';
import GlobalPlace from '@/components/yq-modal/GlobalPlace';
import { addAction } from '@/util/guance';
import { useIntl } from '@/util/intl';
import YqIcon from '@/components/yq-icon';
import styles from '@/components/relevant-knowledge/components/cell/RelevantKnowledgeCell.module.less';

import './index.scss';

const colorMap = {
  SUBMIT: 'primary',
  DELETE: 'danger',
  CREATE: 'primary',
};
export default observer((buttonProps) => {
  const {
    action,
    cancelText,
    color,
    confirmFlag,
    confirmText,
    displayMethod,
    icon,
    id,
    name,
    okText,
    openType,
    tag,
    type,
    viewSize,
    viewId,
    haveParent,
    onSubmit = async () => {},
    Function,
    button = {},
    formFieldValues = {},
    modalClose,
  } = buttonProps;
  const intl = useIntl();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const resolveRef = useRef(() => {});
  const debounceRef = useRef(false);

  const funcConfig = { person: appStore.self.person, personId: appStore.self.personId };
  const hiddenFlag = _.calculateHidden(button, formFieldValues, funcConfig);
  if (hiddenFlag) return null;

  async function handleSubmit() {
    if (!loading) {
      setLoading(true);
      await onSubmit(type);
      setLoading(false);
    }
  }
  async function handleDelete() {
    if (!loading) {
      setLoading(true);
      await onSubmit(type);
      setLoading(false);
    }
  }
  async function executionAction() {
    if (!loading) {
      setLoading(true);
      await onSubmit(undefined, undefined, id);
      setLoading(false);
    }
  }

  async function preConfirm(func) {
    if (confirmFlag) {
      setOpen(true);
      // const modal = Modal.open({
      //   title: confirmText,
      //   popupProps: { style: { height: '90vh' } },
      //   onOk: async () => {
      //     func();
      //     modal.close();
      //   },
      //   onCancel: () => {
      //     // resolve(false);
      //     modal.close();
      //   },
      //   // children: <ChooseImage initFileKey={initFileKey} callBack={callback} realName={realName} avatarStyle={avatarStyle} />,
      // });
      Dialog.confirm({
        className: styles.correctedPosition,

        cancel: cancelText || intl.formatMessage({ id: 'yqc.mobile.cancel', defaultMessage: '取消' }),
        confirm: okText || intl.formatMessage({ id: 'yqc.mobile.confirm', defaultMessage: '确认' }),
        title: (
          <>
            <YqIcon type="attention" fill="#fd7d23" size={92} theme="filled" />
            <View className={styles.confirmText}>
              {confirmText}
            </View>
          </>
        ),
        onConfirm: async () => {
          func();
        },
      });
      // const confirm = await new Promise((resolve, reject) => {
      //   resolveRef.current = resolve;
      // });
      // if (confirm) {
      //   func();
      // }
    } else {
      func();
    }
  }

  function handleClose() {
    if (modalClose) {
      modalClose();
      return;
    }
    _.navigateBack();
  }

  async function handleClick() {
    // eslint-disable-next-line no-chinese/no-chinese
    addAction('使用低代码的按钮', {
      buttonType: type,
    });
    switch (type) {
      case 'SUBMIT':
        await preConfirm(handleSubmit);
        break;
      case 'DELETE':
        await preConfirm(handleDelete);
        break;
      case 'CREATE':
        await preConfirm(handleSubmit);
        break;
      case 'CLOSE':
        await preConfirm(handleClose);
        break;
      case 'Function':
        // 模拟 debounce 防止连续的多次提交
        if (!debounceRef.current) {
          debounceRef.current = true;
          const res = await Function();
          if (!res || res?.failed) {
            debounceRef.current = false;
          }
        }
        break;
      case 'EXPRESSION':
        await preConfirm(executionAction);
        break;
      default:
        break;
    }
    if (action === 'OPEN_VIEW') {
      if (haveParent) return;
      _.navigateTo({
        url: `/pages/task-detail/index?viewId=${viewId}`,
      });
    }
  }

  return (
    <Button
      color={color === 'default' ? colorMap[type] : color}
      onClick={handleClick}
      id={id}
      loading={loading}
      key={id}
    >
      {name}
    </Button>

  );
});
