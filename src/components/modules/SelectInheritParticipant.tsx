import React, { useCallback, useEffect, useMemo, useState } from 'react';
import styles from '@/components/multiple-choice/MultipleChoice.module.less';
import MultipleSelect from '@/components/multiple-select';
import { fetch } from '@/util';
import appStore from '@/store/app';
import useUrlParams from '@/hooks/use-url-params';
import ParticipantsCell from '@/components/participants/components/cell';

interface ParticipantList { user: any[], group: any[] }
export default function SelectInherit({ modal }) {
  const [slelected, _setSelected] = useState([]);
  const [options, setOptions] = useState<any[]>([]);
  const params = useUrlParams();

  useEffect(() => {
    (async () => {
      const o = (await fetch(`itsm/v1/${appStore.tenantId}/${params.businessObjCode}/${params.originalId}/participants`)) as ParticipantList;
      setOptions([...o.user, ...o.group]);
    })();
  }, []);
  function selectSelected(_selected) {
    modal.handleOKProps(_selected);
  }

  const renderCell = useCallback((record: Record<string, any>) => (
    <ParticipantsCell name={record.participant} type={record.type.toLowerCase()} />
  ), []);

  return (
    <MultipleSelect
      valueField="id"
      transformResponse={(response) => {
        return response;
      }}
      store={{
        options,
      }}
      records={slelected}
      pageSize={99999}
      wrapperClass={styles.wrapper}
      renderCell={renderCell}
      onChange={selectSelected}
      hiddenHeader
    />
  );
}
