import { useMemo, useEffect, useState, useCallback } from 'react';
import Taro from '@tarojs/taro';
import { Loading } from '@taroify/core';
import { useLocalObservable, observer } from 'mobx-react-lite';
import classnames from 'classnames';
import { injectIntl } from '@/util/intl';
import { fetch, sectionField, _ } from '@/util';
import YqNavBar from '@/components/yq-navbar';
import PageLoader from '@/components/page-loader';
import appStore from '@/store/app';
import { addAction } from '@/util/guance';
import Modal from '@/components/yq-modal';
import SelectInherit from '@/components/modules/SelectInheritParticipant';
import Page from '../../global-component/page';
import './index.less';

function Modules({ intl, fromTab = false }) {
  const [pageStore, setPageStore] = useState<any>({ hasButton: false });
  const params = useMemo(() => Taro.getCurrentInstance().router?.params || {}, []);
  const inheritParticipantsWindowFlag = params.inheritParticipantsWindowFlag === 'true';
  const defaultData = JSON.parse(decodeURIComponent(params?.defaultData || '') || '{}');
  const store = useLocalObservable(() => ({
    formFieldValues: {},
    module: {} as any,
    viewId: null,
    feedbackViewId: null,
    viewCode: null,
  }));

  useEffect(() => {
    if (params.code === 'GLOBAL_TICKET') {
      _.redirectTo({
        url: '/packageOther/pages/global-ticket-page/index',
      });
    }
  }, [params.code]);

  async function handleSubmitView() {
    const { submitData, isPassed } = sectionField.getLowcodeSubmitData(store.formFieldValues, pageStore.jsonData, params, intl);
    if (!isPassed) return;
    let participantList;
    if (inheritParticipantsWindowFlag) {
      await new Promise((resolve) => {
        const modal = Modal.open({
          title: intl.formatMessage({ id: 'yqc.mobile.inherit.participants/groups', defaultMessage: '继承参与人/组' }),
          popupProps: { style: { height: '90vh' } },
          onOk: (_participantList) => {
            participantList = _participantList;
            resolve(true);
            modal.close();
          },
          onCancel: () => {
            resolve(false);
            modal.close();
          },
          children: (
            <SelectInherit />
          ),
        });
      });
    }

    const originalViewParam = params?.originalViewId ? `&originalViewId=${params.originalViewId}` : '';
    const res = await fetch(
      `lc/v1/${appStore.tenantId}/lc_action_relationships/${params.viewId}/${params.viewId}/submit?relationId=${params.relationId}&originalId=${params.originalId}${originalViewParam}`,
      [{
        ...submitData,
        participantList: participantList?.current?.map(participant => ({
          participantId: participant.participantId,
          type: participant.type,
        })),
        _status: 'create',
      }],
      'post',
    );
    await appStore.submitPromiseCallback();
    const ticketViewMapping = {
      CHANGE: 'CHANGE_MOBILE_DETAIL',
      INCIDENT: 'INCIDENT_MOBILE_DETAIL',
      PROBLEM: 'PROBLEM_MOBILE_DETAIL',
      SC_REQ_ITEM: 'SC_REQ_ITEM_MOBILE_DETAIL',
      // SC_REQ_ITEM: 'WF_TASK_INSTANCE_MOBILE_DETAIL',
      SC_TASK: 'SC_TASK_MOBILE_DETAIL',
      PROBLEM_TASK: 'PROBLEM_TASK_MOBILE_DETAIL',
      CHANGE_TASK: 'CHANGE_TASK_MOBILE_DETAIL',
      WF_TASK_INSTANCE: 'WF_TASK_INSTANCE_MOBILE_DETAIL',
    };
    // 跳转详情页面，之前是固定的，现改为优先使用feedbackViewId
    _.redirectTo({
      url: `/pages/task-detail/index?ticketId=${res?.[0]?.id}&${params.feedbackViewId ? `viewId=${params.feedbackViewId}` : `viewCode=${ticketViewMapping[res?.[0]?.table_name]}`}`,
    });
    return res;
  }
  const pageButtons = params.relationId ? [{
    id: 'close',
    name: intl.formatMessage({ id: 'yqc.mobile.cancel', defaultMessage: '取消' }),
    type: 'CLOSE',
  }, {
    id: 'confirm',
    color: 'primary',
    name: intl.formatMessage({ id: 'yqc.mobile.confirm', defaultMessage: '确认' }),
    type: 'Function',
    Function: handleSubmitView,
  }] : null;

  const fetchViewIdByCode = useCallback(async () => {
    const res = await fetch(`portal/v1/${appStore.tenantId}/mobile_portal_module/${params.code}`);
    if (!res?.viewId) {
      Taro.atMessage({
        type: 'error',
        message: intl.formatMessage({ id: 'yqc.mobile.no.viewId', defaultMessage: '视图不存在' }),
      });
    }
    store.module = res;
    store.viewId = res.viewId;
  }, [intl, params.code, store]);

  useEffect(() => {
    if (!params?.viewId && !fromTab) fetchViewIdByCode();
  }, [params.viewId, fetchViewIdByCode, fromTab]);

  const viewId = useMemo(() => {
    let id = store.viewId || params?.viewId;
    if (process.env.TARO_ENV === 'weapp' && fromTab) {
      id = _.getLocalStorage('OrderPage')?.viewId;
    }
    return id;
  }, [params?.viewId, store.viewId, fromTab]);

  const viewCode = useMemo(() => {
    let code = store.viewCode || params?.viewCode;
    if (process.env.TARO_ENV === 'weapp' && fromTab) {
      code = _.getLocalStorage('OrderPage')?.viewCode;
    }
    return code;
  }, [params?.viewCode, store.viewCode, fromTab]);

  const title = useMemo(() => {
    let name = store.module.name || decodeURIComponent(params?.title || '');
    if (process.env.TARO_ENV === 'weapp' && fromTab) {
      name = _.getLocalStorage('OrderPage')?.title;
    }
    return name;
  }, [params.title, store.module?.name, fromTab]);

  useEffect(() => {
    // eslint-disable-next-line no-chinese/no-chinese
    store.module?.name && addAction('打开模块', {
      title: store.module?.name,
    });
  }, [store.module?.name]);

  const isDingtalk = navigator && /DingTalk/.test(navigator.userAgent);
  return (
    <Page
      className={classnames('yq-module', {
        hasButton: pageStore?.hasButton,
        dingTalk: isDingtalk,
      })}
    >
      <YqNavBar
        title={title}
        hiddenLeft={params.fromWebRobot ? false : fromTab}
        onLeftClick={appStore.hiddenFooter && window !== window.parent ? () => {
          process.env.TARO_ENV === 'h5' && window.parent.postMessage(Taro.getCurrentInstance().router, '*');
        } : undefined}
      />
      {(viewId || viewCode) ? (
        <PageLoader
          needCalculate={!params.instanceId}
          viewId={viewId}
          viewCode={viewCode}
          instanceId={params.instanceId}
          formFieldValues={store.formFieldValues}
          listViewPrefix
          extraDefaultData={defaultData}
          setPageStore={setPageStore}
          pageButtons={pageButtons}
          fromTab={fromTab}
          preview={params?.fromDesigner === 'true'}
        />
      ) : <Loading size={40} className="yq-module-loading" />}
    </Page>
  );
}

export default injectIntl(observer(Modules));
