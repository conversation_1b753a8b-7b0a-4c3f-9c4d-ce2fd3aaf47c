.cell {
  min-height: 100px !important;
  padding: 24px 0 !important;

  &.selfPage {
    border-bottom: 1px solid #ededed;
  }

  .brief {
    margin-left: 0 !important;

    .name {
      color: inherit !important;

      &.onlyName {
        margin-bottom: 0 !important;
      }
    }
  }
}

.output {
  margin: 5px 0;
  line-height: 36px;

  .outputTitle {
    width: unset;
    max-width: 200px;
    margin-right: 18px;
    flex-shrink: 0;
    word-break: break-word;
    white-space: pre-wrap;
    color: rgba(18, 39, 77, 0.65);
    opacity: 1;
  }

  .outputContent {
    word-break: break-word;
    white-space: pre-wrap;
    color: rgba(18, 39, 77, 0.65);
  }
}