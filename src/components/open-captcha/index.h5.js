import React, { useRef } from 'react';
import { useCaptcha } from '@zknow/aj-captcha';
import appStore from '@/store/app';
import { getCaptchaKey, sendVerificationCode } from './api';
import { site } from '../../../config';

const useCaptchas = () => {
  const [run] = useCaptcha({ path: `${site}oauth/public`, type: 'slide', extraParams: { lang: appStore.language } });
  const fun = async (value, type) => {
    const verKey = await getCaptchaKey(run);
    if (!verKey) {
      return;
    }
    const res = await sendVerificationCode(value, type, verKey);
    return res;
  };
  return fun;
};

export default useCaptchas;
