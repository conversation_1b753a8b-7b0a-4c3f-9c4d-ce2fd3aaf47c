const CryptoJS = require('./utils/aes_util.js');

/**
 * @desc 验证码插件
 * <AUTHOR>
 * @datatime 2021/4/7 8:59
 */
Component({
  options: {
    multipleSlots: true,
  },
  properties: {
    opt: {
      type: Object,
      value: null,
    },
  },
  data: {
    show: false, // 是否显示组件
    token: '', // 验证码token
    secretKey: '', // refresh时返回的key
    captchaKey: '', // 验证码key
    originalImageBase64: '', // 原图base64
    jigsawImageBase64: '', // 拼图滑块base64
    verifyImgOutHeight: '', // 底图父容器高度
    verifySubBlockWidth: '', // 填充块宽度值
    verifySubBlockTop: '', // 填充块top值
    leftBarClass: 'status-1', // 滑块默认样式
    verifyTipsClass: '', // 文字提示框样式
    verifyTipsText: '', // 文字提示框内容
    verifyMsgText: '', // 滑块框文字内容
    verifyBarAreaClass: '', // 滑块区域样式类
    clickWordTapName: '', // 点击事件名称
    clickWordTapNum: 0, // 点选点击次数
    clickWordXYList: [], // 点选坐标对象集合
    clickWordPointList: [
      // 坐标标点集合，初始化五个
      { left: 0, top: 0, display: 'none' },
      { left: 0, top: 0, display: 'none' },
      { left: 0, top: 0, display: 'none' },
      { left: 0, top: 0, display: 'none' },
      { left: 0, top: 0, display: 'none' },
    ],
    backImgLeft: 0, // 背景图右边界坐标
    backImgTop: 0, // 背景图上边界坐标
  },
  lifetimes: {
    attached() {
      const _self = this;
      // 在组件实例进入页面节点树时执行
      // 初始化属性默认值
      const defaults = {
        baseUrl: 'https://captcha.anji-plus.com/captcha-api', // 服务器前缀，默认值：https://captcha.anji-plus.com/captcha-api
        mode: 'pop', // 弹出式pop，固定fixed, 默认值：pop
        captchaType: 'blockPuzzle', // 验证码类型：滑块blockPuzzle，点选clickWord，默认值：blockPuzzle
        imgSize: {
          // 底图大小，默认值：{ width: '310px',height: '155px'}
          width: '310px',
          height: '155px',
        },
        barHeight: '40px', // 滑块大小，默认值：'40px'
        vSpace: 5, // 底图和verify-bar-area间距，默认值：5像素
        success(res) {}, // 成功回调
        fail(res) {}, // 失败回调
      };
      const res = _self._extend(defaults, _self.data.opt);
      // 点选获取底图位置-嵌入式
      if (res.captchaType == 'clickWord' && res.mode == 'fixed') {
        wx.createSelectorQuery()
          .in(_self)
          .select('.backImg')
          .boundingClientRect((rect) => {
            _self.setData({
              backImgLeft: rect.left,
              backImgTop: rect.top,
            });
          })
          .exec();
      }
      _self.setData({
        verifyImgOutHeight: `${parseInt(res.imgSize.height) + parseInt(res.vSpace) }px`,
        verifySubBlockWidth: `${Math.floor((parseInt(res.imgSize.width) * 47) / 310) }px`,
        verifySubBlockTop: `-${ parseInt(res.imgSize.height) + parseInt(res.vSpace) }px`,
        show: res.mode != 'pop',
      });
      _self._uuid();
      _self._refresh();
    },
  },
  methods: {
    // 显示组件
    show() {
      const _self = this;
      this.setData({ show: true }, () => {
        // 点选获取底图位置-弹出式
        debugger;
        if (_self.data.opt.captchaType === 'clickWord' && _self.data.opt.mode === 'pop') {
          wx.createSelectorQuery()
            .in(_self)
            .select('.backImg')
            .boundingClientRect((rect) => {
              _self.setData({
                backImgLeft: rect.left,
                backImgTop: rect.top,
              });
            })
            .exec();
        }
      });
    },
    // 隐藏组件
    hide() {
      this.setData({ show: false });
    },
    // 重新装载
    reload() {
      const _self = this;
      const res = _self.data.opt;
      // 点选获取底图位置-嵌入式
      if (res.captchaType == 'clickWord' && res.mode == 'fixed') {
        wx.createSelectorQuery()
          .in(_self)
          .select('.backImg')
          .boundingClientRect((rect) => {
            _self.setData({
              backImgLeft: rect.left,
              backImgTop: rect.top,
            });
          })
          .exec();
      }
      _self.setData({
        verifyImgOutHeight: `${parseInt(res.imgSize.height) + parseInt(res.vSpace) }px`,
        verifySubBlockWidth: `${Math.floor((parseInt(res.imgSize.width) * 47) / 310) }px`,
        verifySubBlockTop: `-${ parseInt(res.imgSize.height) + parseInt(res.vSpace) }px`,
        show: res.mode !== 'pop',
      });
      _self._refresh();
    },
    // 滑块触摸结束验证
    _blockPuzzleCheck(obj) {
      const _self = this;
      const url = `${_self.data.opt.baseUrl }/captcha-validate-and-cache`;
      let clientUid = _self.data.opt.captchaType === 'blockPuzzle' ? (clientUid = wx.getStorageSync('slider')) : (clientUid = wx.getStorageSync('point'));
      // 服务端默认310宽度底图
      const serverX = (obj.offsetX / parseInt(_self.data.opt.imgSize.width)) * 310;
      const pointJson = JSON.stringify({ x: serverX, y: 5 });
      const data = {
        captchaKey: _self.data.secretKey,
        captchaType: 'PUZZLE',
        lang: _self.data.opt.lang || 'zh_CN',
        captcha: pointJson,
      };
      _self._getData(url, data, (res) => {
        // debugger
        if (res.data.success) {
          // 响应正确
          _self.setData({
            leftBarClass: 'status-4',
            verifyTipsClass: 'suc-bg',
            captchaKey: res.data.captchaKey,
            verifyTipsText: `${obj.expendTime }s验证成功`,
          });
          setTimeout(() => {
            _self._refresh();
            _self.data.opt.mode === 'pop' ? _self.hide() : '';
            // 回调成功函数
            // debugger
            let captchaVerification = `${_self.data.token }---${ pointJson}`;
            captchaVerification = _self.data.secretKey ? CryptoJS.AesEncrypt(captchaVerification, _self.data.secretKey) : captchaVerification;
            if (typeof _self.data.opt.success === 'function') {
              _self.data.opt.success(res.data);
            }
          }, 700);
        } else {
          // 响应错误
          _self.setData({
            leftBarClass: 'status-3',
            verifyTipsClass: 'err-bg',
            verifyTipsText: res.data.repMsg,
          });
          if (typeof _self.data.opt.fail === 'function') {
            _self.data.opt.fail(res.data);
          }
          setTimeout(() => {
            _self._refresh();
          }, 700);
        }
      });
    },
    // 点选点击事件
    _clickWordTap(res) {
      const _self = this;
      const detail_x = res.detail.x;
      const detail_y = res.detail.y;
      // 显示对应标记点
      _self._setPoint(detail_x, detail_y);

      if (_self.data.clickWordTapNum > 2) {
        // 更新服务器校验，取消点击事件
        _self.setData({ clickWordTapName: '' });

        const url = `${_self.data.opt.baseUrl }/captcha/check`;
        let clientUid = _self.data.opt.captchaType == 'blockPuzzle' ? (clientUid = wx.getStorageSync('slider')) : (clientUid = wx.getStorageSync('point'));
        const pointJson = JSON.stringify(_self.data.clickWordXYList);
        const data = {
          captchaType: _self.data.opt.captchaType,
          pointJson: _self.data.secretKey ? CryptoJS.AesEncrypt(pointJson, _self.data.secretKey) : pointJson,
          token: _self.data.token,
          clientUid,
          ts: Date.now(),
        };
        _self._postData(url, data, (res) => {
          if (res.data.repCode == '0000') {
            // 响应正确
            _self.setData({
              verifyMsgText: '验证成功',
              verifyBarAreaClass: 'suc-area',
            });
            setTimeout(() => {
              _self._refresh();
              _self.data.opt.mode == 'pop' ? _self.hide() : '';
              // 回调成功函数
              let captchaVerification = `${_self.data.token }---${ pointJson}`;
              captchaVerification = _self.data.secretKey ? CryptoJS.AesEncrypt(captchaVerification, _self.data.secretKey) : captchaVerification;
              if (typeof _self.data.opt.success === 'function') {
                _self.data.opt.success({ captchaVerification });
              }
            }, 700);
          } else {
            // 响应错误
            _self.setData({
              verifyMsgText: '验证失败',
              verifyBarAreaClass: 'err-area',
            });
            if (typeof _self.data.opt.fail === 'function') {
              _self.data.opt.fail(res.data);
            }
            setTimeout(() => {
              _self._refresh();
            }, 700);
          }
        });
      }
    },
    // 显示坐标
    _setPoint(detail_x, detail_y) {
      const _self = this;
      const xylist = _self.data.clickWordXYList;
      const pointList = _self.data.clickWordPointList;
      const offsetX = detail_x - _self.data.backImgLeft;
      const offsetY = detail_y - _self.data.backImgTop;
      // 服务端默认310宽度底图，按比例换成对应坐标
      const serverX = (offsetX / parseInt(_self.data.opt.imgSize.width)) * 310;
      const serverY = (offsetY / parseInt(_self.data.opt.imgSize.width)) * 310;
      xylist.push({ x: serverX, y: serverY });
      pointList[_self.data.clickWordTapNum] = { left: `${offsetX - 10 }px`, top: `${offsetY - 10 }px`, display: 'block' };
      _self.setData({
        clickWordTapNum: ++_self.data.clickWordTapNum,
        clickWordXYList: xylist,
        clickWordPointList: pointList,
      });
    },
    // 刷新 在这里获取底图
    _refresh() {
      const _self = this;
      const url = `${_self.data.opt.baseUrl}/captcha-by-type`;
      let clientUid = '';
      if (_self.data.opt.captchaType === 'blockPuzzle') {
        clientUid = wx.getStorageSync('slider');
      } else {
        clientUid = wx.getStorageSync('point');
      }
      const data = {
        captchaType: 'PUZZLE',
        // clientUid: clientUid,
        lang: _self.data.opt.lang || 'zh_CN',
        // ts: Date.now(),
      };
      _self._getData(url, data, (res) => {
        // debugger
        if (res.data.success) {
          const text = data.captchaType === 'PUZZLE' ? '向右滑动完成验证' : `请依次点击【${ res.data.repData.wordList.join(',') }】`;
          _self.setData({
            originalImageBase64: `data:image/png;base64,${res.data.originalImageBase64}`,
            jigsawImageBase64: `data:image/png;base64,${res.data.jigsawImageBase64}`,
            secretKey: res.data.captchaKey,
            token: res.data.token,
            leftBarClass: 'status-1',
            verifyTipsClass: '',
            verifyTipsText: '',
            verifyMsgText: text,
            verifyBarAreaClass: '',
            clickWordTapName: data.captchaType === 'blockPuzzle' ? '' : '_clickWordTap',
            clickWordTapNum: 0,
            clickWordXYList: [],
            clickWordPointList: [
              { left: 0, top: 0, display: 'none' },
              { left: 0, top: 0, display: 'none' },
              { left: 0, top: 0, display: 'none' },
              { left: 0, top: 0, display: 'none' },
              { left: 0, top: 0, display: 'none' },
            ],
          });
        } else {
          // 响应错误
          _self.setData({
            leftBarClass: 'status-3',
            verifyTipsClass: 'err-bg',
            verifyTipsText: res.data.repMsg,
          });
          setTimeout(() => {
            _self._refresh();
          }, 700);
        }
      });
    },
    // 生成uuid
    _uuid() {
      const s = [];
      const hexDigits = '0123456789abcdef';
      for (let i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
      }
      s[14] = '4'; // bits 12-15 of the time_hi_and_version field to 0010
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
      s[8] = s[13] = s[18] = s[23] = '-';
      const slider = 'slider' + `-${ s.join('')}`;
      const point = 'point' + `-${ s.join('')}`;
      // 判断下是否存在 slider和point
      if (!wx.getStorageSync('slider')) {
        wx.setStorageSync('slider', slider);
      }
      if (!wx.getStorageSync('point')) {
        wx.setStorageSync('point', point);
      }
    },
    // post请求封装
    _postData(url, data, success) {
      wx.request({
        url,
        data: JSON.stringify(data),
        method: 'POST',
        enableCache: false,
        header: {
          'Content-Type': 'application/json;charset=UTF-8',
        },
        // 成功执行
        success(res) {
          // http状态码判断
          const status_code = res.statusCode;
          if (status_code != 200) {
            wx.showToast({
              title: `响应错误:\t${ status_code}`,
              icon: 'error',
              duration: 2000,
            });
          } else if (typeof success === 'function') {
            success(res);
          }
        },
        // 失败执行
        fail(res) {
          wx.showToast({
            title: '网络错误',
            icon: 'error',
            duration: 2000,
          });
        },
      });
    },

    objectToQueryString(obj) {
      // 使用Object.keys和Array.map将对象的每个键值对转换成字符串的形式
      const keyValuePairs = Object.keys(obj).map((key) => {
        // encodeURIComponent确保字符被正确编码，避免URL中的特殊字符问题
        const encodedKey = encodeURIComponent(key);
        const encodedValue = encodeURIComponent(obj[key]);
        return `${encodedKey}=${encodedValue}`;
      });

      // 将所有键值对字符串用'&'连接起来，并在最前面加上'?'
      return `?${keyValuePairs.join('&')}`;
    },

    // get请求封装
    _getData(url, data, success) {
      wx.request({
        url: url + this.objectToQueryString(data),
        method: 'GET',
        enableCache: false,
        header: {
          'Content-Type': 'application/json;charset=UTF-8',
        },
        // 成功执行
        success(res) {
          // http状态码判断
          const status_code = res.statusCode;
          if (status_code != 200) {
            wx.showToast({
              title: `响应错误:\t${ status_code}`,
              icon: 'error',
              duration: 2000,
            });
          } else if (typeof success === 'function') {
            success(res);
          }
        },
        // 失败执行
        fail(res) {
          wx.showToast({
            title: 'GET网络错误',
            icon: 'error',
            duration: 2000,
          });
        },
      });
    },
    // json对象合并
    _extend(defaults, opt) {
      const res = {};
      for (var key in defaults) {
        res[key] = defaults[key];
      }
      for (var key in opt) {
        res[key] = opt[key];
      }
      this.setData({ opt: res });
      return res;
    },
  },

});
