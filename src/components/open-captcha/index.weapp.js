import React, { useRef } from 'react';
import Modal from '@/components/yq-modal';
import appStore from '@/store/app';
import { sendVerificationCode } from './api';
import { site } from '../../../config';

const useCaptchas = () => {
  const modalRef = useRef();
  const fun = async (value, type) => {
    return new Promise((resolve, reject) => {
      const captchaOpt = {
        baseUrl: `${site}oauth/public`,
        mode: 'fixed',
        captchaType: 'blockPuzzle',
        lang: appStore.language,
        success(res) {
          modalRef.current.close();
          const verKey = res.captchaKey;
          if (!verKey) {
            reject();
          }
          sendVerificationCode(value, type, verKey).then((newRes) => {
            resolve(newRes);
          });
        },
        fail(res) {
          console.error(res);
        },
      };
      modalRef.current = Modal.open({
        popupProps: { style: { height: '300px' } },
        children: (
          <div style={{ paddingTop: '40px', display: 'flex', justifyContent: 'center' }}>
            <aj-captcha opt={captchaOpt} />
          </div>
        ),
      });
    });
  };
  return fun;
};

export default useCaptchas;
