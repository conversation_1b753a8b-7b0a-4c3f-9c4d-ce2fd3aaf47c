import { fetch } from '@/util';

export interface VerificationCodeResult {
  captchaKey: string;
  failure: boolean;
  interval: number;
  message: string;
  success: boolean;
}

/**
 * 滑动验证
 */
async function getCaptchaKey(run = async () => {}) {
  const data: any = await run();
  const { captchaVerification } = data || {};
  const captchaKey = captchaVerification?.split('---')?.[0];
  return captchaKey;
}

/**
 * 发送验证码
 */
async function sendVerificationCode(value: string, type, captchaKey): Promise<VerificationCodeResult> {
  const _type = type === 'Email' ? 'email' : 'sms';
  const url = `oauth/public/yqc/sendCaptcha?phoneOrEmail=${value}&type=${_type}${captchaKey ? `&captchaKey=${captchaKey}` : ''}`;
  const res: VerificationCodeResult = await fetch(
    url,
    {
      phoneOrEmail: value,
      type: _type,
      captchaKey,
    },
    'POST',
  ).catch(() => {});
  return res;
}

export { getCaptchaKey, sendVerificationCode };
