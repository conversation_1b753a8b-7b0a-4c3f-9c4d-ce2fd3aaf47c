/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useRef, useState } from 'react';
import { observer, useLocalObservable } from 'mobx-react-lite';
import Taro from '@tarojs/taro';
import { Loading } from '@taroify/core';
import { fetch, _ } from '@/util';
import appStore from '@/store/app';
import AmEditor from './components/index';
import { addWatermark } from './watermark';
import './index.scss';

const KnowledgeAmEditor = observer((props) => {
  const {
    knowledgeInfo: { publishStatus, id, spaceId },
    watermarkFlag,
    prefixCls = 'knowledge-center',
    tenantId,
    intl,
    afterRenderContent = () => {},
  } = props;
  const engineRef = useRef(null);
  const [ready, setReady] = useState(false);
  const [content, setContent] = useState('');

  const store = useLocalObservable(() => ({
    // 水印信息
    watermarkInfo: { init: false },
    setWatermarkInfo(data) {
      store.watermarkInfo = data;
    },
    // 获取空间水印详情
    async fetchWatermark(_spaceId) {
      if (_.isEmpty(_spaceId)) {
        store.setWatermarkInfo({ init: true });
        return;
      }
      if (appStore.isPublicUrl) {
        store.setWatermarkInfo({ init: true });
      } else {
        const res = await fetch(`knowledge/v1/${tenantId}/know/watermarks/map?spaceId=${_spaceId}`).catch(() => {});
        if (!res || res.failed) {
          Taro.atMessage({
            type: 'error',
            message: !res ? intl.formatMessage({ id: 'yqc.mobile.api.wrong', defaultMessage: '接口出现报错，请联系管理员' }) : res.message,
          });
          store.setWatermarkInfo({ init: true });
          return;
        }
        store.setWatermarkInfo({ ...res, init: true });
      }
    },
  }));

  const fetchContent = async () => {
    const version = publishStatus === 'PUBLISHED' ? 'version=PUBLISHED' : '';
    const url = `knowledge/v1/${tenantId}/data?id=${id}&${version}`;
    const res = await fetch(url, {}, 'get', undefined, { dataType: 'text' });
    if (res && res.failed) {
      Taro.atMessage({
        type: 'error',
        message: res.message,
      });
    } else {
      setContent(res);
    }
    setReady(true);
  };

  useEffect(() => {
    fetchContent();
  }, [id]);

  useEffect(() => {
    if (watermarkFlag) {
      store.fetchWatermark(spaceId);
    } else {
      store.setWatermarkInfo({ init: true });
    }
  }, [watermarkFlag, spaceId]);

  const handleImageView = (event) => {
    if (document.querySelector('.am-image-watermark')) {
      return;
    }
    const { target } = event;
    if (target.className === 'pswp__zoom-wrap') {
      const imageWatermark = document.createElement('div');
      imageWatermark.id = 'am-image-watermark';
      target.insertBefore(imageWatermark, target.lastChild);

      const { watermark, color, fontSize, spacing } = store.watermarkInfo;
      const waterMap = {
        content: watermark,
        allowRefresh: true,
        font: `${fontSize}px Microsoft Yahei`,
        color,
        spacing,
      };
      const watermarkDiv = document.createElement('div');
      watermarkDiv.className = 'am-image-watermark';
      addWatermark(watermarkDiv, waterMap);
      const container = document.querySelector('.pswp');
      container.appendChild(watermarkDiv);
    }
  };

  useEffect(() => {
    if (store.watermarkInfo.init) {
      if (watermarkFlag) {
        document.addEventListener('DOMNodeInserted', handleImageView);
      }
    }
    return () => {
      document.removeEventListener('DOMNodeInserted', handleImageView);
    };
  }, [watermarkFlag, store.watermarkInfo]);

  return (
    <div className={`${prefixCls}-am-preview`}>
      {ready && store.watermarkInfo.init ? (
        <AmEditor
          readonly
          nocopy={props?.knowledgeInfo?.copyFlag}
          engineRef={engineRef}
          defaultContent={content}
          tenantId={tenantId}
          afterRenderContent={afterRenderContent}
          watermarkInfo={watermarkFlag && store.watermarkInfo}
        />
      ) : (
        <Loading />
      )}
    </div>
  );
});

export default KnowledgeAmEditor;
