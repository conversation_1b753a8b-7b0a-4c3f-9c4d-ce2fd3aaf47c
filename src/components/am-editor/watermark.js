const getTextSize = (context, content, fontFamily, color) => {
  context.font = fontFamily;
  context.fillStyle = color;
  const width = context.measureText(context).width;
  const height = context.measureText('M').width + 0.08 * parseInt(fontFamily, 10);
  return { width, height };
};

const getWatermarkImageSrc = (config) => {
  const {
    content: text = '',
    font = '18px Microsoft Yahei',
    color = 'rgba(184, 184, 184, 0.4)',
    spacing = 30,
  } = config;

  const canvas = document.createElement('canvas');
  canvas.setAttribute('style', 'background:blue');
  const ctx = canvas.getContext('2d');

  const rotate = -24;
  const deg = Math.PI / 180;
  let { width, height } = getTextSize(ctx, text, font, color);
  width += spacing * 2;
  height += spacing * 2;

  canvas.setAttribute('width', width * Math.cos(Math.abs(rotate) * deg) + height * Math.sin(Math.abs(rotate) * deg));
  canvas.setAttribute('height', width * Math.sin(Math.abs(rotate) * deg) + height * Math.cos(Math.abs(rotate) * deg));
  ctx.translate(0, width * Math.sin(Math.abs(rotate) * deg) + height * Math.cos(Math.abs(rotate) * deg) - height * Math.cos(Math.abs(rotate) * deg));
  ctx.rotate(rotate * deg);
  ctx.font = font;
  ctx.fillStyle = color;
  ctx.fillText(text, 0, height * 0.82);
  const base64Url = canvas.toDataURL();
  return base64Url;
};

const addWatermark = (node, config) => {
  if (node.getAttribute('watermarkFlag')) {
    return;
  }
  const watermarkImageSrc = getWatermarkImageSrc(config);
  node.style.background = `url(${watermarkImageSrc}) repeat`;
  node.setAttribute('watermarkFlag', true);
};

export { getWatermarkImageSrc, addWatermark };
