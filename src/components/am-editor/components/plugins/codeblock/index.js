/* eslint-disable prefer-template */
import {
  $,
  Plugin,
  isEngine,
  CARD_KEY,
  CARD_VALUE_KEY,
  Parser,
  unescape,
  CARD_TYPE_KEY,
  READY_CARD_KEY,
  decodeCardValue,
  VIEW_CLASS_NAME,
} from '@aomao/engine';
import CodeBlockComponent, { CodeBlockEditor } from './component';

// 缩写替换
const MODE_ALIAS = {
  text: 'plain',
  sh: 'bash',
  ts: 'typescript',
  js: 'javascript',
  py: 'python',
  puml: 'plantuml',
  uml: 'plantuml',
  vb: 'basic',
  md: 'markdown',
  'c++': 'cpp',
  'c#': 'csharp',
};

export default class extends Plugin {
  static get pluginName() {
    return 'codeblock';
  }

  init() {
    this.editor.on('parse:html', this.parseHtml);
    this.editor.on('paste:schema', this.pasteSchema);
    this.editor.on('paste:each', this.pasteHtml);
    if (isEngine(this.editor) && this.markdown) {
      this.editor.on('keydown:enter', this.markdown);
      this.editor.on('paste:markdown-check', this.checkMarkdownMath);
      this.editor.on('paste:markdown-before', this.pasteMarkdown);
    }
  }

  execute(mode, value) {
    if (!isEngine(this.editor)) return;
    const { card } = this.editor;
    const component = card.insert(CodeBlockComponent.cardName, { mode, code: value });
    setTimeout(() => {
      component.focusEditor();
    }, 200);
  }

  hotkey() {
    return this.options.hotkey || '';
  }

  markdown = (event) => {
    if (!isEngine(this.editor) || this.options.markdown === false) return;
    const { change, node, command } = this.editor;
    const blockApi = this.editor.block;
    const range = change.range.get();

    if (!range.collapsed || change.isComposing() || !this.markdown) return;

    const block = blockApi.closest(range.startNode);

    if (!node.isRootBlock(block)) {
      return;
    }

    const chars = blockApi.getLeftText(block);
    const match = /^`{3,}(.*){0,20}$/.exec(chars);

    if (match) {
      const modeText = (undefined === match[1] ? '' : match[1])
        .trim()
        .toLowerCase();
      const alias = { ...(this.options.alias || {}), ...MODE_ALIAS };
      const mode = alias[modeText] || modeText;

      if (mode || mode === '') {
        event.preventDefault();
        blockApi.removeLeftText(block);
        command.execute(
          this.constructor.pluginName,
          mode,
        );
        block.remove();
        return false;
      }
    }
  };

  pasteSchema = (schema) => {
    schema.add([
      {
        type: 'block',
        name: 'pre',
        attributes: {
          'data-syntax': '*',
          class: '*',
          language: '*',
        },
      },
      {
        type: 'block',
        name: 'code',
        attributes: {
          'data-syntax': {
            required: true,
            value: '*',
          },
        },
      },
      {
        type: 'block',
        name: 'code',
        attributes: {
          language: {
            required: true,
            value: '*',
          },
        },
      },
      {
        type: 'block',
        name: 'code',
        attributes: {
          class: {
            required: true,
            value: '*',
          },
        },
      },
      {
        type: 'block',
        name: 'div',
        attributes: {
          'data-syntax': {
            required: true,
            value: '*',
          },
        },
      },
    ]);
  };

  pasteHtml = (node) => {
    if (!isEngine(this.editor) || node.isText()) return;
    if (node.get()?.hasAttribute('data-syntax') || node.name === 'pre') {
      let syntax = node.attributes('data-syntax');
      if (!syntax) {
        const getSyntaxForClass = (curNode) => {
          const classList = curNode?.get()?.classList;
          if (!classList) return;
          for (let i = 0; i < classList.length; i++) {
            const className = classList.item(i);
            if (className && className.startsWith('language-')) {
              const classArray = className.split('-');
              classArray.shift();
              return classArray.join('-');
            }
          }
          return undefined;
        };
        if (node.name === 'pre') {
          syntax = node.attributes('language');
          if (!syntax) {
            syntax = getSyntaxForClass(node);
          }
        }
        const code = node.find('code');
        if (!syntax && code.length > 0) {
          syntax = code.attributes('data-syntax') || code.attributes('language');
          if (!syntax) {
            syntax = getSyntaxForClass(code);
          }
        }
      }
      let code = new Parser(node, this.editor).toText(
        undefined,
        undefined,
        false,
      );
      code = unescape(code.replace(/\u200b/g, ''));
      this.editor.card.replaceNode(node, 'codeblock', {
        mode: syntax || 'plain',
        code,
      });
      node.remove();
      return false;
    }
    return true;
  };

  checkMarkdownMath = (child) => {
    return !this.checkMarkdown(child)?.match;
  };

  checkMarkdown = (node) => {
    if (!isEngine(this.editor) || !this.markdown || !node.isText()) return;
    const text = node.text();
    if (!text) return;
    const reg = /`{3,}/;
    const match = reg.exec(text);
    return {
      reg,
      match,
    };
  };

  pasteMarkdown = (node) => {
    const result = this.checkMarkdown(node);
    if (!result) return;
    const { match } = result;
    if (!match) return;
    const { card } = this.editor;

    let newText = '';
    const nameMaps = {};
    CodeBlockComponent.getModes().forEach((item) => {
      nameMaps[item.value] = item.name;
    });
    const langs = Object.keys(nameMaps)
      .concat(Object.keys(MODE_ALIAS))
      .concat(Object.keys(this.options.alias || {}))
      .sort((a, b) => (a.length > b.length ? -1 : 1));

    const createCodeblock = (nodes, mode = 'text') => {
      // 获取中间字符
      const codeText = nodes.join('\n');
      let code = unescape(codeText);

      if (code.endsWith('\n')) code = code.substr(0, code.length - 1);
      const tempNode = $('<div></div>');
      const carNode = card.replaceNode(
        tempNode,
        'codeblock',
        {
          mode,
          code,
        },
      );
      tempNode.remove();

      return carNode.get()?.outerHTML;
    };

    const rows = node.text().split(/\n|\r\n/);
    let nodes = [];
    let isCode = false;
    let mode = 'text';
    rows.forEach((row) => {
      let curMatch = /^(.*)`{3,}(\s)*$/.exec(row);
      if (curMatch && isCode) {
        nodes.push(curMatch[1]);
        newText += createCodeblock(nodes, mode) + '\n';
        mode = 'text';
        isCode = false;
        nodes = [];
        return;
      }
      curMatch = /^`{3,}(.*)/.exec(row);
      if (curMatch) {
        isCode = true;
        mode = langs.find((key) => curMatch && (curMatch[1] || '').trim().toLowerCase().indexOf(key) === 0) || 'text';
        const alias = { ...(this.options.alias || {}), ...MODE_ALIAS };
        mode = alias[mode] || mode;
      } else if (isCode) {
        nodes.push(row);
      } else {
        newText += row + '\n';
      }
    });
    if (nodes.length > 0) {
      newText += createCodeblock(nodes, mode) + '\n';
    }
    node.text(newText);
  };

  parseHtml = (root, callback) => {
    const results = [];
    root.find(
      `[${CARD_KEY}="${CodeBlockComponent.cardName}"],[${READY_CARD_KEY}="${CodeBlockComponent.cardName}"]`,
    ).each((cardNode) => {
      const node = $(cardNode);
      const card = this.editor.card.find(node);
      const value = card?.getValue() || decodeCardValue(node.attributes(CARD_VALUE_KEY));
      if (value) {
        node.empty();
        const synatxMap = {};
        CodeBlockComponent.getModes().forEach((item) => {
          synatxMap[item.value] = item.syntax;
        });
        const codeEditor = new CodeBlockEditor(this.editor, {
          synatxMap,
        });

        const content = codeEditor.container.find(
          '.data-codeblock-content',
        );
        content.css({
          border: '1px solid #e8e8e8',
          padding: '8px',
        });
        codeEditor.render(value.mode || 'plain', value.code || '');
        content.addClass(VIEW_CLASS_NAME);
        content.hide();
        document.body.appendChild(content[0]);
        content.traverse((curNode) => {
          if (curNode.type === Node.ELEMENT_NODE && (curNode.get()?.classList?.length || 0) > 0) {
            const element = curNode.get();
            const style = window.getComputedStyle(element);
            ['color', 'margin', 'padding', 'background'].forEach(
              (attr) => {
                element.style[attr] = style.getPropertyValue(attr);
              },
            );
          }
        });
        content.show();
        content.css('background', '#f9f9f9');
        node.append(content);
        node.removeAttributes(CARD_KEY);
        node.removeAttributes(CARD_TYPE_KEY);
        node.removeAttributes(CARD_VALUE_KEY);
        node.attributes('data-syntax', value.mode || 'plain');
        content
          .removeClass(VIEW_CLASS_NAME)
          .removeClass('data-codeblock-content');
        let newNode = node;
        if (callback) {
          newNode = callback(node, value);
          node.replaceWith(newNode);
        }
        results.push(newNode);
      } else node.remove();
    });
    return results;
  };

  destroy() {
    this.editor.off('parse:html', this.parseHtml);
    this.editor.off('paste:schema', this.pasteSchema);
    this.editor.off('paste:each', this.pasteHtml);
    if (isEngine(this.editor) && this.markdown) {
      this.editor.off('keydown:enter', this.markdown);
      this.editor.off('paste:markdown-check', this.checkMarkdownMath);
      this.editor.off('paste:markdown-before', this.pasteMarkdown);
    }
  }
}
export { CodeBlockComponent };
