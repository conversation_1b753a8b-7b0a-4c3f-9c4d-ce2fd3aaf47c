import {
  $,
  CardActiveTrigger,
  Card,
  CardType,
  isEngine,
  isServer,
} from '@aomao/engine';
import CodeBlockEditor from './editor';
import modeDatas from './mode';
import './index.css';

class CodeBlcok extends Card {
  static get cardName() {
    return 'codeblock';
  }

  static get cardType() {
    return CardType.BLOCK;
  }

  static get autoSelected() {
    return false;
  }

  static get singleSelectable() {
    return false;
  }

  static get lazyRender() {
    return true;
  }

  static getModes() {
    return modeDatas;
  }

  resize = () => {
    return this.codeEditor?.container.find('.data-codeblock-content');
  };

  #modeNameMap = {};

  #modeSynatxMap = {};

  init() {
    if (isServer) return;
    super.init();
    if (this.codeEditor) return;
    modeDatas.forEach((item) => {
      this.#modeNameMap[item.value] = item.name;
      this.#modeSynatxMap[item.value] = item.syntax;
    });
    this.codeEditor = new CodeBlockEditor(this.editor, {
      synatxMap: this.#modeSynatxMap,
      onSave: (mode, value) => {
        const oldValue = this.getValue();
        if (mode === oldValue?.mode && value === oldValue.code) return;
        this.setValue({ mode, code: value });
      },
      onMouseDown: () => {
        if (!this.activated) {
          this.editor.card.activate(this.root, CardActiveTrigger.MOUSE_DOWN);
        }
      },
      onUpFocus: (event) => {
        if (!isEngine(this.editor)) return;
        event.preventDefault();
        const { change, card } = this.editor;
        const range = change.range.get().cloneRange();
        const prev = this.root.prev();
        const cardComponent = prev ? card.find(prev) : undefined;
        if (cardComponent?.onSelectUp) {
          cardComponent.onSelectUp(event);
        } else if (prev) {
          card.focusPrevBlock(this, range, false);
          change.range.select(range);
        } else {
          this.focus(range, true);
          change.range.select(range);
          return;
        }
        this.activate(false);
        this.toolbarModel?.hide();
      },
      onDownFocus: (event) => {
        if (!isEngine(this.editor)) return;
        event.preventDefault();
        const { change, card } = this.editor;
        const range = change.range.get().cloneRange();
        const next = this.root.next();
        const cardComponent = next ? card.find(next) : undefined;
        if (cardComponent?.onSelectDown) {
          cardComponent.onSelectDown(event);
        } else if (next) {
          card.focusNextBlock(this, range, false);
          change.range.select(range);
        } else {
          this.focus(range, false);
          change.range.select(range);
          return;
        }
        this.activate(false);
        this.toolbarModel?.hide();
      },
      onLeftFocus: (event) => {
        if (!isEngine(this.editor)) return;
        event.preventDefault();
        const { change } = this.editor;
        const range = change.range.get().cloneRange();
        this.focus(range, true);
        change.range.select(range);
        this.activate(false);
        this.toolbarModel?.hide();
      },
      onRightFocus: (event) => {
        if (!isEngine(this.editor)) return;
        event.preventDefault();
        const { change } = this.editor;
        const range = change.range.get().cloneRange();
        this.focus(range, false);
        change.range.select(range);
        this.activate(false);
        this.toolbarModel?.hide();
      },
    });
  }

  #viewAutoWrap;

  focusEditor() {
    this.codeEditor?.focus();
    this.editor.card.activate(this.root);
  }

  onSelectLeft(event) {
    if (!this.codeEditor) return;
    event.preventDefault();
    this.codeEditor.select(false);
    this.activate(true);
    this.toolbarModel?.show();
  }

  onSelectRight(event) {
    if (!this.codeEditor) return;
    event.preventDefault();
    this.codeEditor.select(true);
    this.activate(true);
    this.toolbarModel?.show();
  }

  onSelectDown(event) {
    if (!this.codeEditor) return;
    event.preventDefault();
    this.codeEditor.focus();
    this.activate(true);
    this.toolbarModel?.show();
  }

  onSelectUp(event) {
    if (!this.codeEditor) return;
    event.preventDefault();
    this.codeEditor.focus();
    this.activate(true);
    this.toolbarModel?.show();
  }

  render() {
    if (!this.codeEditor) return;
    if (!this.codeEditor.container.inEditor()) {
      this.codeEditor.container = $(this.codeEditor.renderTemplate());
      this.mirror = undefined;
      this.getCenter().empty().append(this.codeEditor.container);
    }
    const value = this.getValue();

    const mode = value?.mode || 'plain';
    const code = value?.code || '';
    if (isEngine(this.editor)) {
      if (this.mirror) {
        this.codeEditor.update(mode, code);
        this.codeEditor.setAutoWrap(!!value?.autoWrap);
        return;
      }
      setTimeout(() => {
        this.mirror = this.codeEditor?.create(mode, code, {
          lineWrapping: !!value?.autoWrap,
        });
      }, 50);
    } else {
      this.codeEditor?.create(mode, code, {
        lineWrapping: !!value?.autoWrap,
      });
    }
  }
}

export default CodeBlcok;
export { CodeBlockEditor };
