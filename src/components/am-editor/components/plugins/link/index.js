import {
  $,
  InlinePlugin,
  isEngine,
} from '@aomao/engine';
import './index.css';

export default class extends InlinePlugin {
  static get pluginName() {
    return 'link';
  }

  attributes = {
    target: '@var0',
    href: '@var1',
  };

  variable = {
    '@var0': ['_blank', '_parent', '_top', '_self'],
    '@var1': {
      required: true,
      value: '*',
    },
  };

  tagName = 'a';

  markdown =
    this.options.markdown === undefined
      ? '[^!]\\[(.+?)\\]\\(\\s*([\\S]+?)\\s*\\)$'
      : this.options.markdown;

  init() {
    super.init();
    const editor = this.editor;
    editor.on('paste:each', this.pasteHtml);
    editor.on('parse:html', this.parseHtml);
    editor.on('select', this.bindQuery);
  }

  hotkey() {
    return this.options.hotkey || { key: 'mod+k', args: ['_blank'] };
  }

  execute(...args) {
    if (!isEngine(this.editor)) return;
    const { inline, change } = this.editor;
    if (!this.queryState()) {
      const inlineNode = $(`<${this.tagName} />`);
      this.setStyle(inlineNode, ...args);
      this.setAttributes(inlineNode, ...args);
      const text = args.length > 2 ? args[2] : '';

      if (text) {
        inlineNode.text(text);
        inline.insert(inlineNode);
      } else {
        inline.wrap(inlineNode);
      }
      const range = change.range.get();
      if (!range.collapsed && change.inlines.length > 0) {
        this.toolbar?.show(change.inlines[0]);
      }
    } else {
      const inlineNode = change.inlines.find((node) => this.isSelf(node));
      if (inlineNode && inlineNode.length > 0) {
        inline.unwrap(inlineNode);
      }
    }
  }

  bindQuery = () => {
    this.query();
  };

  query = () => {
    if (!isEngine(this.editor)) return;
    const { change } = this.editor;
    const inlineNode = change.inlines.find((node) => this.isSelf(node));
    this.toolbar?.hide(inlineNode);
    if (inlineNode && inlineNode.length > 0 && !inlineNode.isCard()) {
      const range = change.range.get();
      if (
        range.collapsed
          || (inlineNode.contains(range.startNode)
          && inlineNode.contains(range.endNode))
      ) {
        this.toolbar?.show(inlineNode);
        return true;
      } else {
        this.toolbar?.hide();
      }
    }
    return false;
  };

  queryState() {
    return this.query();
  }

  triggerMarkdown(event, text, node) {
    const editor = this.editor;
    if (!isEngine(editor) || !this.markdown) return;
    const match = new RegExp(this.markdown).exec(text);
    if (match) {
      const { command } = editor;
      event.preventDefault();
      const matchText = match[1];
      const url = match[2];
      // 移除 markdown 语法
      const markdownTextNode = node
        .get()
        .splitText(node.text().length - match[0].length);
      markdownTextNode.splitText(match[0].length);
      $(markdownTextNode).remove();
      command.execute(
        this.constructor.pluginName,
        '_blank',
        url,
        matchText,
      );
      editor.node.insertText('\xA0');
      return false;
    }
  }

  checkMarkdown = (node) => {
    if (!isEngine(this.editor) || !this.markdown || !node.isText()) return;

    const text = node.text();
    if (!text) return;

    const reg = /(\[(.+?)\]\(\s*([\S]+?)\s*\))/;
    const match = reg.exec(text);
    return {
      reg,
      match,
    };
  };

  pasteMarkdown = (node) => {
    const result = this.checkMarkdown(node);
    if (!result) return;
    const { reg } = result;
    let { match } = result;
    if (!match) return;

    let newText = '';
    let textNode = node.clone(true).get();
    /* eslint-disable-next-line */
    while (
      textNode.textContent
        && (match = reg.exec(textNode.textContent))
    ) {
      // 从匹配到的位置切断
      const regNode = textNode.splitText(match.index);
      if (
        textNode.textContent.endsWith('!')
          || match[2].startsWith('!')
      ) {
        newText += textNode.textContent;
        textNode = regNode.splitText(match[0].length);
        newText += regNode.textContent;
      } else {
        newText += textNode.textContent;
        // 从匹配结束位置分割
        textNode = regNode.splitText(match[0].length);

        const text = match[2];
        const url = match[3];

        const inlineNode = $(`<${this.tagName} />`);
        this.setAttributes(inlineNode, '_blank', (url || '').trim());
        inlineNode.html(text || url);

        newText += inlineNode.get()?.outerHTML;
      }
    }
    newText += textNode.textContent;
    node.text(newText);
  };

  parseHtml = (root) => {
    root.find(this.tagName).css({
      'font-size': 'inherit',
      padding: '0 2px',
      'line-height': 'inherit',
      'overflow-wrap': 'break-word',
      'text-indent': '0',
    });
  };

  pasteHtml = (child) => {
    if (child.isText()) {
      const text = child.text();
      const { node, inline } = this.editor;
      if (
        /^https?:\/\/\S+$/.test(text.toLowerCase().trim())
          && !inline.closest(child).equal(child)
      ) {
        node.wrap(
          child,
          $(`<${this.tagName} target="_blank" href="${text}"></a>`),
        );
        return false;
      }
    }
    return true;
  };

  destroy() {
    super.destroy?.();
    this.editor.off('paste:each', this.pasteHtml);
    this.editor.off('parse:html', this.parseHtml);
    this.editor.off('select', this.bindQuery);
  }
}
