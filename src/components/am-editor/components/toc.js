/* eslint-disable react-hooks/exhaustive-deps */
import { useRef, useEffect, useState, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import { useIntl } from '@/util/intl';
import { $ } from '@aomao/engine';
import { Outline } from '@aomao/plugin-heading';
import Empty from '@/components/yq-empty';
import './toc.scss';

const outline = new Outline();

function Toc({ engine, afterClick = () => {} }) {
  const rootRef = useRef(null);
  const [datas, setDatas] = useState([]);
  const intl = useIntl();

  useEffect(() => {
    const onChange = () => {
      // 获取大纲数据
      const data = getTocData();
      setDatas(data);
    };
    // 绑定编辑器值改变事件
    engine.on('change', onChange);
    setTimeout(() => {
      onChange();
    }, 50);
    return () => engine.off('change', onChange);
  }, [engine]);

  const getTocData = useCallback(() => {
    // 从编辑区域提取符合结构要求的标题 Dom 节点
    const nodes = [];
    const { card } = engine;
    engine.container.find('h1,h2,h3,h4,h5,h6').each((child) => {
      const node = $(child);
      // Card 里的标题，不纳入大纲
      if (card.closest(node)) {
        return;
      }
      // 非一级深度标题，不纳入大纲
      if (!node.parent()?.isRoot()) {
        return;
      }
      nodes.push(node.get());
    });
    return outline.normalize(nodes);
  }, []);

  return (
    <div className="data-toc-wrapper">
      <div className="data-toc" ref={rootRef}>
        {datas.length && datas.length > 0 ? datas.map((data) => (
          <span
            key={data.id}
            href={`#${data.id}`}
            className={`data-toc-item data-toc-item-${data.depth}`}
            onClick={() => {
              const content = document.getElementsByClassName('knowledge-detail-content')[0];
              const head = document.getElementsByClassName('knowledge-detail-head')[0];
              content.scrollTop = head.clientHeight + document.querySelector(`#${data.id}`).offsetTop;
              afterClick();
            }}
          >
            {data.text}
          </span>
        )) : (
          <Empty />
        )}
      </div>
    </div>
  );
}

export default observer(Toc);
