/* eslint-disable import/no-commonjs */
/** ****************************************************************************
 *  @文件名: watermark.js
 *  @作  者: 20524-xiaoreya
 *  @修改人: xiaoreya
 *  @说  明: 给网页添加水印
 *  @感  受: 脑壳疼，没得味
 ***************************************************************************** */

// 抛抛抛，抛就完事了
Object.defineProperty(exports, '__esModule', {
  value: true,
});

const _react = require('react');

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

// 备用
const _react2 = _interopRequireDefault(_react);

// svg 实现 watermark
const watermark = function watermark({ container, font, fillStyle, content, rotate, zIndex, props, allowRefresh, cloneElement, spacing }) {
  const refContainer = container;
  const refFillStyle = fillStyle === undefined ? 'rgba(184, 184, 184, 0.4)' : fillStyle;
  const refContent = content === undefined ? 'undefind' : content;
  const refRotate = rotate === undefined ? '-24' : rotate;
  const refCIndex = zIndex === undefined ? 900 : zIndex;
  const spacings = spacing === undefined ? 30 : spacing;

  /*  if (!refContainer) {
      console.error('水印容器缺失但是必须')
    } */

  let fontSize; let 
    fontFamily;
  const arr = font.split('px ');
  if (arr.length) {
    fontSize = arr[0];
    fontFamily = arr[1];
  }

  const config = {
    text: refContent,
    rotate: refRotate,
    color: refFillStyle,
    fontSize,
    fontFamily,
    refCIndex,
    refContainer,
    props,
    allowRefresh,
    cloneElement,
    top: spacings,
    left: spacings,
  };
  return autoWaterMark(config);
};

/**
 * 自适应水印字
 *
 *   根据文字大小，旋转角度自动撑大canvas块，渲染水印
 *
 *   参数
 *   config: {
 *      text: 水印字文本
 *      rotate: 旋转角度
 *      color: 水印字颜色
 *      fontSize: 水印字字号大小
 *      fontFamily: 水印字字体
 *      left: 左右间隙 px
 *      top: 上下间隙 px
 *
 *      refContainer: 包裹元素
 *      refCIndex
 *      props
 *   }
 *
 * create by jyjin
 *      at 2019.06.18
 * @param {} text
 * @param {*} rotate
 * @param {*} color
 * @param {*} fontSize
 * @param {*} fontFamily
 */
const autoWaterMark = function (config) {
  const text = config.text || '';
  let rotate = config.rotate || '45';
  const ua = window.navigator.userAgent;
  let color = '';
  if (!!window.ActiveXObject || 'ActiveXObject' in window || ua.indexOf('Edge/') > 0) {
    // color = '#F2F6FA';
    color = '#dce3ec';
    // color = 'rgb(220,227,236,0.4)';
  } else {
    color = config.color || 'rgba(184,184,184,0.4)';
  }

  const fontSize = config.fontSize === 'undefined' ? 18 : config.fontSize;
  const fontFamily = config.fontFamily || 'Microsoft Yahei';
  const refContainer = config.refContainer;
  const refCIndex = config.refCIndex;
  const top = config.top || 30;
  const left = config.left || 30;
  const props = config.props;
  const allowRefresh = config.allowRefresh;
  const cloneElement = config.cloneElement;

  const canvas = document.createElement('canvas');
  drawWaterPrint(rotate);

  function getCtx() {
    canvas.setAttribute('style', 'background:blue');
    const ctx = canvas.getContext('2d');
    return ctx;
  }

  // 测量字体宽度
  function getTextProperty(textParam, fontParam, colorParam) {
    const c0 = getCtx();
    c0.font = fontParam;
    c0.fillStyle = colorParam;
    // eslint-disable-next-line radix
    const height = c0.measureText('M').width + 0.08 * parseInt(fontParam);
    const width = c0.measureText(textParam).width;
    return {
      width,
      height,
    };
  }

  function drawWaterPrint() {
    rotate = 0 - (Math.abs(rotate % 360));
    const deg = Math.PI / 180;
    const font = `${fontSize}px ${fontFamily}`;
    let {
      width,
      height,
    } = getTextProperty(text, font, color);
    width += left * 2;
    height += top * 2;

    const el = canvas;
    const c1 = el.getContext('2d');
    if (rotate <= 0 && rotate > -90) {
      el.setAttribute('width', width * Math.cos(Math.abs(rotate) * deg) + height * Math.sin(Math.abs(rotate) * deg));
      el.setAttribute('height', width * Math.sin(Math.abs(rotate) * deg) + height * Math.cos(Math.abs(rotate) * deg));
      c1.translate(0, width * Math.sin(Math.abs(rotate) * deg) + height * Math.cos(Math.abs(rotate) * deg) - height
        * Math
          .cos(Math.abs(rotate) * deg));
    } else if (rotate <= -90 && rotate > -180) {
      el.setAttribute('width', width * Math.cos(Math.abs(180 + rotate) * deg) + height * Math.sin(Math.abs(180
        + rotate) * deg));

      el.setAttribute('height', width * Math.sin(Math.abs(180 + rotate) * deg) + height * Math.cos(Math.abs(180
        + rotate) * deg));
      c1.translate(width * Math.cos(Math.abs(180 + rotate) * deg), width * Math.sin(Math.abs(180 + rotate) * deg)
        + height
        * Math
          .cos(Math.abs(180 + rotate) * deg));
    } else if (rotate <= -180 && rotate > -270) {
      el.setAttribute('width', width * Math.cos(Math.abs(rotate + 180) * deg) + height * Math.sin(Math.abs(
        rotate + 180
      ) * deg));

      el.setAttribute('height', width * Math.sin(Math.abs(rotate + 180) * deg) + height * Math.cos(Math.abs(
        rotate + 180
      ) * deg));
      c1.translate(width * Math.cos(Math.abs(rotate + 180) * deg) + height * Math.sin(Math.abs(
        rotate + 180
      ) * deg),
      height
        * Math
          .cos(Math.abs(rotate + 180) * deg));
    } else {
      el.setAttribute('width', height * Math.sin(Math.abs(rotate + 360) * deg) + width * Math.cos(Math.abs(rotate
        + 360) * deg));
      el.setAttribute('height', height * Math.cos(Math.abs(rotate + 360) * deg) + width * Math.sin(Math.abs(rotate
        + 360) * deg));
      c1.translate(height * Math.sin(Math.abs(rotate + 360) * deg), 0);
    }
    c1.rotate(rotate * deg);

    c1.font = font;
    c1.fillStyle = color;
    c1.fillText(text, 0, height * 0.82);

    const base64Url = canvas.toDataURL();
    // const styleStr = `background:url(${base64Url}) repeat;background-color: #ffffff;`
    const styleStr = `background:url(${base64Url}) repeat;`;
    const id = refContainer.getAttribute('id');
    const $ = (cloneElement || document);
    $.querySelector(`#${id}`).setAttribute('style', styleStr);

    const MutationObserver = window.MutationObserver || window.WebKitMutationObserver;
    if (MutationObserver) {
      let mo = new MutationObserver((() => {
        const __wm = refContainer;
        // 只在__wm元素变动才重新调用 __canvasWM
        if ($.querySelector(`#${id}`) && $.querySelector(`#${id}`).getAttribute('style') !== styleStr && !allowRefresh) {
          // 避免一直触发
          mo.disconnect();
          mo = null;
          autoWaterMark(config);
        }
      }));

      mo.observe(refContainer, {
        attributes: true,
        subtree: true,
        childList: true,
      });
    }
  }
};

exports.default = watermark;
