import watermarkFunc from '@/components/am-editor/components/watermark';

/**
 * js添加水印
 * @param id DOM的id节点
 * @param content 水印内容
 * @param rotate 偏转角度
 * @param props 判断是否来自于水印配置页面，是否需要实时重复刷新
 * @param height 水印高度
 * @param width 水印宽度
 * @param font 水印字体
 * 更多api参考watermark.md文件
 */
function addWatermark(id, { content, props, height, width, font, allowRefresh, color, spacing }, cloneElement) {
  watermarkFunc({
    content: `${content}`,
    container: (cloneElement && cloneElement.querySelector(`#${id}`)) || document.getElementById(id),
    rotate: '-24',
    props,
    font: font ? `${font}` : '18px Microsoft Yahei',
    height: height ? `${height}` : '350px',
    width: width ? `${width}` : '400px',
    allowRefresh,
    fillStyle: color,
    cloneElement,
    spacing,
  });
}
export default addWatermark;
