/* eslint-disable react-hooks/exhaustive-deps */
import { useRef, useEffect, useState, useCallback } from 'react';
import { observer } from 'mobx-react-lite';


const Toc = ({ engine, afterClick = () => {} }) => {
  const rootRef = useRef(null);
  const [datas, setDatas] = useState([]);

  return (
    <div className='data-toc-wrapper'>
      <div className='data-toc' ref={rootRef}>

      </div>
    </div>
  );
};

export default observer(Toc);
