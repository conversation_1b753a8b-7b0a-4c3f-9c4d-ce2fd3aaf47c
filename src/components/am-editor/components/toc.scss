.data-toc-wrapper {
    padding: 0 30px;
}

.data-toc-title {
    position: relative;
    margin-bottom: .1rem;
    font-size: .14rem;
    font-weight: 700;
    padding: 0 2px 8px;
    border-bottom: 1px solid #e8e8e8;
}

.data-toc{
    overflow: auto;
}
.data-toc .data-toc-item {
  height: 56px;
  font-size: 28px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(18,39,77,0.85);
  line-height: 56px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    cursor: pointer;
  }
  .data-toc .data-toc-item-active,
  .data-toc .data-toc-item:hover,
  .data-toc .data-toc-item:focus {
    color: #1890FF;
    text-decoration: none;
  }
  .data-toc .data-toc-item-2 {
    padding-left: 24px;
  }
  .data-toc .data-toc-item-3 {
    padding-left: 48px;
  }
  .data-toc .data-toc-item-4 {
    padding-left: 72px;
  }
  .data-toc .data-toc-item-5 {
    padding-left: 96px;
  }
  .data-toc .data-toc-item-6 {
    padding-left: 120px;
  }
