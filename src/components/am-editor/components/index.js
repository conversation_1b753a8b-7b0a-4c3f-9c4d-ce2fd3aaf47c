/* eslint-disable react-hooks/exhaustive-deps */
import { useRef, useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import classNames from 'classnames';
import Engine from '@aomao/engine';
import { ICON_SERVER } from '@config';
import Taro from '@tarojs/taro';
import appStore from '@/store/app';
import { _, fetch } from '@/util';
import { plugins, cards, uploadConfig, pluginConfig } from './config';
import './index.scss';

function AmEditor(props) {
  const {
    engineRef,
    defaultContent,
    tenantId = 0,
    readonly = false,
    watermarkInfo, // watermarkInfo默认值不能为空对象，否则当没有水印时下面的useEffect会无限执行
    afterRenderContent = () => {},
  } = props;

  const ref = useRef(null);
  const [engineInstance, setEngine] = useState();
  const [content, setContent] = useState(defaultContent);
  const [base64Url, setBase64Url] = useState('');

  useEffect(() => {
    if (!ref.current) return;
    const accessToken = appStore.accessToken || _.getCookie('access_token');
    const uploadProps = uploadConfig(tenantId, accessToken);
    const engine = new Engine(
      ref.current,
      {
        plugins,
        cards,
        readonly,
        config: {
          ...uploadProps,
          ...pluginConfig,
        },
      },
    );
    engineRef.current = engine;
    window.amEditor = {
      engine,
      defaultContent: content,
    };
    engine.on('change', () => {
      const value = engine.getValue();
      setContent(value);
    });
    if (!_.isEmpty(watermarkInfo) && !_.isEmpty(watermarkInfo.watermark)) {
      const { watermark: _watermark, color, fontSize } = watermarkInfo;
      const spacing = Taro.getSystemInfoSync().windowWidth;
      const waterMap = {
        content: _watermark,
        allowRefresh: false,
        font: `${fontSize}px Microsoft Yahei`,
        color,
        spacing: spacing / 10,
      };
      fetch(`${ICON_SERVER}/v1/get_watermark`, waterMap, 'post', true, { dataType: 'text' }).then(res => {
        setBase64Url(res);
      });
    }
    if (!readonly) {
      engine.focus();
    }
    setEngine(engine);
    afterRenderContent(engine);
    return () => {
      engine.destroy();
    };
  }, [watermarkInfo]);

  useEffect(() => {
    if (engineInstance) {
      engineInstance.setHtml(defaultContent);
    }
  }, [defaultContent, engineInstance]);

  return (
    <div
      className={classNames({
        ameditor: true,
        'ameditor-editable': !readonly,
        'ameditor-readonly': readonly,
      })}
    >
      <div className="ameditor-main">
        <div
          ref={ref}
          id="am-editor"
          className={classNames({
            'am-engine': true,
            nocopy: !props.nocopy,
          })}
        />
        {!_.isEmpty(watermarkInfo) && !_.isEmpty(watermarkInfo.watermark) && (
          <div style={{ background: `url('${base64Url}') repeat` }} id="am-editor-watermark" className="ameditor-watermark" />
        )}
      </div>
    </div>
  );
}

export default observer(AmEditor);
