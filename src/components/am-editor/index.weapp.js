import React, { useEffect, useRef, useState } from 'react';
import { observer } from 'mobx-react-lite';
import Taro from '@tarojs/taro';
import { site, ICON_SERVER } from '@config';
import { injectIntl } from '@/util/intl';
import { Loading } from '@taroify/core';
import { _, fetch } from '@/util';
import DOMPurify from '@/util/dompurify';
import { asyncFetch } from '../../util/fetch';

import './index.scss';
// import { sanitize } from '../../util/quill-util';
const { sanitize } = DOMPurify;

function KnowledgeAmEditor(props) {
  const {
    knowledgeInfo: { publishStatus, id, spaceId },
    prefixCls = 'knowledge-center',
    detailUtilStore,
    tenantId,
    afterRenderContent = () => {},
    intl,
  } = props;

  const [ready, setReady] = useState(false);
  const [content, setContent] = useState('');
  const [watermark, setWatermark] = useState('');

  async function fetchWatermark() {
    if (!spaceId) {
      setWatermark(true);
      return;
    }
    const res = await fetch(`knowledge/v1/${tenantId}/know/watermarks/map?spaceId=${spaceId}`).catch(() => {});
    if (!res || res.failed) {
      Taro.atMessage({
        type: 'error',
        message: !res ? intl.formatMessage({ id: 'yqc.mobile.api.wrong', defaultMessage: '接口出现报错，请联系管理员' }) : res.message,
      });
    } else {
      const { watermark: _watermark, color, fontSize } = res;
      const spacing = Taro.getSystemInfoSync().windowWidth;
      const waterMap = {
        content: _watermark,
        allowRefresh: false,
        font: `${fontSize}px Microsoft Yahei`,
        color,
        spacing: spacing / 10,
      };
      const watermarkBase64 = await asyncFetch(`${ICON_SERVER}/v1/get_watermark`, waterMap, 'post', true, { dataType: 'text' });
      if (watermarkBase64.startsWith('data')) {
        setWatermark(watermarkBase64);
      }
    }
  }

  const fetchContent = async () => {
    const version = publishStatus === 'PUBLISHED' ? 'version=PUBLISHED' : '';
    const url = `knowledge/v1/${tenantId}/data?id=${id}&${version}`;
    const res = await fetch(url, {}, 'get', undefined, { dataType: 'text' });
    if (res && res.failed) {
      Taro.atMessage({
        type: 'error',
        message: res.message,
      });
    } else {
      setContent(res);
    }
    setReady(true);
  };

  useEffect(() => {
    fetchContent();
    fetchWatermark();
  }, [id]);

  return (
    <div className={`${prefixCls}-am-preview`}>
      {ready && watermark ? (
        // eslint-disable-next-line react/no-danger
        <div dangerouslySetInnerHTML={{ __html: sanitize(content) }} />
      ) : (
        <Loading />
      )}
      <div style={{ background: `url('${watermark}') repeat` }} id="am-editor-watermark" className="ameditor-watermark" />
    </div>
  );
}

export default injectIntl(observer(KnowledgeAmEditor));
