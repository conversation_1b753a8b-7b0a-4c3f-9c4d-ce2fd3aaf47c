.knowledge-center-am-preview {
  position: relative;
  padding: 0 30px !important;
  .am-engine {
    min-height: 3.5rem;
    margin-bottom: .24rem;
  }

  p {
    text-indent: 0 !important;
  }
}
.ameditor-watermark {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  pointer-events: none;
  z-index: 1;
}

.h5-pre {
  overflow: auto;
}
#am-image-watermark {
  position: absolute;
  z-index: 1;
  width: 100vh !important;
  height: 200vh !important;
  top: -100vh !important;
}

.am-image-watermark {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  pointer-events: none;
  z-index: 1;
}
