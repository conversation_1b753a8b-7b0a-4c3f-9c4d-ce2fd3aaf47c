import React, { useEffect, useState, useMemo } from 'react';
import Taro from '@tarojs/taro';
import { ScrollView, View } from '@tarojs/components';
import { observer } from 'mobx-react-lite';
import { DropdownMenu } from '@taroify/core';
import classnames from 'classnames';
import { useIntl } from '@/util/intl';
import styles from '@/components/service-catalog/ServiceCatalog.module.less';
import { _ } from '@/util';
import appStore from '@/store/app';
import useCommonPage from '@/hooks/use-common-page';
import YqEmpty from '@/components/yq-empty';
import cascaderStyles from './Cascader.module.less';

function PopupContent({ hasBorder, selectedPath, index, onClick, url, spaceId, parent }) {
  const params = useMemo(() => Taro.getCurrentInstance().router?.params, []);
  let postParams = {};
  if (params?.shareId) {
    postParams = { spaceId, folderId: parent };
  } else {
    postParams = { spaceId, folderId: parent === spaceId ? 0 : parent };
  }
  const { data, setSize, size, hasMore, isEmpty } = useCommonPage(url.url, postParams, url.method);
  const intl = useIntl();
  useEffect(() => {
    if (process.env.TARO_ENV === 'h5') {
      if (selectedPath.length === index + 1) {
        const scrollDom = document.querySelector('#yq-cascader-scroll');
        if (scrollDom) {
          scrollDom.scrollLeft = 999999;
        }
      }
    }
  }, [selectedPath, index, data]);
  if (isEmpty) {
    if (appStore.isPublicUrl) return null;
    return <YqEmpty />;
  }
  return (
    <ScrollView key="scrollView" scrollY style={{ height: _.pxToRem(900) }} lowerThreshold={400} onScrollToLower={() => hasMore && setSize(size + 1)} className={classnames(styles.popupListCol, { [styles.isFirst]: index === 0 })}>
      {
        !params?.shareType && (
          <View className={classnames(styles.popupListColItem, styles.hasBorder)} key="ALL" onClick={() => onClick({ type: 'ALL', spaceId, parent })}>
            <View className={styles.popupListColItemText}>{intl.formatMessage({ id: 'yqc.mobile.all', defaultMessage: '全部' })}</View>
          </View>
        )
      }
      {Array.isArray(data) && data.length > 0 && data.filter(item => {
        if (!params?.shareType) {
          return true;
        } else if (params?.shareType === 'space' && index === 0) {
          return item.id === params?.spaceId;
        } else if (params?.shareType === 'folder' && hasBorder) {
          return item.id === params?.folderId;
        } else {
          return true;
        }
      }).map(item => {
        const rowCls = { [styles.hasBorder]: hasBorder, [styles.checked]: selectedPath.includes(item.id) };
        const classNames = classnames(styles.popupListColItem, rowCls);
        return (
          <View className={classNames} key={item.id} onClick={() => onClick(item, index)}>
            <View className={styles.popupListColItemText}>{item.name}</View>
          </View>
        );
      })}
      {hasBorder && <View className={styles.restCol} />}
    </ScrollView>
  );
}

function YqCascaderBreadcrumb({
  defaultPath = ['ROOT'],
  title,
  urls,
  onClick,
}) {
  const [scrollLeft, setScrollLeft] = useState(0.1);
  const [selectedPath, setSelectedPath] = useState<string[]>(defaultPath);
  const params = useMemo(() => Taro.getCurrentInstance().router?.params, []);
  useEffect(() => {
    setSelectedPath(defaultPath);
  }, [defaultPath]);
  function handleCatalogClick(item: any, index: number) {
    onClick?.(item);
    selectedPath[index + 1] = item.id;
    setSelectedPath([...selectedPath.slice(0, index + 2)]);
    if (process.env.TARO_ENV === 'h5') {
      const scrollDom = document.querySelector('#yq-cascader-scroll');
      if (scrollDom) {
        scrollDom.scrollLeft = 999999;
      }
    } else {
      setScrollLeft(999999);
    }
  }
  const renderPopupContent = (parent: string, index: number, arr: Array<any>): React.ReactElement => {
    const hasBorder = index < arr.length - 1;
    return (
      <PopupContent key={parent} spaceId={params?.spaceId} parent={parent} hasBorder={hasBorder} index={index} selectedPath={selectedPath} onClick={handleCatalogClick} url={index === 0 ? urls[0] : urls[1] || urls[0]} />
    );
  };
  return (
    <DropdownMenu className={classnames(styles.popup, cascaderStyles.popup)}>
      <DropdownMenu.Item
        value="list"
        title={title}
      >
        <ScrollView id="yq-cascader-scroll" key="list" scrollX scrollLeft={scrollLeft} className={styles.popupContent}>
          {selectedPath.map(renderPopupContent)}
        </ScrollView>
      </DropdownMenu.Item>
    </DropdownMenu>
  );
}

export default observer(YqCascaderBreadcrumb);
