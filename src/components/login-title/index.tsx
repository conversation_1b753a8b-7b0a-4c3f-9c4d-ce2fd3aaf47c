import React, { useEffect } from 'react';
import { View } from '@tarojs/components';
import classNames from 'classnames';
import appStore from '@/store/app';
import YqIcon from '@/components/yq-icon';
import YqTooltip from '@/components/yq-tooltip';
import { _ } from '@/util';
import { LoginTitleProps } from './LoginTitle';
import styles from './LoginTitle.module.less';

function LoginTitle(props: LoginTitleProps): React.ReactElement {
  const {
    title = '',
    brief = '',
    titleClassNames = '',
    briefClassNames = '',
    titleStyle = {},
    briefStyle = {},
  } = props;
  const supportLanguage = {
    // FIXME: 待修改成从租户信息中获取支持的多语言
    // eslint-disable-next-line no-chinese/no-chinese
    zh_CN: '简体中文',
    en_US: 'English',
  };
  function handleChangeLanguage(newLanguage) {
    appStore.language = newLanguage;
    _.setLocalStorage('language', newLanguage);
    appStore.changeLanguage(newLanguage);
  }
  return (
    <React.Fragment>
      {title && <View className={classNames([styles.title, titleClassNames])} style={titleStyle}>
        <View>
          {title}
        </View>
        <View className={styles.language}>
          <YqTooltip
            title={(
              <View onClick={() => handleChangeLanguage(appStore.language === 'zh_CN' ? 'en_US' : 'zh_CN')}>
                {appStore.language === 'zh_CN' ? supportLanguage.en_US : supportLanguage.zh_CN}
              </View>
            )}
            placement="bottomRight"
            clickToClose
          >
            <View className={styles.right}>
              <View>{appStore.language === 'en_US' ? supportLanguage.en_US : supportLanguage.zh_CN}</View>
              <YqIcon type="Down" theme="outline" style={{ marginBottom: 6 }} size={40} fill="#595959" />
            </View>
          </YqTooltip>
        </View>
      </View>}
      {brief && <View className={classNames([styles.brief, briefClassNames])} style={briefStyle}>{brief}</View>}
    </React.Fragment>
  );
}

export default LoginTitle;
