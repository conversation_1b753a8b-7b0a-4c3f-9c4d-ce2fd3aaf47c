import { observer } from 'mobx-react-lite';
import PageLoader from '@/components/page-loader';
import VariableTable from '@/components/variable-table';
import MobileTable from '@/components/mobile-table';
import Wysiwyg from '@/components/wysiwyg';
import YqDateRange from '@/components/yq-date-range';
import CustomComponent from '@/components/custom-component';
import YqDynamic from '@/components/yq-dynamic';
import YqReply from '@/components/yq-reply';
import YqAttachment from '@/components/yq-attachment';
import YqCamera from '@/components/yq-camera';
import DataTime from '@/components/datetime';
import MbRadio from '@/components/mb-radio';
import ServiceItemPage from '@/components/service-item-page';
import RequestContent from '@/components/request-content';
import StageExpand from '@/components/stage-expand';
import TicketSla from '@/components/ticket-sla';
import LocationSelect from '@/components/location-select';
import Input from '@/components/input';
import { _ } from '@/util';
import UrlButton from '@/components/url-button';
import SubTask from '@/components/sub-task';
import WorkTimeRelated from '@/components/work-time-related';
import YqPage from '@/components/yq-page';
import GlobalTicketPage from '../../../packageOther/pages/global-ticket-page';
import { BasicProps, FieldItemProps } from '../ts';
import { getApiParam } from '../utils';

function GlobalTicket(props: BasicProps) {
  return (
    <div>
      <GlobalTicketPage ticketId={props.ticketId} type="component" />
    </div>
  );
}
function FieldLocation(props: BasicProps) {
  return <LocationSelect {...props} />;
}
function FieldTicketSLA(props: BasicProps) {
  return <TicketSla {...props} />;
}
function FieldStageExpand(props: BasicProps) {
  return <StageExpand {...props} />;
}
function FieldRequestContent(props: BasicProps) {
  return <RequestContent {...props} />;
}

function FieldServiceItemPage(props: BasicProps) {
  return <ServiceItemPage {...props} />;
}

const FieldDateRange = observer(({ originProps, basicProps }: FieldItemProps) => {
  const { field, pageStore, section, values } = originProps;
  const rootSection = pageStore?.flattenJsonData && _.findRootSection(pageStore.flattenJsonData, section);
  const handleDateChange = (valueArr) => {
    originProps.onChange(valueArr.start, field.widgetConfig?.startFieldCode, field, true, rootSection);
    originProps.onChange(valueArr.end, field.widgetConfig?.endFieldCode, field, true, rootSection);
  };
  return <YqDateRange {...basicProps} onChange={handleDateChange} value={values} />;
});

function FieldCustomComponent({ originProps, basicProps }) {
  return <CustomComponent {...basicProps} eventItem={originProps.eventItem} />;
}

function FieldMobileTable({ originProps, basicProps }) {
  return <MobileTable {...basicProps} onChange={originProps.onChange} />;
}

function FieldPCTable({ originProps, basicProps }) {
  return <MobileTable {...basicProps} onChange={originProps.onChange} pcFlag />;
}

function FieldVariableTable({ originProps, basicProps }) {
  return <VariableTable {...basicProps} onChange={originProps.onChange} />;
}

function FieldVariablePage({ basicProps }) {
  const { field, value, values, pageStore, disabled, cacheFormFieldsValues, liveUpdate = () => { } } = basicProps;
  if (!values[`${field.code}:_variable`]) {
    values[`${field.code}:_variable`] = {};
  }
  return value ? (
    <PageLoader
      variableFlag={!value?._view_flag}
      viewId={value?.id || value}
      disabled={disabled}
      instanceId={value?._view_flag ? basicProps.ticketId : undefined}
      formFieldValues={value?._view_flag ? values : values[`${field.code}:_variable`]}
      pristineValue={value?._view_flag ? (pageStore.pristineValue || values) : (pageStore.pristineValue[`${field.code}:_variable`] || values[`${field.code}:_variable`])}
      cacheFormFieldsValues={value?._view_flag ? cacheFormFieldsValues : cacheFormFieldsValues[`${field.code}:_variable`]}
      liveUpdate={liveUpdate}
      setFormJsonData={res => {
        try {
          if (typeof pageStore.jsonData?.sections?.push === 'function' && Array.isArray(res?.sections)) {
            res.sections.forEach(section => {
              Object.assign(section, { validateSection: true });
              pageStore.jsonData.sections.push({ ...section });
            });
          }
        } catch (e) {
          // eslint-disable-next-line no-console
          console.error(e);
        }
      }}
    />
  ) : null;
}

const FieldRichText = observer(({ basicProps }) => {
  basicProps.ocr = basicProps.field.ocr;
  basicProps.ocrFlag = basicProps.field.ocrFlag;
  basicProps.handleOcr = basicProps.field.handleOcr;
  basicProps.voice = basicProps.field.voice;
  basicProps.handleVoice = basicProps.field.handleVoice;
  const defaultValue = basicProps?.pristineValue?.[basicProps.name] || basicProps?.pageStore?.pristineValue?.[basicProps.name];
  const htmlDefaultValue = _.parseMsgToHtml(defaultValue || basicProps.value, basicProps?.voice);
  return <Wysiwyg {...basicProps} defaultValue={htmlDefaultValue} onChange={basicProps.onChange} />;
});

function FieldImage({ basicProps }) {
  return <YqCamera inPage {...basicProps} />;
}

function FieldFile({ basicProps }) {
  return <YqAttachment {...basicProps} forceShow />;
}

function FieldDynamic({ basicProps }) {
  const { intl, reqId, viewCode, field, businessObjectId, businessObjectCode, viewId, ...other } = basicProps;
  return (
    <YqDynamic
      {...other}
      intl={intl}
      viewCode={viewCode}
      ticketId={reqId}
      widgetConfig={field?.widgetConfig}
      businessObjectId={businessObjectId}
      businessObjectCode={businessObjectCode}
      viewId={viewId}
      name={field?.name}
      componentType={field?.widgetType}
    />
  );
}

function FieldReply({ basicProps }) {
  const { intl, reqId, viewCode, field, businessObjectId, businessObjectCode, viewId, ...other } = basicProps;
  return (
    <YqReply
      {...other}
      intl={intl}
      viewCode={viewCode}
      ticketId={reqId || other.ticketId}
      widgetConfig={field?.widgetConfig}
      businessObjectId={businessObjectId}
      businessObjectCode={businessObjectCode}
      viewId={viewId}
      name={field?.name}
    />
  );
}

function FieldDateTime({ basicProps }) {
  const { field } = basicProps;
  const format = field.widgetConfig?.format || 'YYYY-MM-DD HH:mm:ss';
  return <DataTime {...basicProps} placeholder={field.placeHolder} mode="datetime" format={format} />;
}

function FieldDate({ basicProps }) {
  const { field } = basicProps;
  const format = field.widgetConfig?.format || 'YYYY-MM-DD';
  return <DataTime {...basicProps} placeholder={field.placeHolder} mode="date" format={format} />;
}

function FieldTime({ basicProps }) {
  const { field } = basicProps;
  const format = field.widgetConfig?.format || 'HH:mm:ss';
  return <DataTime {...basicProps} placeholder={field.placeHolder} mode="time" format={format} />;
}

function FieldRadio({ basicProps }) {
  const { field } = basicProps;
  const { keyMap, url, data, type } = getApiParam(field);
  const api = url ? { url, data, type, sourceKey: '' } : null;
  return <MbRadio {...basicProps} api={api} keyMap={keyMap} data={field.widgetConfig?.options} />;
}

function FieldSingleText({ basicProps }) {
  const { field } = basicProps;
  return <Input {...basicProps} type={field.widgetType} ocrFlag={field.ocrFlag} ocr={field?.ocr} handleOcr={field?.handleOcr} />;
}

function FieldUrl({ basicProps }) {
  const { field } = basicProps;
  if (field?.widgetConfig?.urlType === 'BUTTON') {
    return <UrlButton {...basicProps} />;
  }
  return <Input {...basicProps} type={field.widgetType} ocrFlag={field.ocrFlag} ocr={field?.ocr} handleOcr={field?.handleOcr} />;
}

function FieldNumber({ basicProps }) {
  const { field } = basicProps;
  return <Input {...basicProps} ocr={field?.ocr} handleOcr={field?.handleOcr} />;
}

// 手机号类型组件
function FieldPhone({ basicProps }) {
  const { field } = basicProps;
  return <Input {...basicProps} type="tel" ocr={field?.ocr} handleOcr={field?.handleOcr} />;
}

function FieldPassword({ basicProps }) {
  const { field } = basicProps;
  return <Input {...basicProps} type="password" ocr={field?.ocr} handleOcr={field?.handleOcr} />;
}

function FieldTextArea({ basicProps, originProps }) {
  const { field, name, rootSection } = basicProps;
  return (
    <Input
      {...basicProps}
      multi
      ocrFlag={field.ocrFlag}
      ocr={field?.ocr}
      handleOcr={field?.handleOcr}
      onChange={(val, isValidatePassed) => {
        if (typeof val !== 'string') {
          return;
        }
        originProps.onChange(val, name, field, isValidatePassed, rootSection);
      }}
    />
  );
}

// 子任务组件
function FieldSubTask({ basicProps }) {
  return <SubTask {...basicProps} />;
}

// 工时关联内容
//   - 预估工时
//   - 实际工时
//        -回复
//        -动作
function FieldWorkHourRelated(props) {
  return <WorkTimeRelated {...props} />;
}

// 页面视图组件
function FieldPage({ basicProps }) {
  const { reqId, field, ...other } = basicProps;
  return (
    <YqPage
      {...other}
      ticketId={reqId || other.ticketId}
      widgetConfig={field?.widgetConfig}
      name={field?.name}
    />
  );
}

export {
  FieldLocation,
  FieldTicketSLA,
  FieldStageExpand,
  FieldRequestContent,
  FieldServiceItemPage,
  FieldDateRange,
  FieldCustomComponent,
  FieldMobileTable,
  FieldPCTable,
  FieldVariableTable,
  FieldVariablePage,
  FieldRichText,
  FieldFile,
  FieldImage,
  FieldReply,
  FieldDynamic,
  FieldDateTime,
  FieldDate,
  FieldTime,
  FieldRadio,
  FieldSingleText,
  FieldNumber,
  FieldUrl,
  FieldPassword,
  FieldTextArea,
  GlobalTicket,
  FieldSubTask,
  FieldPhone,
  FieldWorkHourRelated,
  FieldPage,
};
