import TransValueRegionList from '@/components/region-list';
import TicketHeader from '@/components/ticket-header';
import TicketChatRecords from '@/components/ticket-chat-records';
import YqLabel from '@/components/yq-label';
import TicketWorkHour from '@/components/ticket-workhour';
import Yq<PERSON><PERSON>ck<PERSON>ox from '@/components/yq-checkbox';
import YqSelectBox from '@/components/yq-select-box';
import CustomTreeLov from '@/components/custom-treelov';
import Duration from '@/components/duration';
import AssetRelation from '@/components/asset-relation';
import AccessoriesList from '@/components/accessories-list';
import ProcessTime from '@/components/process-time';
import YqAlert from '@/components/yq-alert';
import Switch from '@/components/switch';
import MbRate from '@/components/mb-rate';
import MbLike from '@/components/mb-like';
import TicketTransfer from '@/components/ticket-transfer';
import MbPercentage from '@/components/mb-percentage';
import YqCurrency from '@/components/yq-currency';
import <PERSON>Number from '@/components/auto-number';
import Participants from '@/components/participants';
import RelevantKnowledge from '@/components/relevant-knowledge';
import MasterDetail from '@/components/master-detail';
import Select from '@/components/select';
import MultipleChoice from '@/components/multiple-choice';
import TicketSurveyDetail from '@/components/ticket-survey-detail';
import KnowledgeDetail from '@/components/knowledge-detail';
import TicketRelation from '@/components/ticket-relation';
import TicketRelationCard from '@/components/ticket-relation-card';
import TicketRelationList from '@/components/ticket-relation-list';
import { constants } from '@/util';
import {
  FieldServiceItemPage,
  FieldRequestContent,
  FieldDateRange,
  FieldCustomComponent,
  FieldMobileTable,
  FieldPCTable,
  FieldVariableTable,
  FieldVariablePage,
  FieldRichText,
  FieldImage,
  FieldFile,
  FieldDynamic,
  FieldReply,
  FieldDateTime,
  FieldDate,
  FieldTime,
  FieldRadio,
  FieldSingleText,
  FieldNumber,
  FieldPassword,
  FieldTextArea,
  FieldStageExpand,
  FieldTicketSLA,
  FieldLocation,
  FieldUrl,
  GlobalTicket,
  FieldSubTask,
  FieldPhone,
  FieldWorkHourRelated,
  FieldPage,
} from './FieldCustom';

// TODO Field开头的组件传参不太标准，后面慢慢改吧
export const NotStandardComponents = [
  constants.FORM_TYPE.EMAIL,
  constants.FORM_TYPE.URL,
  constants.FORM_TYPE.INPUT,
  constants.FORM_TYPE.PASSWORD,
  constants.FORM_TYPE.FLOAT_FIELD,
  constants.FORM_TYPE.NUMBER_FIELD,
  constants.FORM_TYPE.TEXTAREA,
  constants.FORM_TYPE.RADIO,
  constants.FORM_TYPE.RICHTEXT,
  constants.FORM_TYPE.DATE,
  constants.FORM_TYPE.TIME,
  constants.FORM_TYPE.DATE_TIME,
  constants.FORM_TYPE.IMAGE,
  constants.FORM_TYPE.UPLOAD,
  constants.FORM_TYPE.DYNAMIC,
  constants.FORM_TYPE.COMMENT,
  constants.FORM_TYPE.VARIABLE_PAGE,
  constants.FORM_TYPE.VARIABLE_TABLE,
  constants.FORM_TYPE.MOBILE_TABLE,
  constants.FORM_TYPE.CUSTOM,
  constants.FORM_TYPE.CAMERA,
  constants.FORM_TYPE.DATE_RANGE,
  constants.FORM_TYPE.TABLE,
  constants.FORM_TYPE.SUBTASKS,
  constants.FORM_TYPE.MOBILEFIELD,
  constants.FORM_TYPE.WORKHOURRELATEDCONTENT,
  constants.FORM_TYPE.PAGE,
];

export const FieldMapping = {
  [constants.FORM_TYPE.LOCATION]: FieldLocation,
  [constants.FORM_TYPE.TICKET_SLA]: FieldTicketSLA,
  [constants.FORM_TYPE.STAGE_EXPAND]: FieldStageExpand,
  [constants.FORM_TYPE.EMAIL]: FieldSingleText,
  [constants.FORM_TYPE.URL]: FieldUrl,
  [constants.FORM_TYPE.INPUT]: FieldSingleText,
  [constants.FORM_TYPE.PASSWORD]: FieldPassword,
  [constants.FORM_TYPE.FLOAT_FIELD]: FieldNumber,
  [constants.FORM_TYPE.CURRENCY]: YqCurrency,
  [constants.FORM_TYPE.DURATION]: Duration,
  [constants.FORM_TYPE.NUMBER_FIELD]: FieldNumber,
  [constants.FORM_TYPE.TEXTAREA]: FieldTextArea,
  [constants.FORM_TYPE.MASTER_DETAIL]: MasterDetail,
  [constants.FORM_TYPE.RADIO]: FieldRadio,
  [constants.FORM_TYPE.SELECT]: Select,
  [constants.FORM_TYPE.CASCADER]: Select,
  [constants.FORM_TYPE.SELECTBOX]: YqSelectBox,
  [constants.FORM_TYPE.MULTI_SELECT]: Select,
  [constants.FORM_TYPE.RICHTEXT]: FieldRichText,
  [constants.FORM_TYPE.DATE]: FieldDate,
  [constants.FORM_TYPE.TIME]: FieldTime,
  [constants.FORM_TYPE.DATE_TIME]: FieldDateTime,
  [constants.FORM_TYPE.CHECKBOX]: YqCheckBox,
  [constants.FORM_TYPE.BOOLEAN]: Switch,
  [constants.FORM_TYPE.SWITCH]: Switch,
  [constants.FORM_TYPE.IMAGE]: FieldImage,
  [constants.FORM_TYPE.UPLOAD]: FieldFile,
  [constants.FORM_TYPE.DYNAMIC]: FieldDynamic,
  [constants.FORM_TYPE.REGION]: TransValueRegionList,
  [constants.FORM_TYPE.COMMENT]: FieldReply,
  [constants.FORM_TYPE.VARIABLE_PAGE]: FieldVariablePage,
  [constants.FORM_TYPE.SERVICE_ITEM_PAGE]: FieldServiceItemPage,
  [constants.FORM_TYPE.REQUEST_CONTENT]: FieldRequestContent,
  [constants.FORM_TYPE.VARIABLE_TABLE]: FieldVariableTable,
  [constants.FORM_TYPE.MOBILE_TABLE]: FieldMobileTable,
  [constants.FORM_TYPE.TABLE]: FieldPCTable,
  [constants.FORM_TYPE.RATE]: MbRate,
  [constants.FORM_TYPE.LIKE]: MbLike,
  [constants.FORM_TYPE.PERCENTAGE]: MbPercentage,
  [constants.FORM_TYPE.CUSTOM]: FieldCustomComponent,
  [constants.FORM_TYPE.CAMERA]: FieldImage,
  [constants.FORM_TYPE.ASSET_RELATION]: AssetRelation,
  [constants.FORM_TYPE.ACCESSORIES_LIST]: AccessoriesList,
  [constants.FORM_TYPE.PROCESS_TIME]: ProcessTime,
  [constants.FORM_TYPE.TICKET_SURVEY_DETAIL]: TicketSurveyDetail,
  [constants.FORM_TYPE.TICKET_HEADER]: TicketHeader,
  [constants.FORM_TYPE.CHAT_RECORDS]: TicketChatRecords,
  [constants.FORM_TYPE.TAG]: YqLabel,
  [constants.FORM_TYPE.TICKET_WORKHOUR]: TicketWorkHour,
  [constants.FORM_TYPE.MULTIPLE_CHOICE]: MultipleChoice,
  [constants.FORM_TYPE.TREELOV]: CustomTreeLov,
  [constants.FORM_TYPE.DATE_RANGE]: FieldDateRange,
  [constants.FORM_TYPE.ALERT]: YqAlert,
  [constants.FORM_TYPE.TICKETTRANSFER]: TicketTransfer,
  [constants.FORM_TYPE.AUTO_NUM]: AutoNumber,
  [constants.FORM_TYPE.PARTICIPANTS]: Participants,
  [constants.FORM_TYPE.RELEVANTKNOWLEDGE]: RelevantKnowledge,
  [constants.FORM_TYPE.KNOWLEDGEDETAIL]: KnowledgeDetail,
  [constants.FORM_TYPE.GLOBAL_TICKET]: GlobalTicket,
  [constants.FORM_TYPE.SUBTASKS]: FieldSubTask,
  [constants.FORM_TYPE.MOBILEFIELD]: FieldPhone,
  [constants.FORM_TYPE.WORKHOURRELATEDCONTENT]: FieldWorkHourRelated,
  [constants.FORM_TYPE.PAGE]: FieldPage,
  [constants.FORM_TYPE.TICKET_RELATION]: TicketRelation,
  [constants.FORM_TYPE.TICKET_RELATION_CARD]: TicketRelationCard,
  [constants.FORM_TYPE.TICKET_RELATION_LIST]: TicketRelationList,
};
