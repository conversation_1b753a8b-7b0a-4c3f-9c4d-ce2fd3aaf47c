import { _, constants } from '@/util';
import appStore from '@/store/app';
import lovStore from '@/store/lov';
import { Field } from '../ts';

function calculateFieldIsDisabled(field: Field, values: any, fieldDisabled: boolean, policiesData) {
  // 不要把readonly和disabled一起算了，这俩玩意儿是分开的，readonly是false了，就算计算disabled是true禁用状态他也是可编辑的
  // 目前readonly只在VARIABLE_PAGE 变量视图上会有
  if (field.widgetType === constants.FORM_TYPE.VARIABLE_PAGE) {
    return _.conditionReadonly(field, values, policiesData?.conditionMap, policiesData?.actionMap?.[field.code]);
  } else {
    return (_.conditionDisabled(field, values, policiesData?.conditionMap, policiesData?.actionMap?.[field.code]) || fieldDisabled);
  }
}

// 专门用来处理富文本的默认值
function getDefaultRichText(content, isHtmlAndJson = false) {
  try {
    // eslint-disable-next-line no-prototype-builtins
    if (isHtmlAndJson || (content?.hasOwnProperty('html') || content?.hasOwnProperty('json'))) {
      return content;
    }

    const jsonData = _.isJSON(content) ? JSON.parse(content) : content;
    if (typeof jsonData === 'string' && !_.isJSON(jsonData)) {
      return jsonData;
    }
    jsonData && jsonData.length > 0 && jsonData.forEach(delta => {
      if (delta.insert && delta.insert['proc-user']) {
        delta.insert = delta.attributes?.innerHtml;
      } else if (delta.insert && delta.insert['proc-knowledge']) {
        delta.insert = delta.attributes?.innerHtml;
      }
    });
    return _.parseMsgToHtml(jsonData);
  } catch (e) {
    return [];
  }
}

function getApiParam(field, pageStore?) {
  const currentDs = pageStore?.jsonData?.datasets?.find(ds => ds.id === pageStore.data.id);
  const dsField = currentDs?.fields?.find(f => f.code === field.code);
  const keyMap: any = {
    key: 'value',
    value: 'meaning',
  };
  let url = '';
  const data = {};
  let type = 'GET';
  let searchBar = true;
  // 设置快码数据请求
  if (field.widgetConfig?.dataSource === constants.FIELD_DATA_SOURCE.LOOKUP) {
    let urlType = 'queryByCode';
    const isTable = field.tag === 'Table';
    const { condition } = field.widgetConfig;
    if (!isTable && condition && condition.length > 0) {
      urlType = 'queryCodeWithCondition';
      type = 'POST';
    }
    url = `hpfm/v1/${appStore.tenantId === '0' ? '' : `${appStore.tenantId}/`}lookup/${urlType}?lookupTypeCode=${field.widgetConfig.lookupCode}`;
    keyMap.key = 'code';
    keyMap.value = 'value';
    searchBar = false;
  }
  // LOV数据请求
  if (field.relationLovId) {
    //  setting 请求
    // url = `lc/v1/${appStore.tenantId}/object_options/id/${field.relationLovId}`
    url = `lc/v1/engine/${appStore.tenantId === '0' ? '' : `${appStore.tenantId}/`}options/${field.relationLovId}/queryWithCondition`;
    type = 'POST';
    // 若没传pageStore就在field里面取，两个都取不到就取默认值
    keyMap.key = dsField?.widgetConfig?.relationLovValueFieldCode || field?.widgetConfig?.relationLovValueFieldCode || 'id';
    // keyMap.value = dsField?.widgetConfig?.relationLovNameFieldCode || field?.widgetConfig?.relationLovNameFieldCode || 'name' // 据说没有默认取name，
    keyMap.value = dsField?.widgetConfig?.mobileLovNameFieldCode || dsField?.widgetConfig?.relationLovNameFieldCode || field?.widgetConfig?.mobileLovNameFieldCode || field?.widgetConfig?.relationLovNameFieldCode || 'name';
    keyMap.code = dsField?.widgetConfig?.relationLovNameFieldCode || field?.widgetConfig?.relationLovNameFieldCode || 'name';

    lovStore.fetchLovConfigById(field.relationLovId).then(res => {
      const { jsonData } = res;
      if (jsonData) {
        const { layout } = jsonData;
        if (Array.isArray(layout)) {
          const hiddenFields = layout.filter(l => l?.props?.displayFlag === false && l?.props?.name).map(l => l?.props?.name);
          if (hiddenFields.length === 0) return; // 没有隐藏字段直接return掉
          const displayFields = (keyMap.value || '').split(',');
          const afterReducesFields: any[] = [];
          displayFields.forEach(displayField => {
            if (!hiddenFields.includes(displayField)) {
              afterReducesFields.push(displayField);
            }
          });
          keyMap.value = afterReducesFields.join(',');
        }
      }
    });
  }
  if (field.widgetType === constants.FORM_TYPE.MULTIPLE_CHOICE) {
    lovStore.fetchLovConfigById(field.widgetConfig.multipleLovVariableCode);
    url = `lc/v1/engine/${appStore.tenantId}/options/${field.widgetConfig.multipleLovVariableCode}/query`;
    type = 'POST';
  }
  return { keyMap, url, data, type, searchBar };
}

export {
  calculateFieldIsDisabled,
  getDefaultRichText,
  getApiParam,
};
