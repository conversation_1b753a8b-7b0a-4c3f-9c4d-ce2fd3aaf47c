/* eslint-disable no-unused-vars */
import { IntlShape } from '@/util/intl';

interface WidgetConfig {
  relatedObjectCode?: String;
  relatedPrimaryKey?: String;
  hideQuantity?: boolean;
  hidePrice?: boolean;
  startFieldCode: string;
  endFieldCode: string;
  locationUrl?: string;
  locationFieldCode?: string;
}

export interface Field {
  title: string;
  name: string;
  code: string;
  widgetType: string;
  placeHolder?: string;
  widgetConfig: WidgetConfig;
  voice: any;
}

interface Section {

}

interface CommonProps {
  value: any;
  field: Field;
  disabled: boolean;
  values: any;
  onChange: (_v: any, fieldCode: string, field: Field, pass: boolean, section: Section) => void;
  ticketId?
  pageStore?
}

export interface BasicProps extends CommonProps {
  editable: boolean;
  title: string;
  name: string;
  required: boolean;
  rootSection: Section;
}

export interface FieldProps extends CommonProps {
  alwaysShow: boolean;
  alwayEditable: boolean;
  onSelected: () => void;
  pageStore: any;
  section: Section;
  intl: IntlShape;
  surveyFlag: boolean;
  cardMode?: boolean;
}

export interface FieldItemProps {
  basicProps: BasicProps;
  originProps: FieldProps;
}
