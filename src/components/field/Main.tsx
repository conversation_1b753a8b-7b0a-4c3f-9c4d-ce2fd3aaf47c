import React, { useCallback, useMemo } from 'react';
import { observer } from 'mobx-react';
import { View } from '@tarojs/components';
import { constants, _ } from '@/util';
import { FieldMapping, NotStandardComponents } from './components-mapping';
import { FieldProps } from './ts';
import { calculateFieldIsDisabled, getDefaultRichText } from './utils';
import styles from './Field.module.less';

const Filed: React.FC<FieldProps> = function Field(props) {
  const {
    field, values, value: propsValue, alwaysShow, disabled: fieldDisabled,
    alwayEditable, onChange = () => { }, pageStore, section, surveyFlag, cardMode = false,
  } = props;

  const widgetType = field.widgetType;
  const policiesData = pageStore?.policiesData;

  const visible = _.conditionVisible(field, values, policiesData?.conditionMap, policiesData?.actionMap?.[field.code]) || alwaysShow;

  const rootSection = useMemo(() => {
    return pageStore?.flattenJsonData && _.findRootSection(pageStore.flattenJsonData, section);
  }, [pageStore, section]);

  const handleChange = useCallback((v, pass, fieldCode = field.code) => {
    onChange(v?.changedFiles || v, fieldCode, field, pass, rootSection);
  }, [onChange, rootSection, field]);

  // NOTE: 2024 年 9 月 11 日 【白春强】需求，移动端视图渲染不再在页面显示不支持的组件提醒
  //   只在控制台打印提示
  const renderDefault = useCallback(() => {
    console.warn(`${field.name} - ${field.widgetType}[Unknown Component]`);
    return null;
  }, [field]);

  const basicProperties = useMemo(() => {
    const title = surveyFlag ? (field.title || field.name) : field.name;
    const name = field.code;
    const editable = true;
    const rootCode = rootSection?.code;
    const required = !!_.conditionRequired(field, values, policiesData?.conditionMap, policiesData?.actionMap?.[field.code]);
    const disabled = !alwayEditable && !!calculateFieldIsDisabled(field, values, fieldDisabled, policiesData);
    let value = propsValue;
    if (rootCode?.startsWith('variable_set')) {
      const curValues = _.transVariableSetToObj(values);
      try {
        const valueObj = typeof curValues[rootCode] === 'object' ? curValues[rootCode] : JSON.parse(curValues[rootCode] || '{}');
        value = valueObj?.[name] || value;
      } catch (e) {
        // eslint-disable-next-line no-console
        console.warn(e);
      }
    }
    if (field.widgetType === constants.FORM_TYPE.RICHTEXT) {
      value = getDefaultRichText(value, field?.voice); // 富文本传值之前需要进行处理
    }
    if (field.widgetType === constants.FORM_TYPE.UPLOAD && field.code?.startsWith('camera_')) {
      field.widgetType = constants.FORM_TYPE.CAMERA; // 现在拍照和录像组件的widgetType也是Upload，得加以区分
    }
    return { ...props, value, cardMode, field, editable, title, name, disabled, required, values, onChange: handleChange, rootSection, changeOtherField: onChange };
  }, [surveyFlag, cardMode, field, rootSection, values, alwayEditable, fieldDisabled, propsValue, props, handleChange]);

  const fieldProps = useMemo(() => {
    if (NotStandardComponents.includes(widgetType)) return { originProps: props, basicProps: basicProperties };
    else return basicProperties;
  }, [basicProperties, props, widgetType]);

  if (!visible) return null;
  // @ts-ignore
  return FieldMapping[widgetType] ? React.createElement(FieldMapping[widgetType], fieldProps) : renderDefault();
};

export default observer(Filed);
