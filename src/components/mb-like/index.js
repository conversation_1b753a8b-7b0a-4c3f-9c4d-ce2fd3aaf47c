import { View } from '@tarojs/components';
import { Radio } from '@taroify/core';
import { observer } from 'mobx-react-lite';
import { injectIntl } from '@/util/intl';
import YqIcon from '@/components/yq-icon';
import { _ } from '@/util';
import { addAction } from '@/util/guance';
// eslint-disable-next-line import/no-cycle
import { MbRow } from '../index';
import './index.scss';

function MbLike(props) {
  const {
    onChange = _.nullFunc,
    value,
    title,
    editable,
    intl,
    name,
    field,
    disabled,
  } = props;
  function renderValue() {
    return (
      <Radio.Group
        direction="horizontal"
        value={value}
        disabled={disabled}
        onChange={(v) => {
          // eslint-disable-next-line no-chinese/no-chinese
          addAction('使用低代码点赞组件');
          onChange(v);
        }}
      >
        <Radio
          name="true"
          icon={
            <YqIcon type="GoodTwo" size={40} fill={value === 'true' || value === true ? '#2979ff' : '#595959'} theme="filled" />
          }
        >
          {field.widgetConfig?.likeText}
        </Radio>
        <Radio
          name="false"
          icon={
            <YqIcon type="ThumbsDown" size={40} fill={value === 'false' || value === false ? '#2979ff' : '#595959'} theme="filled" />
          }
        >
          {field.widgetConfig?.dislikeText}
        </Radio>
      </Radio.Group>
    );
  }

  return (
    <MbRow
      {...props}
      copyable={false}
      className="yq-like"
      intl={intl}
      label={title}
      value={renderValue()}
    />
  );
}

export default injectIntl(observer(MbLike));
