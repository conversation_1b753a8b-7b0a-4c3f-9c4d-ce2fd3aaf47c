import { Text, View } from '@tarojs/components';
import { useState } from 'react';
import { Dialog, Popup, Button, Toast } from '@taroify/core';
import { observer } from 'mobx-react-lite';
import classnames from 'classnames';
import { useIntl } from '@/util/intl';
import YqButton from '@/components/yq-button';
import Field from '@/components/field';
import YqIcon from '@/components/yq-icon';
import Select from '@/components/select';
import { DialogPopup, PopupHeader } from '@/components/yq-component-popup';
import '../list-previewer/index.scss';
import styles from './BatchAction.module.less';

export default observer(({
  open,
  setOpen,
  handleCancel,
  handleConfirm,
  selectStore,
  handleActionsChange,
  handleRestFieldChange,
  openFeedback,
  setOpenFeedback,
  onCloseFeedback = () => { },
  mode,
}) => {
  const intl = useIntl();

  const [openConfirm, setOpenConfirm] = useState(false);
  const [curAction, setCurAction] = useState({});

  function actionChangeHandler(action) {
    handleActionsChange(action);
    if (!action.popupWindowFlag) {
      setOpenConfirm(true);
      setCurAction(action);
    } else {
      setOpen(true);
    }
  }

  const handleCloseFeedback = () => {
    setOpenFeedback(false);
    onCloseFeedback();
  };

  const handleClose = (e) => {
    setOpenConfirm(e);
    setCurAction({});
  };

  const handlePreConfirm = async () => {
    setOpenConfirm(false);
    Toast.loading({
      message: intl.formatMessage({ id: 'yqc.mobile.loading', defaultMessage: '加载中...' }),
      duration: 9999999,
    });
    await handleConfirm();
    selectStore.actions = [];
    Toast.close();
  };

  const handlePreCancel = () => {
    setOpenConfirm(false);
    handleCancel?.();
  };

  const descriptionRenderer = () => (
    <View>
      <View style={{ height: '27px' }}>
        {intl.formatMessage({ id: 'yqc.mobile.common.select', defaultMessage: '选中' })}<Text style={{ color: '#2979ff' }}>{selectStore.numbers[0]}</Text>{intl.formatMessage({ id: 'yqc.mobile.common.item', defaultMessage: '条' })},
        {selectStore.numbers[1] > 0 && (
          <Text>
            <Text style={{ color: '#7bc95a' }}>{selectStore.numbers[1]}</Text>{intl.formatMessage({ id: 'yqc.mobile.batchAction.desc.success', defaultMessage: '条操作成功' })},
          </Text>
        )}
        {selectStore.numbers[2] > 0 && (
          <Text>
            <Text style={{ color: '#ff4949' }}>{selectStore.numbers[2]}</Text>{intl.formatMessage({ id: 'yqc.mobile.batchAction.desc.failed', defaultMessage: '条操作失败' })},
          </Text>
        )}
      </View>
      {selectStore.numbers[2] > 0 && (
        <View style={{ height: '27px' }}>
          {intl.formatMessage({ id: 'yqc.mobile.batchAction.desc.failed.reason', defaultMessage: '失败原因：存在选中数据不可执行的动作' })}
        </View>
      )}
    </View>
  );

  const confirmRenderer = () => (
    <View>
      {intl.formatMessage({ id: 'yqc.mobile.batchaction.description.pre', defaultMessage: '确认对' })}
      <span style={{ color: '#2979ff' }}>{selectStore.selectedList.length}</span>
      {intl.formatMessage({ id: 'batchAction.description.after' }, { name: curAction?.name, count: selectStore.selectedList.length })}
    </View>
  );

  return (
    <>
      {mode === 'inline' && <View
        className={classnames({
          'flex-button-group': true,
          // 'no-more': !customButtons || customButtons.length === 0,
          overflow: selectStore.actions?.length > 4,
        })}
      >
        {selectStore.actions[0] && selectStore.actions.map((action) => (
          <YqButton
            key={action?.id}
            className="flex-button-group-item"
            structure="tb"
            icon={<YqIcon type={action.icon} theme="outline" size={32} fill="#595959" />}
            onClick={() => actionChangeHandler(action)}
            fontColor="#595959"
            name={action.name}
          />
        ))}
      </View>}
      {selectStore.curAction && <Popup
        placement="bottom"
        open={open}
        onClose={setOpen}
        style={{ minHeight: '30%', maxHeight: '90%', display: 'flex', flexDirection: 'column' }}
      >
        <View className="list-preview-popup">
          {/* 标题 */}
          <PopupHeader
            title={mode === 'inline'
              ? (selectStore.curAction.windowTitle || intl.formatMessage({ id: 'yqc.mobile.pleaseconfirmyesno', defaultMessage: '请确认是否' }) + selectStore.curAction.name)
              : intl.formatMessage({ id: 'yqc.mobile.batchaction', defaultMessage: '批量处理' })}
            handleOk={handleConfirm}
            handleCancel={handleCancel}
          />
          <View className="list-preview-popup-content">
            {/* 选择操作 */}
            {mode !== 'inline' && (
              <Select
                required
                fullRecord
                title={intl.formatMessage({ id: 'yqc.mobile.select.actions', defaultMessage: '选择操作' })} // 选择操作
                config={{ valueField: 'id', nameField: 'name' }}
                value={selectStore.curAction?.id}
                data={selectStore.actions}
                onChange={(r) => handleActionsChange?.(r || {})}
              />
            )}
            {/* 操作描述 */}
            {selectStore.curAction?.windowDescription && <View className="list-preview-popup-content-description">
              <View className={styles.PopupDescription} style={{ color: 'rgba(18, 39, 77, 0.65)' }}>
                {intl.formatMessage({ id: 'yqc.mobile.batch.action.description', defaultMessage: '操作描述' })}
              </View>
              <View className={styles.PopupDescription}>
                {selectStore.curAction?.windowDescription}
              </View>
            </View>}
            {selectStore.restFields.length > 0 && selectStore.restFields.map((field) => (
              <Field
                required={field.required}
                field={field}
                intl={intl}
                key={field.code}
                value={selectStore.formFieldValues[field.code]}
                values={selectStore.formFieldValues}
                onChange={handleRestFieldChange}
              />
            ))}
            <View style={{ height: '40px', width: '100%' }} />
          </View>
        </View>
      </Popup>}
      <DialogPopup
        open={openFeedback}
        handleClickOutside={handleCloseFeedback}
        type={selectStore.numbers[2] === 0 ? 'success' : 'error'}
        descriptionRenderer={descriptionRenderer}
        actions={[{ click: handleCloseFeedback, text: intl.formatMessage({ id: 'yqc.mobile.close', defaultMessage: '关闭' }) }]}
      />
      <DialogPopup
        open={openConfirm}
        handleClickOutside={handleClose}
        type="noImage"
        descriptionRenderer={confirmRenderer}
        actions={[
          {
            click: handlePreCancel,
            text: intl.formatMessage({ id: 'yqc.mobile.cancel', defaultMessage: '取消' }),
          },
          {
            click: handlePreConfirm,
            text: intl.formatMessage({ id: 'yqc.mobile.confirm', defaultMessage: '确认' }),
          },
        ]}
      />
    </>
  );
});
