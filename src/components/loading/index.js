import { View } from '@tarojs/components';
import { Loading } from '@taroify/core';
import './index.scss';

export default function (props) {
  const { color = '#2979ff', size = '32px' } = props;
  if (props.loading) {
    return (
      <View className="loading">
        <Loading color={color} size={size} />
        {props.tip ? <View className="loading-tip">{props.tip}</View> : null}
      </View>
    );
  } else {
    return props.children || null;
  }
}
