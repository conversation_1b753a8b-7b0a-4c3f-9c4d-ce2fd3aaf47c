.content {
  padding: 22px 34px 22px 34px;
  background: white;
}


.header {
  font-size:0.8rem;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #12274D;
  line-height: 1.6rem;
  padding-bottom: 20px;
}

.father {

}

.item {
  width: 100%;
  height: 220px;
  border-radius: 8px;
  border: 2px solid rgba(203,210,220,0.5);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 26px 24px;
  color: #12274D;
  margin-bottom: 32px;
  .line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 28px;
    .ticketTitle {
      font-size: 30px;
      font-weight: 500;
    }

    .ticketState {
      display: flex;
      width: 160px;
      height: 44px;
      background-color: red;
      border-radius: 44px;
      align-items: center;
      justify-content: space-around;
      padding: 0 10px 0 10px;
      .stateIcon {
        width: 28px;
        height: 28px;
        background-color: white;
        border-radius: 28px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .stateText {
        color: white;
        font-size: 24px;
      }
    }
  }
}

.header {
  display: flex;
  justify-content: space-between;
}
.fixSla {
  align-items: center;
  font-weight: 400;
  display: flex;
  color: #2979ff;
  font-size: 28px;
  font-family: PingFangSC-Regular, PingFang SC;

}
.slaCard {
  margin: 0 32px;
  margin-bottom: 40px;
  border-radius: 8px;
  border: 1px solid rgba(203,210,220,0.5);
}
.cardHeader {
  padding:24px;
  background: #EDF2FF;

  font-size: 32px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #12274D;
  line-height: 45px;
}
.cardRow {
  padding: 24px;

  &:not(:last-child) {
    border-bottom: 1px solid rgba(203,210,220,0.5);
  }
}
.firstRow {
  display: flex;
  justify-content: space-between;
  .status {
    display: flex;
  }
}
.secondRow {
  margin-top: 19px;
  font-size: 24px;
  color: rgba(18,39,77,0.65);
  line-height: 44px;
  display: flex;
  justify-content: space-between;
}
.personRow {
  padding: 16px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(203,210,220,0.2);
}
.personInfo {
  display: flex;
  align-items: center;
}
.mr20 {
  margin-right: 20px;
}
.personList {
  min-height: 645px;
}
.slaRepair {
  font-size: 28px;
  color: #12274D;
  font-weight: 400;
  width: 391px;
  margin: 0 auto;
  font-family: PingFangSC-Regular, PingFang SC;
  line-height: 42px;
  margin-top: 24px;
}
