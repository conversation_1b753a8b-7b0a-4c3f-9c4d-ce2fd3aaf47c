import { View } from '@tarojs/components';
import { useEffect, useState } from 'react';
import { useIntl } from '@/util/intl';
import { fetch, constants } from '@/util';
import appStore from '@/store/app';
import styles from '@/components/process-time/ProcessTime.module.less';
import Icon from '@/components/yq-icon';
import YqEmpty from '@/components/yq-empty';
import { handleSlaData, minutesToHour, handleUser, openSLADetailModal } from './FatherTicket';

function ChildTicket(props) {
  const intl = useIntl();
  const { ticketId, businessObjectCode } = props;
  const { tenantId } = appStore;
  const [data, setData] = useState();
  const getData = async () => {
    const res = await fetch(`itsm/v1/${tenantId}/tasks/${ticketId}/slaGoals?activeFlag=true&businessObjectCode=${businessObjectCode}`);
    if (res && res.length > 0) {
      const _data = handleSlaData(res[0].goalItems[0], intl);
      const _user = handleUser(res[0].goalItems);
      setData({ ..._data, detailInfo: _user });
    }
  };

  useEffect(() => {
    getData();
  }, []);

  if (data) {
    return (
      <View className={styles.item} onClick={() => { openSLADetailModal(data.detailInfo, intl); }}>
        <View className={styles.line}>
          <View className={styles.ticketState} style={{ backgroundColor: data.ticketStatus.color }}>
            <View className={styles.stateIcon}>
              <Icon
                style={{ transform: 'scale(1.3)' }}
                type={data.ticketStatus.iconType}
                strokeWidth={data.ticketStatus.strokeWidth}
                size={data.ticketStatus.iconSize}
                fill={data.ticketStatus.color}
              />
            </View>
            <View className={styles.stateText}>{data.ticketStatus.meaning}</View>
          </View>
          <View style={{ fontWeight: '500', color: data.ticketStatus.color }}>
            {data.deltaMeaning}
            {data.deltaHour}
          </View>
        </View>
        <View className={styles.line}>
          <View style={{ display: 'flex', alignItems: 'center' }}>
            <Icon type="right-small" size={40} fill="#12274D" />
            <View style={{ paddingLeft: '8px' }}>已消耗{data.cost}</View>
          </View>

        </View>
        <View className={styles.line} style={{ opacity: '0.65' }}>
          {intl.formatMessage({ id: 'yqc.mobile.process.time.complete.time', defaultMessage: '预计完成时间：' })}{data.originalBreachTime}
        </View>
      </View>
    );
  } else {
    return <YqEmpty description={intl.formatMessage({ id: 'yqc.mobile.process.time.nodata', defaultMessage: '暂无时效信息' })} />;
  }
}

export default ChildTicket;
