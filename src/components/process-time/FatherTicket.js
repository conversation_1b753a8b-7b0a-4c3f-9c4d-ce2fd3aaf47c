import { View } from '@tarojs/components';
import React, { useEffect, useState } from 'react';
import { List } from '@taroify/core';
import { useIntl } from '@/util/intl';
import appStore from '@/store/app';
import { fetch, constants } from '@/util';
import Icon from '@/components/yq-icon';
import YqModal from '@/components/yq-modal';
import YqAvatar from '@/components/yq-avatar';
import YqEmpty from '@/components/yq-empty';
import styles from './ProcessTime.module.less';

export function openSLADetailModal(detailInfo, intl) {
  const modal = YqModal.open({
    title: intl.formatMessage({ id: 'yqc.mobile.process.time.detail', defaultMessage: '耗时明细' }),
    onCancel: () => { modal.close(); },
    onOk: () => {},
    hiddenOk: true,
    children: (
      <List className={styles.personList}>
        {detailInfo.map((assign) => {
          return (
            <View className={styles.personRow}>
              <View className={styles.personInfo}>
                <YqAvatar classNames={styles.mr20} personId={assign.assigneePersonId}>{assign.assigneePersonName}</YqAvatar>
                {!(assign.assigneePersonName || assign.assigneePersonName) && <View style={{ color: '#595959' }} className={styles.mr20}>{intl.formatMessage({ id: 'yqc.mobile.no.person/group', defaultMessage: '无处理人/组' })}</View>}
                <View className={styles.mr20}>{assign.assigneePersonName}</View>
                <View className={styles.mr20}>{assign.assignmentGroupName}</View>
              </View>
              <View>{minutesToHour(assign.businessElapsedDuration, intl)}</View>
            </View>
          );
        })}
      </List>
    ),
  });
}
const calDurationMinutes = (_duration) => {
  const duration = JSON.parse(_duration);
  let res = 0;
  if (duration.hours) {
    res += duration.hours * 60;
  }
  if (duration.minutes) {
    res += duration.minutes;
  }
  return res;
};

export const minutesToHour = (minutes, intl) => {
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  return intl.formatMessage({ id: 'yqc.mobile.process.time.to.hour', defaultMessage: '{hour}小时{min}分' }).replace('{hour}', hours).replace('{min}', remainingMinutes);
};

export const handleUser = (allSlas) => {
  const { breakDownList } = allSlas[0];
  return breakDownList;
};

export const handleSlaData = (sla, intl) => {
  if (sla) {
    const { TicketProcessStatus } = constants;
    const { originalBreachTime, businessElapsedDuration, stopTime, goalName } = sla;
    const cost = minutesToHour(businessElapsedDuration, intl);
    const hasStopped = !!stopTime;

    // 持续时间（对象）
    const { duration } = sla;
    const durationMin = calDurationMinutes(duration);

    // 时间差（分钟）
    const delta = businessElapsedDuration - durationMin;
    const deltaHour = minutesToHour(Math.abs(delta), intl);

    let state;
    let deltaMeaning;
    let ticketStatus;
    if (delta > 0) {
      state = 'TIMEOUT'; // 超时
      deltaMeaning = intl.formatMessage({ id: 'yqc.mobile.process.time.time.out', defaultMessage: '超时' });
    } else if (hasStopped) {
      state = 'OK'; // 达标
      deltaMeaning = intl.formatMessage({ id: 'yqc.mobile.process.time.ok', defaultMessage: '提早' });
    } else if (!hasStopped) {
      state = 'TIMING'; // 计时中
      deltaMeaning = intl.formatMessage({ id: 'yqc.mobile.process.time.timing', defaultMessage: '剩余' });
    }

    TicketProcessStatus.forEach((situation) => {
      if (situation.code === state) {
        ticketStatus = situation;
      }
    });

    return {
      title: goalName, // 标题
      originalBreachTime, // 预计完成时间
      cost, // 已消耗时间
      delta, // 为了表示时间差的正负
      deltaHour, // 时间差
      hasStopped, // 是否截止
      ticketStatus, // 状态
      deltaMeaning, // 超时/提早/剩余
    };
  }
  return null;
};

function FatherTicket(props) {
  const intl = useIntl();
  const { ticketId, businessObjectCode } = props;
  const { tenantId } = appStore;
  const getData = async () => {
    const data = [];
    const res = await fetch(`ticket/v1/${tenantId}/task/${businessObjectCode.toLowerCase()}/workflow/${ticketId}`);
    if (res) {
      res.forEach((item) => {
        if (item.sla) {
          // 筛选掉不是子节点的数据
          const _data = handleSlaData(item.sla, intl);
          const _user = handleUser(item.allSlas);
          data.push({ ..._data, title: item.name, detailInfo: _user });
        }
      });
    }
    setData(data);
  };

  const [data, setData] = useState([]);
  useEffect(() => {
    getData();
  }, []);

  return (
    <View className={styles.father}>
      {data.length > 0
        ? data.map((item, index) => {
          return (
            <View className={styles.item} onClick={() => { openSLADetailModal(item.detailInfo, intl); }}>
              <View className={styles.line}>
                <View className={styles.ticketTitle}>{item.title}</View>
                <View className={styles.ticketState} style={{ backgroundColor: item.ticketStatus.color }}>
                  <View className={styles.stateIcon}>
                    <Icon
                      style={{ transform: 'scale(1.2)' }}
                      type={item.ticketStatus.iconType}
                      strokeWidth={item.ticketStatus.strokeWidth}
                      size={item.ticketStatus.iconSize}
                      fill={item.ticketStatus.color}
                    />
                  </View>
                  <View className={styles.stateText}>{item.ticketStatus.meaning}</View>
                </View>
              </View>
              <View className={styles.line}>
                <View style={{ display: 'flex', alignItems: 'center' }}>
                  <Icon type="right-small" size={40} fill="#12274D" />
                  <View style={{ paddingLeft: '8px' }}>已消耗{item.cost}</View>
                </View>
                <View style={{ fontWeight: '500', color: item.ticketStatus.color }}>
                  {item.deltaMeaning}
                  {item.deltaHour}
                </View>
              </View>
              <View className={styles.line} style={{ opacity: '0.65' }}>
                {intl.formatMessage({ id: 'yqc.mobile.process.time.complete.time', defaultMessage: '预计完成时间：' })}{item.originalBreachTime}
              </View>
            </View>
          );
        }, [])
        : <YqEmpty description={intl.formatMessage({ id: 'yqc.mobile.process.time.nodata', defaultMessage: '暂无时效信息' })} />}
    </View>
  );
}

export default FatherTicket;
