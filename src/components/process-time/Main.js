import { useIntl } from '@/util/intl';
import { observer } from 'mobx-react-lite';
import { View } from '@tarojs/components';
import FatherTicket from './FatherTicket';
import ChildTicket from './ChildTicket';
import styles from './ProcessTime.module.less';

function ProcessTimeMain(props) {
  const intl = useIntl();
  const isFather = () => {
    return props.values['business_id:number'] === undefined && props.values['req_item_id:number'] === undefined;
  };

  return (
    <View className={styles.content}>
      <View className={styles.header}>{intl.formatMessage({ id: 'yqc.mobile.process.time', defaultMessage: '时效' })}</View>
      {isFather() ? <FatherTicket {...props} /> : <ChildTicket {...props} />}
    </View>
  );
}

export default observer(ProcessTimeMain);
