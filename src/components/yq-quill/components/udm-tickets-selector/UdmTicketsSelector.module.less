.wrapper {
  display: flex;
  flex-direction: column;
  z-index: 2024;
  bottom: 120px;
  top: 220px;
  position: absolute;
  overflow: hidden;
  width: 100%;
  max-height: calc(100vh - 8rem);
  box-shadow: 0px -10px 20px -10px #C7CEDA;
  border-radius: 20px 20px 0px 0px;
  background-color: white;

  .ticketListFrame {
    padding: 0 20px;
    .listHeader {
      font-weight: 500;
      font-size: 32px;
      height: 80px;
      //background-color: red;
      display: flex;
      align-items: center;
    }
    .listItem {
      height: 130px;
      display: flex;
      align-items: center;
      overflow: hidden;
      border-bottom: 1px solid #e5e5e5;
      .itemFrame {
        display: flex;
        align-items: center;
        //background-color: green;
        .left {
          flex-basis: 280px;
          flex-grow: 0;
          flex-shrink: 0;
          color: #2979FF;
          padding: 0 10px;
          display: flex;
          align-items: center;
          .icon {
            width: 40px;
            height: 40px;
            margin-right: 5px;
            //background-color: red;
          }
        }
        .right {
          flex-grow: 1;
          flex-shrink: 0;
          height: 100%;
          padding: 10px 0;
          line-height: 46px;
        }
      }
    }
  }
}


.globalTicketsFrame {
  //background-color: red;
  height: 120px;
  padding: 20px;
  position: relative;
  .globalTicketsInfo {
    background-color: #f2f3f5;
    width: 100%;
    height: 100%;
    display: flex;
    border-radius: 5px;
    align-items: center;
    padding: 0 20px;
    justify-content: space-between;
  }
}
