import { View } from '@tarojs/components';
import React, { useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { useIntl } from '@/util/intl';
import YqList from '@/components/yq-list';
import appStore from '@/store/app';
import GlobalPlace from '@/components/yq-modal/GlobalPlace';
import useVisualViewport from '@/hooks/use-visual-viewport';
import YqIcon from '@/components/yq-icon';
import { _ } from '@/util';
import KnowledgeCard from '../../packageOther/pages/search/components/card/knowledge';
import styles from './KnowledgeModal.module.less';

interface KnowledgeModalProps {
  show: boolean
  searchFieldName: string
  onClick: Function,
  modalTitle: string,
  setKnowledgeModal: any
}
const KnowledgeModal: React.FC<KnowledgeModalProps> = observer(({ show = false, searchFieldName, onClick = () => {}, modalTitle, setKnowledgeModal }) => {
  const viewport = useVisualViewport();
  const intl = useIntl();
  const searchUrl = `knowledge/v1/${appStore.tenantId}/know/element/mentionList?draftFlag=false`;
  const postParam = [{ type: ['knowledge'], filters: [{ key: 'fileType.keyword', value: 'FOLDER', containFlag: false }] }];
  function renderer(item) {
    const _item = { ...item, name: item.title || item.titleNormal || item.name };
    if (_item.name) {
      return (
        <View className={styles.knowledgeCard}>
          <KnowledgeCard
            {...item}
            showLabels
            content=""
            title={_item.name}
            titleNormal={item.titleNormal || _item.name}
            border
          />
          <YqIcon type="send" size={48} strokeWidth={2} fill="#2979ff" theme="filled" onClick={() => onClick(item)} />
        </View>
      );
    }
  }
  useEffect(() => {
    document.querySelector('#knowledge-modal-header')?.addEventListener('touchmove', (e) => {
      // 阻止默认事件
      e.preventDefault();
    }, {
      passive: false,
    });
  }, []);
  function handleCancel() {
    //
    setKnowledgeModal(false);
  }

  return (
    <View
      className={styles.wrapper}
      style={{
        display: show ? 'flex' : 'none',
        bottom: window.innerHeight === viewport.height ? document.querySelector('.ql-toolbar-swiper')?.clientHeight : window.innerHeight - viewport.height + 20,
      }}
    >
      <View className={styles.header} id="knowledge-modal-header">
        <View onClick={handleCancel} className={styles.cancel}>{intl.formatMessage({ id: 'yqc.mobile.cancel', defaultMessage: '取消' })}</View>
        <View className={styles.title}>{modalTitle}</View>
      </View>
      <YqList
        key={JSON.stringify(searchUrl)}
        queryMethod="POST"
        url={searchUrl}
        postParams={postParam}
        pageSize={20}
        canSearch
        hasPrefixSearchName={false}
        hasCustomPrefix={false}
        renderer={renderer}
        searchFieldName={searchFieldName}
      />
    </View>
  );
});

export default function (props) {
  return (
    <GlobalPlace>
      <KnowledgeModal {...props} />
    </GlobalPlace>
  );
}
