.wrapper {
  display: flex;
  flex-direction: column;
  z-index: 2011;
  bottom: 120px;
  top: 60px;
  position: absolute;
  overflow: hidden;
  width: 100%;
  max-height: calc(100vh - 1rem);

  box-shadow: 0px -10px 20px -10px #C7CEDA;
  border-radius: 20px 20px 0px 0px;
}
.header {
  width: 100%;
  height: 104px;
  background: #FFFFFF;
  display: flex;
  padding: 0 30px;
  position: relative;
  //justify-content: center;
  //justify-content: space-around;
  align-items: center;
  border-bottom: 1px solid rgba(203,210,220,0.5);
  justify-content: center;
}
.title {
  font-size: 32px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #12274D;
  line-height: 48px;
}
.cancel {
  position: absolute;
  left: 30px;
  font-size: 32px;
  color: rgba(18,39,77,0.65);
  line-height: 32px;
}
.knowledgeCard {
  display: flex;
  align-items: center;
  padding-right: 18px;
}