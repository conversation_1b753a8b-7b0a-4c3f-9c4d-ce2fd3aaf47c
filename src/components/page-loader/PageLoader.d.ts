import React from 'react';
import { TransferInfo } from '@/components/ticket-transfer/TicketTransfer';

/**
 * 定义页面加载器的属性。
 */
export interface PageLoaderProps {
  // 弹窗中的PageLoader的关闭方法
  modalClose?: Function;
  onModalOk?: Function;
  isDataSet?: boolean;
  style?: React.CSSProperties;
  pageTenantId?: string;
  /** 可选的视图标识符 */
  viewId?: string;

  /** 请求标识符 */
  reqId?: string;

  /** 视图代码 */
  viewCode?: string;

  /** 变量标记 */
  variableFlag?: boolean;

  /** 实例标识符 */
  instanceId?: string;

  /** 设置视图代码的函数 */
  setViewCode?: Function;

  /** 设置视图标识符的函数 */
  setViewId?: Function;

  /** 表单字段值 */
  formFieldValues?: any;

  /** 设置表单 JSON 数据的函数 */
  setFormJsonData?: any;

  /** 设置页面存储的函数 */
  setPageStore?: Function;

  /** 提交后的回调函数 */
  afterSubmit?: Function;

  /** 设置业务对象代码的函数 */
  setBusinessObjCode?: Function;

  /** 设置业务对象标识符的函数 */
  setBusinessObjId?: Function;

  /** 设置自定义按钮的函数 */
  setCustomButtons?: Function;

  /** 事件项 */
  eventItem?: any;

  /** 调查标记 */
  surveyFlag?: boolean;

  /** 是否禁用 */
  disabled?: boolean;

  /** 是否为预览模式 */
  preview?: boolean;

  /** 列表视图前缀 */
  listViewPrefix?: boolean;

  /** 隐藏按钮 */
  hiddenButtons?: boolean;

  /** 原始值 */
  pristineValue?: boolean | object;

  /** 空间背景 */
  spaceBackground?: object;

  /** 加载完成的回调函数 */
  onloaded?: Function;

  /** 刷新的回调函数 */
  onRefresh?: Function;

  /** 是否可以自我更改 */
  canChangeSelf?: boolean | undefined;

  /** 变化时的回调函数 */
  onChange?: Function;

  /** 头部后的任意内容 */
  afterHeader?: any;

  /** 缓存表单字段值 */
  cacheFormFieldsValues?: any;

  /** 实时更新的函数 */
  liveUpdate?: Function;

  /** JSON 数据 */
  jsonData?: any;

  /** 是否为静态 */
  isStatic?: boolean;

  /** 是否需要计算 */
  needCalculate?: boolean;

  /** 额外的默认数据 */
  extraDefaultData?;

  /** 页面按钮 */
  pageButtons?;

  /** 父级标识符 */
  parentId?;

  /** JSON 数据转换的函数 */
  jsonDataTransfer?: Function;

  /** 是否来自标签页 */
  fromTab?: boolean;

  /** 请求项配置 */
  requestItemConfig?: any;

  /** 封装卡片配置 */
  wrapperCardConfig?: any;

  /** 是否隐藏头部 */
  hiddenHeader?: boolean;

  /** 禁用刷新 */
  disabledRefresh?: boolean;

  /** 最大长度 */
  maxLength?: number;

  /** 空提示 */
  emptyTips?: string | React.ReactNode;

  /** 传输信息 */
  transferInfo?: TransferInfo;

  /**
   * 是否为卡片模式
   */
  cardMode?: boolean;

  /** 列表预览样式 */
  listPreviewerStyle?: React.CSSProperties;

  /** 设置无权限的函数 (高航添加) */
  setNoPermission?: Function;

  /** 是否无权限 (高航添加) */
  noPermission?: boolean;

  /** 是否首次计算（可选属性） */
  calculateFirst?: boolean;

  /**
   * 智能识单默认值字段是否隐藏
   */
  hiddenDefaultFields?: boolean;

  /**
   * 智能识单需要隐藏的字段
   */
  hiddenFields?: Array<string>;

  // 是否是【服务项视图】组件
  isServiceItemPage?: boolean;

  // 提单页面才会有
  createOrderStore?: any;
}
