@import "../../style/setting.scss";

.survey-title {
  height: 42px;
  font-size: 32px;
  font-weight: 500;
  color: #262626;
  line-height: 42px;
  margin-bottom: 20px;
  background-color: #fff;
  margin-left: 24px;
  display: flex;
  align-items: center;
}

.page-loader {
  &-wrapper {
    .taroify-tabs__tab__content {
      position: relative;
      overflow: visible;
    }
    .taroify-tabs__tab--active {
      .taroify-tabs__tab__content::after {
        content: ' ';
        border-radius: 4px;
        height: 4px;
        bottom: -23px;
        width: 100%;
        position: absolute;
        background-color: #2979ff;
      }
    }
    .taroify-tabs__line {
      display: none !important;
    }
  }

  &-btns {
    width: 100%;
    display: flex;
    padding: 12px 24px;
    font-size: 0.76rem!important;
    .taroify-button:not(:first-child) {
      margin-left: 20px;
      border-radius: 8px;
    }
    .taroify-button--default {
      border: 2px solid #2979ff;
      color: #2979ff;
    }
  }
  &-wrapper {
    height: 100%;
    overflow-y: auto;
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
    /* Hide scrollbar for Chrome, Safari and Opera */
    &::-webkit-scrollbar {
      display: none;
    }
    &-tab {
      > *:first-child {
        position: sticky;
        top: 0;
        z-index: 99;
      }
      --tabs-line-width: 100%;
      .taroify-tabs__tab {
        padding: 0;
        flex-basis: unset !important;
        margin: 0 24px;
        &:last-child {
          padding-right: 24px;
        }
      }
      .taroify-tabs__wrap {
        border-bottom: 1px solid rgba($color: #cbd2dc, $alpha: 0.5);
      }
    }
    .yq-pageloader-card {
      position: relative;
    }
  }

  &-loading {
    width: 100%;
    min-height: 200px;
  }
}

//高航
.no-permission-image{
  width:260px;
  height: 260px;
}
.no-permission-page{
  height: 100vh;
}
