import React, { useEffect, useMemo, useContext } from 'react';
import { observer, useLocalObservable } from 'mobx-react-lite';
import { toJS } from 'mobx';
import { Tabs, Loading } from '@taroify/core';
import Taro from '@tarojs/taro';
import isEmpty from 'lodash/isEmpty';
import { View } from '@tarojs/components';
import classnames from 'classnames';
import { MobXProviderContext } from 'mobx-react';
import { useIntl } from '@/util/intl';
import { _, fetch, sectionField, constants } from '@/util';
import YqSpace from '@/components/yq-space';
import YqEmpty from '@/components/yq-empty';
import { PageLoaderProps } from '@/components/page-loader/PageLoader';
import AiTranslate, { TranslateLoading } from '@/components/ai-translate';
import { viewPoliciesData, injectViewIdToField } from '../../util/sectionField';
import appStore from '../../store/app';
import ListPreviewer from '../list-previewer';
import { Card, Field } from '../index';
import PageButton from '../page-button';
import { TransferInfo } from '../ticket-transfer/TicketTransfer';
import './index.scss';
import FieldAutoComplete from '../../packageOther/pages/create-order/components/field-auto-complete';

const VIEW_TYPE_TABLE = 'TABLE';
const VIEW_TYPE_MOBILE_TABLE = 'MobileTable';

// 通过链接上的 anchorTabCode 参数，打开含有特定组件的 tab 页【注意，业务深度绑定逻辑，非通用】
const ANCHOR_MAPPING = {
  DYNAMIC: 'Dynamic',
};

const FILTER_FIELD_CODES = [
  'id',
  'created_by',
  // 'creation_date',
  // 'last_update_date',
  'last_updated_by',
  'tenant_id',
  'domain_id',
  'domain_path',
];

const PageLoader: React.FC<PageLoaderProps> = observer((props) => {
  const {
    onModalOk,
    isDataSet = false,
    style,
    cardMode,
    pageTenantId,
    viewId,
    reqId, // 请求项id
    viewCode,
    variableFlag = false,
    instanceId, // 主业务对象实例id
    setViewCode = (_v: string) => { }, // 父级没有 viewCode，需要从视图数据中返回
    setViewId = (_v: string) => { },
    setFormJsonData, // 外部传入获得 formJsonData 的方法
    setPageStore = () => { }, // 外部传入获得 store 的方法
    afterSubmit = () => { }, // 创建类型的视图提交成功后的回调
    setCustomButtons = () => { }, //
    setBusinessObjCode = (_businessObjectCode: string) => { },
    setBusinessObjId = () => { },
    eventItem,
    surveyFlag = false, // 调查模式
    disabled = false,
    preview = false, // 是否为pc的预览
    hiddenButtons = false, // 是否隐藏按钮
    listViewPrefix = false, // 当为列表视图时，是否有过滤器前缀名search_
    pristineValue = false,
    onloaded = () => { },
    spaceBackground = {}, // 间距背景色
    onRefresh = () => { },
    canChangeSelf = false, //
    onChange = () => { }, // 自己调用的字段变更方法
    afterHeader,
    cacheFormFieldsValues = {}, // 缓存的字段值，只会更新附件和富文本
    liveUpdate = () => { }, // 实时更新的方法
    isStatic = false, // 直接用现成的jsonData渲染
    jsonData = null, // isStatic=true时必填，模拟的静态数据
    needCalculate = false, // 是否需要请求额外的默认值接口
    transferInfo = {}, // 转交视图的额外信息，需要从动作管理上读取配置😅
    listPreviewerStyle = {}, // 列表视图的yq-list的style
    extraDefaultData = null,
    pageButtons = null,
    parentId = '',
    jsonDataTransfer = (v) => v,
    fromTab,
    requestItemConfig,
    wrapperCardConfig,
    hiddenHeader,
    disabledRefresh,
    maxLength,
    emptyTips,
    setNoPermission = () => { }, // 高航
    noPermission, // 高航
    calculateFirst = true, // 表示需要计算，且计算的优先级最高。如果为false则计算优先级比不过默认数据
    hiddenDefaultFields = false,
    hiddenFields = [],
    isServiceItemPage,
    modalClose, // 弹窗中view的关闭方法
    createOrderStore,
  } = props;
  let { formFieldValues } = props; // observable的对象,从外部传进来，方便外面使用}
  const intl = useIntl();
  const { tenantId } = appStore;
  const params = useMemo(() => Taro.getCurrentInstance()?.router?.params, []);
  const _parentId = parentId || params?.parentId;
  const useForAi = params?.useFor === 'ai_ticket';
  const { translateStore } = useContext(MobXProviderContext);
  function getViewQueryUrl(_tenantId: string) {
    if (surveyFlag) {
      return `asmt/v1/${_tenantId}/assessments/${viewId}`;
    }
    const urlPrefix = `lc/v1/${_tenantId}/views`;
    const url = `${urlPrefix}/form/${variableFlag ? 'variable/' : ''}${viewId || `code/${viewCode}`}?deviceType=H5`;
    // 这里是lc/v1/${tenantId}/views/form/...?deviceType=H5的api，防止找不到
    return url;
  }

  const calcBtnFromComponent = (theJsonData: any) => {
    // tab模式下，回复和工时页面，各自浮个添加按钮
    // 非tab模式下，回复和工时页面，可能同时复现两个按钮
    const arr = [] as any;
    // section 可能因为条件设置而隐藏，在其下面的评论组件则不应该渲染全局回复入口
    //  所以需要记录所有有 Comment 的 section，在 task-detail 页面渲染的时候以判断是否需要渲染全局回复入口
    //   另外，暂时不支持整棵树查询，只支持直接父级 section
    const sectionWithCommonList: any = [];
    let hasWorkHour = false;
    let hasComment = false;
    let commentWidgetConfig = {};
    theJsonData?.sections?.forEach(section => {
      section.fields?.forEach?.(field => {
        if (field.widgetType === 'TicketWorkHour') {
          const target = {
            widgetType: field.widgetType,
            sectionParentId: section.parentId,
          };
          hasWorkHour = true;
          arr.push(target);
        }
        if (field.widgetType === 'Comment') {
          const target = {
            widgetType: field.widgetType,
            sectionParentId: section.parentId,
          };
          commentWidgetConfig = field.widgetConfig;
          hasComment = true;
          arr.push(target);
        }
      });
    });

    let workhourInTab = false;
    let commentInTab = false;
    let workHourTabId = '';
    let commentTabId = '';
    arr.forEach(target => {
      if (target.sectionParentId) {
        const parentSection = theJsonData.sections.find(v => v.id === target.sectionParentId);
        if (parentSection && parentSection.tag === 'Tab') {
          if (target.widgetType === 'TicketWorkHour') {
            workhourInTab = true;
            workHourTabId = target.sectionParentId;
          }
          if (target.widgetType === 'Comment') {
            commentInTab = true;
            commentTabId = target.sectionParentId;
            sectionWithCommonList.push(target.sectionParentId);
          }
        }
      }
    });
    return {
      hasWorkHour,
      hasComment,
      workhourInTab,
      commentInTab,
      workHourTabId,
      commentTabId,
      commentWidgetConfig,
      sectionWithCommonList,
    };
  };

  const store = useLocalObservable(() => ({
    requestItemConfig,
    data: {
      viewType: undefined,
      id: undefined,
    },
    hasButton: false,
    refreshKey: 0 as number,
    pristineValue: {} as any,
    jsonData: {} as any,
    policiesData: null as any,
    transferInfo: {} as TransferInfo,
    loadError: false,
    loading: true,
    viewCode: undefined,
    viewId: undefined,
    businessObjectId: undefined,
    businessObjectCode: undefined,
    flattenJsonData: undefined as any,
    btnFromComponent: {} as any, // 记录是否同时有回复、工时组件，及是否有tab
    tabs: [] as any,
    tabCurrent: 0,
    scope: `pageloader-${_.uuid()}`,
    setDefaultFlag(flag) { this.defaultFlag = flag; },

    async fetchInstanceData(_viewId, _tenantId: string) {
      try {
        if (instanceId && instanceId !== 'new') {
          if (surveyFlag) {
            const url = `asmt/v1/${_tenantId}/assessment_instances/query/${instanceId}`;
            const res = await fetch(url).catch(() => { });
            return res?.[0];
          } else {
            const datasetId = isDataSet ? store.jsonData?.datasets?.[0]?.id : _viewId;
            const url = `lc/v1/engine/${pageTenantId || _tenantId || tenantId}/dataset/${_viewId}/${datasetId}/query`;
            const res = await fetch(url, { id: instanceId, __page_params: { __parent_id: instanceId } }, 'post')
              .catch((err) => {
                if (err.data.code === 'Type Convert Failed') {
                  setNoPermission(true);
                }
              });
            if (res.empty) {
              setNoPermission(true);
            }

            Taro.eventCenter.trigger('refreshList', res?.content);
            return res?.content?.[0];
          }
        } else {
          return {};
        }
      } catch (err) {
        console.error(err.message);
      }
    },
    async fetch(_tenantId: string) {
      let returnValue;
      try {
        store.loading = true;

        if (!viewId && !viewCode) {
          return;
        }
        const res = await fetch(getViewQueryUrl(_tenantId || appStore.tenantId)).catch(() => { });
        const policiesData = hiddenDefaultFields ? {} : _.transformCondition(res?.policies, undefined);
        store.policiesData = policiesData;
        if (res?.failed) {
          store.loadError = true;
          Taro.atMessage({
            type: 'error',
            message: res.message,
          });
        } else {
          viewPoliciesData.set(res.id, policiesData);
          const transferredJsonData = injectViewIdToField(
            res?.id,
            jsonDataTransfer(JSON.parse(res?.mobileJsonData || res?.jsonData || '{}')),
          );
          store.loadError = false;
          store.data = res || {};
          store.viewCode = res?.code;
          store.viewId = res?.id;
          store.businessObjectId = res?.businessObjectId;
          store.businessObjectId && instanceId && fetchServiceSetting();
          store.businessObjectCode = res?.businessObjectCode;
          store.jsonData = transferredJsonData;
          store.flattenJsonData = transferredJsonData;
          store.btnFromComponent = calcBtnFromComponent(transferredJsonData);
          const tree = _.arrToTree(store.jsonData.sections);
          store.jsonData.sections = tree;
          setViewCode(res?.code);
          setViewId(res?.id);
          res.businessObjectCode && setBusinessObjCode(res.businessObjectCode);
          res.businessObjectId && setBusinessObjId(res.businessObjectId);
          store.jsonData.buttons && setCustomButtons(store.jsonData.buttons);
          setFormJsonData({
            ...store.flattenJsonData,
            mobileFlag: !!res?.mobileJsonData,
          });
          if (needCalculate) {
            const parent = JSON.parse(decodeURIComponent(params?.parentParams || '{}'));
            const defaultValueCalculate = await fetch(`lc/v1/engine/${appStore.tenantId}/dataset/${store.viewId}/${store.viewId}/calculate`, { _parentId, ...parent }, 'POST');
            sectionField.assignDefaultValue(formFieldValues, store.jsonData, defaultValueCalculate, undefined, calculateFirst);
          }
          if (extraDefaultData) {
            Object.assign(formFieldValues, _.transformColonObject(extraDefaultData));
            // sectionField.assignDefaultValue(formFieldValues, store.jsonData, );
          }
          if (res?.viewType !== VIEW_TYPE_TABLE && res?.viewType !== VIEW_TYPE_MOBILE_TABLE && instanceId) {
            const instanceData = await store.fetchInstanceData(res.id, _tenantId).catch(e => { console.error('e == ', e); });
            returnValue = instanceData;
            if (instanceData) {
              const transData = _.transformColonObject(instanceData);
              Object.assign(formFieldValues, {
                ...transData,
                __pristineValue__: transData,
              });
              Object.assign(store.pristineValue, transData);
              Object.assign(cacheFormFieldsValues, transData);
            } else {
              Object.assign(store.pristineValue, pristineValue);
            }
          } else {
            // Object.assign(store.pristineValue, pristineValue);
          }
          setPageStore(store);
        }
      } catch (e) {
        store.loadError = false;
        console.error(e);
      } finally {
        store.loading = false;
      }
      return returnValue;
    },

    /**
     * 参数只有在表格行操作才会有这两个参数，譬如行删除
     * @param type 请求类型
     * @param listItem 列表行数据
     * @returns
     */
    async submitData(type = '', listItem = null, expressionId = '', disabledFetch = false) {
      if (onModalOk) {
        await onModalOk();
        return;
      }
      const { isPassed, submitData } = sectionField.getLowcodeSubmitData(listItem || formFieldValues, store.jsonData, params, intl);
      const haveParent = _.isJSON(decodeURIComponent(params?.parentParams || ''));
      let yqStatus = '';
      if (type === 'CREATE') {
        yqStatus = 'create';
      } else if (type === 'DELETE') {
        yqStatus = 'delete';
      } else if (store.data.viewType === 'INSERT') {
        yqStatus = 'create';
      } else {
        yqStatus = 'update';
      }
      const data = {
        ...submitData,
        // eslint-disable-next-line no-nested-ternary
        _status: yqStatus,
      };
      // END: 处理结束
      if (!isPassed) return;
      try {
        const url = expressionId ? `lc/v1/engine/${appStore.tenantId}/dataset/${store.data.id}/executeButton/${expressionId}` : `lc/v1/engine/${tenantId}/dataset/${store.data.id}/${store.data.id}/submit`;
        const res = await fetch(url, expressionId ? data : [data], 'post', false, expressionId ? { dataType: 'text' } : undefined);
        if (disabledFetch) {
          const _ovn = formFieldValues?.object_version_number;
          const ovn = Number.isInteger(_ovn) ? _ovn : parseInt(_ovn, 10);
          Object.assign(formFieldValues, { object_version_number: (ovn || 0) + 1 });
        } else {
          await store.fetch(tenantId);
        }
        store.refreshKey += 1;
        onRefresh();
        Taro.atMessage({
          type: 'success',
          message: disabledFetch ? intl.formatMessage({ id: 'yqc.mobile.modified.success', defaultMessage: '修改成功' }) : intl.formatMessage({ id: 'yqc.mobile.submit.success', defaultMessage: '提交成功' }),
        });
        Taro.eventCenter.trigger('refreshList', res);

        if (haveParent) {
          _.navigateBack();
        }
        afterSubmit(res);
      } catch (err) {
        console.error(err);
      }
    },
  }));

  async function fetchServiceSetting() {
    if (_.isEmpty(instanceId)) return;
    const url = `itsm/v1/${appStore.tenantId}/service_settings/apply?businessObjectId=${store.businessObjectId}&ticketId=${instanceId}`;
    try {
      appStore.serviceSetting = await fetch(url);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('error -- ', error);
    }
  }

  useEffect(() => {
    if (isStatic) {
      // 由于 store.viewId 的赋值是在 store.fetch 中进行的
      // 如果判定为 static 模式的话，不再执行 store.fetch，所以要在这里将 viewId 赋值下，因为有些组件需要 viewId 的，例如 MultipleChoice
      if (viewId) {
        store.viewId = viewId as any;
      }
      if (viewCode) {
        store.viewCode = viewCode as any;
      }
      return;
    }
    if (tenantId) {
      store.fetch(tenantId);
    }
    setPageStore(store);
    onloaded();
  }, [tenantId, isStatic, viewId]);

  useEffect(() => {
    // src/components/service-item-page/Main.tsx:L51
    // 对服务请求类型的服务项视图，会将表单数据赋值给 jsonData
    if (jsonData && !isServiceItemPage) {
      store.loading = false;
      store.jsonData = jsonData;
    }
  }, [jsonData]);

  useEffect(() => {
    if (transferInfo?.transferTypeFlag) {
      store.transferInfo = transferInfo;
    }
  }, [transferInfo]);

  function doChangeValue(name, _value) {
    Object.keys(formFieldValues).forEach(key => {
      if (key.startsWith(`${name}:`)) {
        // formFieldValues[key] = undefined;
        const [, lastKey] = key.split(`${name}:`);
        if (lastKey) {
          formFieldValues[key] = _value ? _value?.[lastKey] : undefined;
        }
      }
    });
    const fields = store.flattenJsonData?.sections?.reduce((pre, cur) => [...pre, ...(cur.fields || [])], []) || [];
    fields.forEach((field) => {
      field.widgetConfig?.variableFilter?.forEach(f => {
        const relatedFieldCode = f?.relatedFieldCode;
        if (relatedFieldCode === name) {
          formFieldValues[field.code] = undefined;
          Object.keys(formFieldValues).forEach(key => {
            if (key.startsWith(`${field.code}:`)) {
              formFieldValues[key] = undefined;
            }
          });
        }
      });
    });

    formFieldValues[name] = typeof _value === 'string' ? _value.trim() : _value;
  }

  const handleChange = (value, name, _field, _passed, rootSection) => {
    if (canChangeSelf) {
      onChange(value, name);
      return;
    }
    const { policiesData } = store;
    // 使用标准转交组件的话，预置的人员值列表是一个中间表，不能使用id，而要使用 userId
    if (_field?.widgetType === 'TicketTransfer' && (name === 'person_id' || name === 'assignee_person_id')) {
      if (value?.user_id || value?.userId) {
        value.__relatedTempField = 'user_id,userId';
      }
    }

    if (rootSection?.code?.startsWith('variable_set')) {
      formFieldValues = Object.assign(formFieldValues, _.transVariableSetToObj(formFieldValues));
      formFieldValues[`${rootSection.code}.${name}`] = value;
      formFieldValues[`${name}`] = value;
      formFieldValues[rootSection.code] = JSON.stringify({
        ...(typeof formFieldValues[rootSection.code] === 'object' ? formFieldValues[rootSection.code] : JSON.parse(formFieldValues[rootSection.code] || '{}')),
        [name]: value,
      });
    } else {
      // 将MasterDetail字段其他属性也设置到formFieldValues，用于LOV属性的级联带出
      if (_field?.widgetType === 'MasterDetail') {
        const fields = store.flattenJsonData?.sections?.reduce((pre, cur) => [...pre, ...(cur.fields || [])], []) || [];
        Object.keys(value).forEach((fieldKey) => {
          if (!FILTER_FIELD_CODES.includes(fieldKey)) {
            const otherFieldCode = `${_field.code}:${fieldKey}`;
            const fieldConfig = fields.find(field => field.code === otherFieldCode);
            if (fieldConfig?.widgetType === 'RichText') {
              try {
                const richTextValue = _.transRichTextToHtml(value[fieldKey]);
                doChangeValue(otherFieldCode, richTextValue);
              } catch (e) {
                doChangeValue(otherFieldCode, value[fieldKey]);
              }
            } else {
              doChangeValue(otherFieldCode, value[fieldKey]);
            }
          }
        });
      }
      doChangeValue(name, value);
    }

    // 初始数据，只更新附件和富文本
    if ([constants.FORM_TYPE.UPLOAD, constants.FORM_TYPE.RICHTEXT].includes(_field?.widgetType)) {
      if (rootSection?.code?.startsWith('variable_set')) {
        cacheFormFieldsValues[rootSection.code] = JSON.stringify({
          ...(typeof cacheFormFieldsValues[rootSection.code] === 'object'
            ? cacheFormFieldsValues[rootSection.code]
            : JSON.parse(cacheFormFieldsValues[rootSection.code] || '{}')),
          [name]: value,
        });
      } else {
        cacheFormFieldsValues[name] = value;
      }
      if (_field?.widgetConfig?.liveUpdate) { // 实时保存
        liveUpdate(_field, value);
      }
    }

    /**
     * 处理多对一映射字段的逻辑
     * 只有多对一才有映射字段
     */
    if (_field?.widgetType === 'MasterDetail') {
      let filedPrefix = '';
      if (_field.variableSetFlag) {
        filedPrefix = name?.split('.')[0];
      }
      // 判断m2o字段是否进行了赋值
      const mappingConfigList = _field?.widgetConfig?.mappingField || [];
      mappingConfigList.forEach((mapping) => {
        const { mappingFieldCode: _mappingFieldCode, sourceFieldCode } = mapping || {};
        // 变量集中字段需要拼接上变量集code
        const mappingFieldCode = filedPrefix ? `${filedPrefix}.${_mappingFieldCode}` : _mappingFieldCode;
        let mappingField;

        // 从 sections 中获取字段比从 datasets 中获取更准确
        (store.jsonData?.sections || []).forEach(section => {
          section?.fields?.forEach(field => {
            if (field.code === mappingFieldCode) {
              mappingField = field;
            }
          });
        });

        if (mappingFieldCode && sourceFieldCode) {
          if (value && value[sourceFieldCode]) {
            // 如果被映射的字段是m2o，则默认尝试去当前值列表中查找名称字段，进行赋值操作
            if (mappingField && mappingField?.widgetType === 'MasterDetail') {
              const { relationLovNameFieldCode = 'name' } = mappingField?.widgetConfig || {};
              const realValue = _.transformColonObject(value);
              formFieldValues[mappingFieldCode] = {
                id: value[sourceFieldCode],
                [relationLovNameFieldCode]: value[`${sourceFieldCode}:${relationLovNameFieldCode}`] || value[relationLovNameFieldCode],
                ...realValue?.[mappingField?.code],
              };
            } else if (mappingField && mappingField?.widgetType === 'RichText') {
              const richText = value[`${sourceFieldCode}-backup`];
              try {
                const richTextValue = JSON.parse(richText);
                formFieldValues[mappingFieldCode] = _.transRichTextToHtml(richTextValue);
              } catch (e) {
                formFieldValues[mappingFieldCode] = richText;
              }
            } else {
              formFieldValues[mappingFieldCode] = value[sourceFieldCode];
            }
          } else {
            formFieldValues[mappingFieldCode] = undefined;
          }
        }
      });
    }

    if (policiesData) {
      const { fieldConditionMap, settingMap, conditionMap } = policiesData;
      if (fieldConditionMap && fieldConditionMap[name]) {
        const fieldConditionIds = fieldConditionMap[name];
        fieldConditionIds?.map(conditionId => {
          const result = _.calculateConditions(undefined, conditionMap, [conditionId], formFieldValues);
          const settings = settingMap[conditionId];
          if (result && settings) {
            settings.map(settingItem => {
              const {
                field, filter, fieldValue, widgetType, fieldLovValue,
                executeType, expression,
              } = settingItem;
              if (executeType !== 'EXPRESSION') {
                if (_.OPERATOR.IS_ASSIGN_VARIABLE === filter) { // 根据其他字段取值
                  formFieldValues[field] = fieldValue && formFieldValues[fieldValue];
                } else if (_.OPERATOR.NULL === filter) { // 设置为null
                  doChangeValue(field, undefined);
                } else if (widgetType === 'MasterDetail') { // 默认设置固定值
                  let lovValue;
                  try {
                    lovValue = JSON.parse(fieldLovValue);
                  } catch (e) {
                    lovValue = false;
                  }
                  doChangeValue(field, lovValue || fieldValue);
                } else {
                  doChangeValue(field, fieldValue);
                }
              } else {
                _.calculateExpression(expression, formFieldValues, appStore.tenantId);
              }
              return settingItem;
            });
          }
          return conditionId;
        });
      }
    }
  };

  function getCondition(section, allSections) {
    const isSection = section.tag === 'Section';
    const isRootSection = (isSection && section.parentId === null) || allSections.every(_section => _section.id !== section.parentId);
    const isTab = section.tag === 'Tab';
    return { isSection, isRootSection, isTab };
  }

  function getSubSections(allSections, section) {
    return allSections.filter(s => s.tag === 'Tab' && s.parentId === section.id);
  }

  function calculateSectionVisible(section) {
    if (preview && section.visibleType === 'CONDITION') return true;
    if (section.visibleType === 'CONDITION') {
      if (section.permissionFlag !== false) {
        // 有权限判断ui规则
        return (_.calculateConditions(undefined, {}, undefined, formFieldValues, section.visibleCondition, undefined, section.visibleAction));
      } else {
        // 没权限直接隐藏
        return false;
      }
    } else return section.visibleType !== 'ALWAYS_NOT_VISIBLE';
  }

  const renderFormView = () => {
    const { sections } = store.jsonData;
    const allSections = (sections || []).filter((section) => {
      return !section.validateSection && calculateSectionVisible(section);
    });
    const traversalRender = (_sections, isTop = false, disabledAll = false) => _sections?.map((section, sectionIndex) => {
      let children = [];
      if (!calculateSectionVisible(section)) {
        return null;
      }
      if (section?.children?.length) {
        children = traversalRender(section.children, undefined, disabledAll);
      }
      const hasSpace = sectionIndex + 1 < _sections.length && (section?.fields || []).length > 0;
      // eslint-disable-next-line prefer-const
      let { isSection, isRootSection, isTab } = getCondition(section, allSections);
      // isRootSection = false;
      if (section.tag === 'Tab' && section.children?.length > 0) {
        isRootSection = false;
      }
      const showHeader = (section?.fields || []).filter((field) => {
        if (preview && field.widgetConfig.visibleType === 'CONDITION') return true;
        const visible = field.widgetConfig.visibleType === 'CONDITION'
          ? _.calculateConditions(undefined, {}, undefined, formFieldValues, field.widgetConfig.visibleCondition, undefined)
          : field.widgetConfig.visibleType !== 'ALWAYS_NOT_VISIBLE';
        return visible;
      }).length > 0;

      const cascadeMap = _.getCascadeMap(section?.fields);

      if (isRootSection) {
        return (
          <View key={section.id} style={{ display: translateStore?.translateLoadingFlag ? 'none' : 'block' }}>
            <Card
              pageStore={store}
              title={section.name}
              showHeader={showHeader && section.mode === 'STANDARD'}
              style={{ marginBottom: 0 }}
              className="yq-pageloader-card"
              cardFlag={section.mode === 'CARD' && !isStatic && showHeader}
              formFieldValues={formFieldValues}
              section={section}
            >
              {/* 普通section的field解析 */}
              {(section?.fields || []).slice().sort((s1, s2) => s1.xPosition - s2.xPosition).map(field => {
                const { conditionMap, actionMap } = store.policiesData || {};
                const fieldAction = actionMap?.[field.code];
                let visible = hiddenDefaultFields && hiddenFields.includes(field?.code) ? false : _.conditionVisible(field, formFieldValues, conditionMap, fieldAction);
                if (preview && field?.widgetConfig?.visibleType === 'CONDITION') visible = true;
                if (!visible) return null;
                const fieldElement = (
                  <>
                    <Field
                      cardMode={(section.mode === 'CARD' && !isStatic) || cardMode}
                      surveyFlag={surveyFlag}
                      alwaysShow={visible}
                      section={section}
                      disabled={disabled || disabledAll}
                      key={`field${field.id}${store.refreshKey}`}
                      intl={intl}
                      onChange={handleChange}
                      onSelected={handleChange}
                      viewCode={viewCode || store.viewCode}
                      businessObjectId={store.businessObjectId}
                      businessObjectCode={store.businessObjectCode}
                      reqId={reqId}
                      viewId={store.viewId}
                      ticketId={formFieldValues.id}
                      field={field}
                      value={formFieldValues?.[field.code]}
                      values={formFieldValues}
                      cacheFormFieldsValues={cacheFormFieldsValues}
                      liveUpdate={liveUpdate}
                      eventItem={eventItem}
                      pageStore={store}
                      afterHeader={afterHeader}
                      variableFlag={variableFlag}
                      cascadeMap={cascadeMap}
                      useForAi={useForAi}
                      createOrderStore={createOrderStore}
                    />
                    <AiTranslate from="PAGE_LOADER" code={field?.code} />
                    {!useForAi && field?.widgetConfig?.fieldAutoCompleteFlag ? (
                      <FieldAutoComplete field={field} value={formFieldValues?.[field.code]} />
                    ) : null}
                  </>
                );
                return fieldElement;
              })}
              {/* 顶层的Section单独处理 */}
              {traversalRender(getSubSections(allSections, section), undefined, disabledAll)}
            </Card>
            {hasSpace && <YqSpace style={spaceBackground} />}
            {children}
          </View>
        );
      }

      // 注意：每个Tab一定会在Section下
      // 这里目的是为了确保第一层不渲染tab
      if (isTop) return null;
      if (isTab) {
        let parent = allSections.find(s => s.id === section.parentId);

        if (parent?.fields?.length > 0 && (!section.fields || section.fields?.length === 0)) {
          return null;
        }
        // console.log('parent.tag', parent.tag);
        if (section.tag === 'Tab' && section.children?.length > 0) {
          parent = section;
        }

        if (parent.tag === 'Section' || parent.tag === 'Tab') {
          store.tabs = section.children;
          // URL 添加参数定位到指定的 Tab
          const getTabOptions = () => {
            try {
              if (params?.anchorCode && section.children?.length) {
                const anchor = ANCHOR_MAPPING[params.anchorCode];
                if (anchor) {
                  const flattenSections = store.flattenJsonData?.sections || [];
                  const target = flattenSections.find(s => {
                    const cpm = (s.fields || []).find(field => field.tag === anchor);
                    return !!cpm;
                  });
                  const findTabParent = (node) => {
                    if (node?.tag === 'Tab') {
                      return node;
                    } else if (node.parentId) {
                      const p = flattenSections.find(s => (s.id === node.parentId));
                      if (p?.tag === 'Tab') {
                        return p;
                      } else if (p?.parentId) {
                        return findTabParent(p);
                      }
                      return null;
                    }
                  };
                  if (target) {
                    const topSection = findTabParent(target);
                    const visibleTabs = (section.children || []).filter(st => {
                      const hidden = st.visibleType === 'ALWAYS_NOT_VISIBLE';
                      const conditionHidden = st.visibleType === 'CONDITION' && !calculateSectionVisible(st);
                      return !(hidden || conditionHidden);
                    });
                    const activeIndex = visibleTabs.findIndex(t => t.id === topSection.id);
                    if (activeIndex > -1) {
                      return {
                        defaultValue: activeIndex,
                      };
                    }
                  }
                }
              }
            } catch (e) {
              // 逻辑不影响渲染
            }
            return null;
          };
          return (
            <Tabs
              sticky
              key={section.id}
              className="page-loader-wrapper-tab"
              onChange={(v) => {
                store.tabCurrent = v;
              }}
              {...(getTabOptions() || {})}
            >
              {section.children?.length ? section.children.map((tab) => {
                if (tab.visibleType === 'ALWAYS_NOT_VISIBLE') return null;
                if (tab.visibleType === 'CONDITION' && !calculateSectionVisible(tab)) {
                  return null;
                }
                return (
                  <Tabs.TabPane key={tab.id} title={tab.name}>
                    {traversalRender(tab.children, undefined, disabledAll)}
                  </Tabs.TabPane>
                );
              }) : null}
            </Tabs>
          );
        }
      }
      if (isSection) {
        return (
          <View key={section.id}>
            <Card
              pageStore={store}
              title={section.name}
              showHeader={section.mode === 'STANDARD'}
              cardFlag={section.mode === 'CARD' && !isStatic && showHeader}
              style={{ marginBottom: 0 }}
              formFieldValues={formFieldValues}
              section={section}
            >
              {/* 普通section的field解析 */}
              {(section?.fields || []).slice().sort((s1, s2) => s1.xPosition - s2.xPosition).map(field => {
                const visible = !(hiddenDefaultFields && hiddenFields.includes(field.code));
                if (!visible) return null;

                return (
                  <Field
                    cardMode={(section.mode === 'CARD' && !isStatic) || cardMode}
                    disabled={disabled || disabledAll}
                    surveyFlag={surveyFlag}
                    key={`field${field.id}`}
                    intl={intl}
                    section={section}
                    onChange={handleChange}
                    onSelected={handleChange}
                    viewCode={viewCode || store.viewCode}
                    businessObjectId={store.businessObjectId}
                    businessObjectCode={store.businessObjectCode}
                    reqId={reqId}
                    viewId={store.viewId}
                    field={field}
                    ticketId={formFieldValues.id}
                    value={formFieldValues?.[field.code]}
                    values={formFieldValues}
                    cacheFormFieldsValues={cacheFormFieldsValues}
                    liveUpdate={liveUpdate}
                    eventItem={eventItem}
                    pageStore={store}
                    afterHeader={afterHeader}
                    variableFlag={variableFlag}
                    cascadeMap={cascadeMap}
                    createOrderStore={createOrderStore}
                  />
                );
              })}
            </Card>
            {hasSpace && <YqSpace style={spaceBackground} />}
            {children}
          </View>
        );
      }
      return null;
    });
    if (wrapperCardConfig) {
      return (
        <Card
          pageStore={store}
          title={intl.formatMessage({ id: 'yqc.mobile.requestcontent', defaultMessage: '请求内容' })}
          price={wrapperCardConfig.price}
          quantity={wrapperCardConfig.quantity}
          // showHeader={section.mode === 'STANDARD'}
          cardFlag
          titleStyle={{ marginBottom: 0, backgroundColor: wrapperCardConfig.color, lineHeight: 'unset' }}
          formFieldValues={formFieldValues}
          section={allSections}
        >
          {traversalRender(allSections, true, true)}
        </Card>
      );
    }
    return traversalRender(allSections, true, wrapperCardConfig || requestItemConfig);
  };

  const renderView = () => {
    if (store.loadError && isEmpty(store.jsonData)) {
      return intl.formatMessage({ id: 'yqc.mobile.errorloadingjsondata', defaultMessage: '加载出错了' });
    }

    if (store.data?.viewType === VIEW_TYPE_TABLE || store.data?.viewType === VIEW_TYPE_MOBILE_TABLE) {
      return (
        <ListPreviewer
          emptyTips={emptyTips}
          hiddenHeader={hiddenHeader}
          disabledRefresh={disabledRefresh}
          maxLength={maxLength}
          fromTab={fromTab}
          store={store}
          listViewPrefix={listViewPrefix}
          listStyle={listPreviewerStyle}
        />
      );
    }

    return renderFormView();
  };

  const filterButtons = (button) => {
    // 按钮组先直接过滤，其内部内容直接平铺展示
    return button.tag !== 'ButtonGroup' && button.tag !== 'ButtonAction' && button?.type !== 'QRCode';
  };

  const renderPageBtn = () => {
    if (hiddenButtons || formFieldValues?.id || preview) return;
    const haveParent = _.isJSON(decodeURIComponent(params?.parentParams || ''));
    const buttons: Array<any> = pageButtons || store.jsonData?.buttons?.filter?.(filterButtons);
    if (buttons?.length > 0) {
      store.hasButton = true;
    }
    return buttons?.length > 0 ? (
      <View className="page-loader-btns">
        {buttons.map(button => (
          <PageButton
            modalClose={modalClose}
            store={store}
            {...button}
            key={button.id}
            button={button}
            formFieldValues={formFieldValues}
            onSubmit={store.submitData}
            viewId={button.viewId || store.data.id}
            haveParent={haveParent}
          />
        ))}
      </View>
    ) : null;
  };

  // 高航：这里是一个可以写判断的地方
  if (noPermission) {
    return (
      <YqEmpty type="permission" description={intl.formatMessage({ id: 'yqc.mobile.no.permission.to.view', defaultMessage: '暂无权限查看' })} />
    );
  }

  const renderViewAndLoading = () => {
    if (store.loading) return <Loading className="page-loader-loading" />;
    // 之所以这么写，因为要避免翻译的loading界面使得renderView不必要的重新加载
    return (
      <>
        {translateStore?.translateLoadingFlag && <TranslateLoading />}
        {renderView()}
      </>
    );
  };
  return (
    <>
      <View id={store.scope} style={style} className={classnames('page-loader-wrapper', { survey: surveyFlag, hasButton: store.hasButton })}>
        {renderViewAndLoading()}
      </View>
      {!store.loading && renderPageBtn()}
    </>
  );
});

PageLoader.defaultProps = {
  variableFlag: false,
  fromTab: false,
  setBusinessObjId: () => { },
  setFormJsonData: () => { },
  afterSubmit: () => { },
};

export default PageLoader;
