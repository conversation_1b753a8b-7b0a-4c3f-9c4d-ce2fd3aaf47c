import React from 'react';
import { useIntl } from '@/util/intl';
import { View } from '@tarojs/components';
import { _ } from '@/util';
import styles from './index.module.less';

interface EmptyTipsProps {
  lineWidth?: number;
  tips?: string | React.ReactNode;
}

const EmptyTips: React.FC<EmptyTipsProps> = function EmptyTips({
  lineWidth,
  tips = '',
}) {
  const intl = useIntl();

  return (
    <View className={styles.emptyTips}>
      <View className={styles.emptyTipsLine} style={{ width: _.pxToRem(lineWidth) || '' }} />
      <View className={styles.emptyTipsText}>
        {tips || intl.formatMessage({ id: 'yqc.mobile.no.more.content', defaultMessage: '没有更多内容了' })}
      </View>
      <View className={styles.emptyTipsLine} style={{ width: _.pxToRem(lineWidth) || '' }} />
    </View>
  );
};

export default EmptyTips;
