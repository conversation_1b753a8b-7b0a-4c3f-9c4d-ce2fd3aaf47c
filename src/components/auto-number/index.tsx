import React from 'react';
import { observer } from 'mobx-react-lite';
import { BaseEventOrig, InputProps } from '@tarojs/components';
import { Input } from '@taroify/core';
import MbRow from '@/components/mb-row';
import { AutoNumberProps } from './AutoNumber';

const AutoNumber: React.FC<AutoNumberProps> = function AutoNumber(props) {
  const {
    value, field, values, title, onChange = () => {},
    disabled = false, required = false, placeholder = '', cardMode = false,
  } = props;

  const handleChange = (e: BaseEventOrig<InputProps.inputEventDetail>) => {
    const v = e.detail.value;
    onChange(v);
  };

  return (
    <MbRow cardMode={cardMode} disabled={disabled} required={required} field={field} values={values} label={title}>
      <Input value={value} disabled={disabled} placeholder={placeholder} onChange={handleChange} />
    </MbRow>
  );
};

export default observer(AutoNumber);
