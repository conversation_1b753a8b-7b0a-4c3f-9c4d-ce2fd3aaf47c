import React, { useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Rate } from '@taroify/core';
import YqIcon from '@/components/yq-icon';
import MbRow from '@/components/mb-row';
import { MbRateProps } from './MbRate';

import styles from './Rate.module.less';

// TODO: 再次点击相同值时，无法清除，count数量过多时一行放不下，两行onChange会有问题
function MbRate(props: MbRateProps): React.ReactElement {
  const { onChange = () => {}, value, title, field, disabled = false, rateCount = 5, required = false } = props;

  const { rateCount: configCount, rateSize = 16, rateCheckedColor = '#FDCA23', rateUncheckedColor = '#E5E8ED' } = field?.widgetConfig || {};

  const count = useMemo(() => (configCount || rateCount), [configCount, rateCount]);
  return (
    <MbRow
      cardMode={props.cardMode}
      field={field}
      disabled={disabled}
      copyable={false}
      label={title}
      required={required}
      cellClassName={styles.wrapper}
      value={(
        // 点击和上一次相同的值时无法触发onChange...
        <Rate
          // allowHalf
          icon={field?.widgetConfig?.rateCharacter ? <YqIcon type={field.widgetConfig.rateCharacter} theme="filled" size={rateSize} fill={rateCheckedColor} /> : undefined}
          emptyIcon={field?.widgetConfig?.rateCharacter ? <YqIcon type={field.widgetConfig.rateCharacter} theme="outline" size={rateSize} fill={rateUncheckedColor} /> : undefined}
          disabled={disabled}
          count={count}
          size={count > 8 ? 36 : 48}
          value={value}
          onChange={onChange}
        />
      )}
    />
  );
}

export default observer(MbRate);
