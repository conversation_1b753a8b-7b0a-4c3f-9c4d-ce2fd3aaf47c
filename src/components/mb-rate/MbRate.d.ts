/* eslint-disable no-unused-vars */
import { IntlShape } from '@/util/intl';

interface WidgetConfig {
  rateCount: number | null; // 评分数量 居然还能为null
  rateCharacter: string; // 移动端就默认是star，暂时不用其他的图标
  allowClear?: boolean; // 再次点击时清除已有数据
  rateSize?: number; // 图标大小
  rateCheckedColor?: string; // 图标选中颜色
  rateUncheckedColor?: string; // 图标未选中颜色
}

interface Field {
  code: string;
  name: string;
  id: string;
  defaultValue?: string;
  widgetConfig: WidgetConfig;
  widgetType: string;
}

export interface MbRateProps {
  onChange: (value: any) => void;
  intl: IntlShape;
  title: string;
  value: any;
  disabled?: boolean;
  rateCount?: number;
  required?: boolean;
  field?: Field;
  cardMode?: boolean;
}
