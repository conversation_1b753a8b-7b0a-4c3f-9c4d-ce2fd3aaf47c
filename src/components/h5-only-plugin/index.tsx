import { useEffect } from 'react';
import { ICON_SERVER } from '@config';
import queryString from 'query-string';
import appStore from '@/store/app';
import { fetch, _ } from '@/util';
import larkSign from '@/components/h5-only-plugin/larkSign';
import useDynamicScript from '../../hooks/use-script';

export async function wxWorkSign() {
  if (!appStore.isWxwork) return;
  setTimeout(async () => {
    const corpId = appStore.loginInfo.appid;
    const agentId = _.getLocalStorage('agentId');
    const params = queryString.stringify({
      tenantId: appStore._tenantId,
      url: window.location.href.split('#')[0],
      corpId,
      agentId,
    });
    if (!corpId || !agentId) {
      return;
    }
    const signData = await fetch(`ecos/v1/wecom/signature?${params}`);
    const signInfo = {
      timestamp: signData.enterprise.timestamp, // 必填，生成签名的时间戳
      nonceStr: signData.enterprise.noncestr, // 必填，生成签名的随机串
      signature: signData.enterprise.signature, // 必填，签名，见附录1
    };
    const agentSignInfo = {
      timestamp: signData.agent.timestamp, // 必填，生成签名的时间戳
      nonceStr: signData.agent.noncestr, // 必填，生成签名的随机串
      signature: signData.agent.signature, // 必填，签名，见附录-JS-SDK使用权限签名算法
    };
    // @ts-ignore
    wx.config({
      beta: true, // 必须这么写，否则wx.invoke调用形式的jsapi会有问题
      debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
      appId: corpId, // 必填，企业微信的corpID
      ...signInfo,
      jsApiList: ['openEnterpriseChat', 'shareWechatMessage'], // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
    });

    // @ts-ignore
    wx.error((res) => {
      // config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名。
      // eslint-disable-next-line no-console
      console.error('第一个签名报错', JSON.stringify(res));
    });
    function getConfigSignature() {
      return signInfo;
    }
    async function getAgentConfigSignature() {
      // 根据 url 生成应用签名，生成方法同上，但需要使用应用的 jsapi_ticket
      return agentSignInfo;
    }
    // @ts-ignore
    wx.ready(() => {
      setTimeout(() => {
        // @ts-ignore
        wx.agentConfig({
          corpid: corpId, // 必填，企业微信的corpid，必须与当前登录的企业一致
          agentid: agentId, // 必填，企业微信的应用id （e.g. 1000247）
          ...agentSignInfo,
          jsApiList: ['openEnterpriseChat', 'shareWechatMessage', 'onMenuShareTimeline', 'onMenuShareWechat'], // 必填，传入需要使用的接口名称
          success(res) {
            appStore.wxWorkSigned = true;
            ww.register({
              corpId, // 必填，当前用户企业所属企业ID
              agentId, // 必填，当前第三方应用的AgentID
              jsApiList: ['getExternalContact', 'getCurExternalContact', 'shareWechatMessage', 'onMenuShareTimeline', 'onMenuShareWechat', 'scanQRCode', 'sendChatMessage', 'openDefaultBrowser', 'closeWindow'], // 必填，需要使用的JSAPI列表
              getConfigSignature, // 必填，根据url生成企业签名的回调函数
              getAgentConfigSignature, // 必填，根据url生成应用签名的回调函数
              onAgentConfigSuccess: async () => {
                const externalContact = await ww.getCurExternalContact();
                appStore.externalContact = externalContact;
              },
              onConfigSuccess: async () => {
                const externalContact = await ww.getCurExternalContact();
                appStore.externalContact = externalContact;
              },
            });
          },
          fail(res) {
            // eslint-disable-next-line no-console
            console.error(JSON.stringify(res));
          },
        });
      }, 500);
      // config信息验证后会执行ready方法，所有接口调用都必须在config接口获得结果之后，config是一个客户端的异步操作，所以如果需要在页面加载时就调用相关接口，则须把相关接口放在ready函数中调用来确保正确执行。对于用户触发时才调用的接口，则可以直接调用，不需要放在ready函数中。
    });
  }, 1000);
}

export default function () {
  const userAgent = navigator.userAgent;
  const isIOS = /iPad|iPhone|iPod/.test(userAgent);
  const [heic2anyReady] = useDynamicScript({
    url: `${ICON_SERVER}/static/js/heic2any.min.js`,
    delay: 3000,
  });
  const [_vconsoleReady] = useDynamicScript({
    url: `${ICON_SERVER}/static/js/vconsole.min.js`,
    delay: 4000,
  });
  const [] = useDynamicScript({
    url: `${ICON_SERVER}/static/js/browser-image-compression.js`,
    delay: 0,
  });

  const wxSDKUrl = isIOS ? 'https://open.work.weixin.qq.com/wwopen/js/jwxwork-1.0.0.js' : 'https://res.wx.qq.com/open/js/jweixin-1.2.0.js';
  const wwSDKUrl = 'https://wwcdn.weixin.qq.com/node/open/js/wecom-jssdk-1.3.1.js';
  const [wxReady] = useDynamicScript({
    url: appStore.isWxwork ? wxSDKUrl : '',
    delay: 0,
  });

  const [wwReady] = useDynamicScript({
    url: appStore.isWxwork ? wwSDKUrl : '',
    delay: 0,
  });

  const [larkReady] = useDynamicScript({
    url: appStore.isLark ? 'https://lf1-cdn-tos.bytegoofy.com/goofy/lark/op/h5-js-sdk-1.5.19.js' : '',
    delay: 0,
  });

  useEffect(() => {
    if (heic2anyReady) {

    }
  }, [heic2anyReady]);

  useEffect(() => {
    if (larkReady) {
      larkSign();
    }
  }, [larkReady]);
  useEffect(() => {
    if (wxReady && wwReady) {
      wxWorkSign();
    }
  }, [wxReady, wwReady]);
  return null;
}
