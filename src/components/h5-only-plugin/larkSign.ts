import queryString from 'query-string';
import { fetch } from '@/util';
import appStore from '@/store/app';

export default async function larkSign() {
  const appKey = appStore.loginInfo.appid;
  const params = queryString.stringify({
    tenantId: appStore.tenantId,
    url: window.location.href.split('#')[0],
    appKey,
  });
  if (!appKey) {
    return;
  }
  // const signData = await fetch(`ecos/v1/lark/signature?${params}`);
  const result = await fetch(`ecos/v1/lark/signature?${params}`);
  const SCOPE_BADGE = 'scope.appBadge';
  window.h5sdk.config({
    appId: appKey,
    timestamp: result.timeStamp,
    nonceStr: result.nonceStr,
    signature: result.signature,
    jsApiList: ['scope.appBadge'],
    // 鉴权成功回调
    onSuccess: () => {
      window.tt.getSetting({
        success: (res) => {
          // eslint-disable-next-line no-prototype-builtins
          if (!res || !res.authSetting || !res.authSetting.hasOwnProperty(SCOPE_BADGE)) {
            // 若从未对scope.appBadge授权，则请求授权
            window.tt.authorize({
              scope: SCOPE_BADGE,
              success() {
              },
              fail(err) {
                console.error('auth badge fail res=', err);
              },
            });
            return;
          }
          if (!res.authSetting[SCOPE_BADGE]) {
            // 若用户曾经拒绝授权，则通过其他方式引导用户进行授权，例如引导用户点击能出发openSetting调用的按钮等
          }
        },
        fail(res) {
          console.error('get setting fail res=', res);
        },
      });
    },
    // 鉴权失败回调
    onFail: (err) => {
      console.error('onFail', JSON.stringify(err));
    },
  });
}
