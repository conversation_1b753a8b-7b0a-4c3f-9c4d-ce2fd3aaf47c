/* eslint-disable jsx-quotes */
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import React, { useRef, useEffect, useState } from 'react';
import { Loading } from '@taroify/core';
import classnames from 'classnames';
import { useIntl } from '@/util/intl';
import YqIcon from '@/components/yq-icon';
import YqTooltip from '@/components/yq-tooltip';
import appStore from '@/store/app';
import { addAction } from '@/util/guance';

import { fetch, _ } from '@/util';
import './index.scss';
import notifySound from '../../util/notifySound';

function YqAudio({
  audios,
  setAudios,
  voiceRes,
  index,
  disabled = false, // 是否禁用转文字和删除
  onChangeText = _.nullFunc, // 转文字回调
  onChangeAudio = _.nullFunc, // 删除回调
}) {
  const intl = useIntl();
  const tooltipRef = useRef(null);
  const audioRef = useRef(null);
  const propAudioRef = useRef(audios);
  const isMove = useRef(false);
  const [translateText, _setTranslateText] = React.useState('');
  const [loading, setLoading] = React.useState(false);
  const [dynWidth, setWidth] = useState(30);
  const touchPos = useRef({ x: 0, y: 0 });
  const longTouchTimer = useRef(null);
  useEffect(() => {
    propAudioRef.current = audios;
  }, [audios]);
  const setTranslateText = (text) => {
    _setTranslateText(text);
    if (text.length <= 7) {
      setWidth(50);
    } else {
      setWidth(90);
    }
  };

  const setAudioRef = (ref) => {
    audioRef.current = ref;
    propAudioRef.current[index].play = true;
    setAudios([...propAudioRef.current]);
    ref.play();
  };
  // 点击预览的语音播放
  const handleAudioPlay = async (e) => {
    longTouchTimer.current && clearTimeout(longTouchTimer.current);
    if (isMove.current) return;
    if (voiceRes.url.endsWith('undefined')) {
      Taro.atMessage({
        type: 'error',
        message: intl.formatMessage({ id: 'yqc.mobile.unable.to.play', defaultMessage: '该段语音暂时无法播放' }),
      });
      return;
    }
    if (!audioRef.current) {
      // eslint-disable-next-line no-chinese/no-chinese
      addAction('播放语音');
      if (process.env.TARO_ENV === 'weapp') {
        await notifySound(`${voiceRes.url}&snapshot=false`, true, setAudioRef);
      } else {
        await notifySound(voiceRes.url, true, setAudioRef);
      }
      audioRef.current = null;
      handleAudioPlayEnd();
    } else if (audioRef.current.paused) {
      // eslint-disable-next-line no-chinese/no-chinese
      addAction('语音继续播放');
      propAudioRef.current[index].play = true;
      // audioRef.current.paused = false;
      setAudios([...propAudioRef.current]);
      audioRef.current.play();
    } else if (!audioRef.current.paused) {
      // audioRef.current.paused = true;
      // eslint-disable-next-line no-chinese/no-chinese
      addAction('语音暂停播放');
      propAudioRef.current[index].play = false;
      setAudios([...propAudioRef.current]);
      audioRef.current.pause();
    }
    e.stopPropagation();
  };

  function handleAudioPlayEnd() {
    audios[index].play = false;
    setAudios([...audios]);
  }

  function getWidthStyle() {
    // eslint-disable-next-line no-unsafe-optional-chaining
    const n = dynWidth <= 30 ? 1 : Math.ceil((dynWidth - 30) / 5);
    const count = [];
    for (let i = 0; i < n; i++) {
      count.push(i);
    }
    return { count };
  }

  function closeAll() {
    audios.forEach(v => { v.open = false; });
    setAudios(audios);
  }

  async function translate(res) {
    if (!res.fileKey) {
      Taro.atMessage({
        type: 'error',
        message: intl.formatMessage({ id: 'yqc.mobile.not.translation', defaultMessage: '该段语音暂时无法翻译' }),
      });
      return;
    }
    const translateValue = await fetch(
      `intelligent/v1/${appStore.tenantId}/audio/transfer?fileKey=${res.fileKey}`,
      {},
      'POST',
    );
    if (translateValue.code === 'success') {
      return translateValue.text;
    } else {
      return '';
    }
  }

  async function handleTranlate(e) {
    try {
      audios[index].loading = true;
      setAudios(audios);
      setLoading(true);
      const text = await translate(voiceRes);
      audios[index].loading = false;
      audios[index].open = false;
      setAudios(audios);
      // onChangeText(text);
      setTranslateText(text);
      e.stopPropagation();
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error(err);
    } finally {
      setLoading(false);
    }
  }

  function handleDelete() {
    audios.splice(index, 1);
    setAudios(audios);
    onChangeAudio(audios);
    // e.stopPropagation();
  }

  function renderTransferPop() {
    return (
      <React.Fragment>
        <View
          className="trans-pop-item"
          onClick={(e) => handleTranlate(e, voiceRes, index)}
        >
          <YqIcon type="TextRecognition" theme="outline" size={32} fill="#fff" />
          <View>{intl.formatMessage({ id: 'yqc.mobile.translate', defaultMessage: '转文字' })}</View>
        </View>
        <View
          className="trans-pop-item"
          onClick={(e) => handleDelete(e, voiceRes, index)}
        >
          <YqIcon
            type="Delete"
            theme="outline"
            size={32}
            fill="#fff"
          />
          <View>{intl.formatMessage({ id: 'yqc.mobile.delete', defaultMessage: '删除' })}</View>
        </View>
      </React.Fragment>
    );
  }
  function handleTouchStart(e) {
    touchPos.current.x = e.targetTouches?.[0]?.pageX;
    touchPos.current.y = e.targetTouches?.[0]?.pageY;
    e.stopPropagation();
    e.preventDefault();
    longTouchTimer.current = setTimeout(() => {
      tooltipRef.current.handleChildrenClick();
    }, 300);
    isMove.current = false;
  }
  function handleTouchMove(e) {
    if (Math.abs((e?.targetTouches?.[0]?.pageY || 0) - touchPos.current.y) > 30) {
      isMove.current = true;
      longTouchTimer.current && clearTimeout(longTouchTimer.current);
    }
  }
  function renderPlayIcon() {
    return (
      <View
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleAudioPlay}
        onLongPress={e => e.preventDefault()}
        onLongTap={e => e.preventDefault()}
      >
        <YqIcon
          type={!voiceRes.play ? 'Play' : 'PauseOne'}
          theme="filled"
          size={48}
          fill="#2979FF"
          // onClick={handleAudioPlay}
        />
      </View>
    );
  }

  function renderWaveIcon(count) {
    return (
      <View className="record-wave">
        {count.map(v => <YqIcon key={v} type="WavesLeft" theme="outline" size={48} fill="#595959" />)}
      </View>
    );
  }

  const [count, setCount] = useState([0]);
  useEffect(() => {
    const time = (voiceRes.file?.duration || 0) / 1000 / 60;
    setWidth(time < 0.35 ? 35 : time * 100);
    setCount(getWidthStyle(voiceRes).count);
  }, [voiceRes]);

  return (
    <View key={voiceRes.url} style={{ display: 'flex', alignItems: 'center', padding: '5px 0' }}>
      {/* {renderPlayIcon()} */}
      {/* longPress想要阻止冒泡。必须在onLongPress和onTouchStart上同时加上stopPropagation，缺一不可，不知道是不是taro的bug */}
      <View
        className="record-item"
        onTouchEnd={handleAudioPlay}
        onTouchMove={handleTouchMove}
        onTouchStart={handleTouchStart}
        // onClick={handleAudioItemClick}
      >
        <View
          style={{ width: `${dynWidth * 4.2}PX` }}
        >
          <YqTooltip
            ref={tooltipRef}
            trigger={['manual']}
            onTrigger={handleTouchMove}
            title={renderTransferPop()}
            disabled={disabled}
            wrapperClassName='trans-pop'
            backgroundClassName='trans-pop-background'
            clickToClose
            stopPropagation
          >
            <View className={classnames('record-item-voice', { 'has-translate': translateText })} style={{ display: 'flex' }}>
              {renderPlayIcon()}
              <View style={{ display: 'flex', alignItems: 'center', flex: 1, height: '100%' }}>
                {renderWaveIcon(count)}
                <View style={{ marginLeft: '15px', userSelect: 'none' }}>
                  {/* eslint-disable-next-line no-unsafe-optional-chaining */}
                  {Math.ceil(voiceRes.file?.duration / 1000)}&apos;&apos;
                </View>
              </View>
            </View>
          </YqTooltip>
          {translateText && (
            <View className="record-item-translate-text">
              {translateText}
            </View>
          )}

        </View>

      </View>

      {/* 解决iOS无法触发onend及duration问题：&snapshot=false */}
      {/* eslint-disable-next-line jsx-a11y/media-has-caption */}
      {/* <audio src={`${voiceRes.url }&snapshot=false`} /> */}

      {loading && (
        <View className="record-transing">
          <Loading size="24px" />
        </View>
      )}
    </View>
  );
}

export default YqAudio;
