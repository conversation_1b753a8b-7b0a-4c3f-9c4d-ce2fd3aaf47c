/* eslint-disable no-unused-vars */
/* eslint-disable jsx-quotes */
import { useState, useEffect } from 'react';
import { View } from '@tarojs/components';
import { _ } from '@/util';
import Audio from './audio';
import './index.scss';

function YqAudio({
  audios: _audios,
  disabled = false, // 是否禁用转文字和删除
  onChangeText = (v) => {}, // 转文字回调
  onChangeAudio = (v) => {}, // 删除回调
  classNames = '',

}) {
  const [audios, setAudios] = useState([]);

  useEffect(() => {
    const newAudios = [];
    _audios?.map(v => newAudios.push(audios.find(a => a.fileKey === v.fileKey) ? audios.find(a => a.fileKey === v.fileKey) : v));
    setAudios(newAudios);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [_audios]);

  return (
    <View className={`record-list ${classNames || ''}`}>
      {audios?.map((voiceRes, index) => {
        return (
          <Audio
            key={voiceRes.fileKey}
            disabled={disabled}
            index={index}
            voiceRes={voiceRes}
            audios={audios}
            setAudios={setAudios}
            onChangeAudio={onChangeAudio}
            onChangeText={onChangeText}
          />
        );
      })}
    </View>
  );
}

export default YqAudio;
