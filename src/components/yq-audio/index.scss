.new-dynamic,
.yq-quill,
.dynamic-content,
.yq-reply,
.rich-right,
.yq-richtext {
  .record-list {
    padding: 0 24px;
    &.out {
      padding: 0;
      margin-top: 16px;
      img {
        max-width: unset !important;
      }
    }
  }

  .record-item {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    //align-items: center;
    align-items: flex-start;
    min-height: 0;
    padding: 24px 24px;
    //height: 78px;
    background-color: #cce6ff;
    border-radius: 8px;
    position: relative;
    margin-bottom: 16px;
    &-translate-text {
      margin-right: -20px;
      font-size: 32px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: rgba(18,39,77,0.85);
      line-height: 48px;
    }
    &-voice {
      &.has-translate {
        border-bottom: 1px solid #CBD2DC;
        margin-bottom: 16px;
        padding-bottom: 20px;
      }
    }

    .i-icon {
      img {
        width: 40px;
      }
    }
    .record-wave {
      flex-grow: 0;
      //display: flex;
      align-items: center;
      padding: 0 10px;
      user-select: none;
      .i-icon {
        margin-right: -4px;
      }
    }
  }

  .record-transing {
    display: flex;
    align-items: center;
    padding-left: 10px;
  }
}

.trans-pop {
  display: flex;
  align-items: center;
  user-select: none;
  &-item {
    height: 72px;
    text-align: center;
    font-size: 24px;
    .yq-mb-icon {
      width: 100px !important;
    }
  }
  &-background {
    user-select: none;
  }
}
