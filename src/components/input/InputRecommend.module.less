.globalSearch {
  height: 542px;
  background: #fff;
}
.globalSearchExpand {
  transition-duration: 0.5s;
  height: calc(90vh - 182px);
}
.expandRecommend {
  height: 60px;
  border-radius: 0px 0px 16px 16px;
  font-size: 24px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #12274D;
  line-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  background: #F6F7F9;
}
.recommendTips {
  max-height: 0;
  overflow: hidden;
  padding: 0 24px;
  margin: 0 20px;
  margin-top: 0;
  position: relative;
  box-sizing: border-box;
  background: #FFFFFF;
  transition: all 0.25s linear;
  border-radius: 8px;
  border: 1px solid rgba(41,121,255,0);
  &.show {
    max-height: 150px;
    padding: 24px;
    margin: 20px;
    margin-top: 0;
    box-shadow: 0px 3px 14px 2px rgba(0,0,0,0.12);

  }
}
.afterContent {
  position: absolute;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.5);
  border-top: 1px solid #cbd2dc;
}
.tipTitle {
  font-size: 28px;
  color: #12274D;
  display: flex;
  align-items: center;
  line-height: 40px;
}
.firstRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.secondRow {
  margin-top: 12px;
  font-size: 23px;
  color: rgba(18,39,77,0.65);
  line-height: 32px;
}
.watchMore {
  height: 44px;
  background: rgba(41,121,255,0.1);
  border-radius: 22px;
  padding: 0 16px;

  font-size: 24px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #2979FF;
  line-height: 44px;
}
