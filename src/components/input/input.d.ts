import React from 'react';
import { Field } from '@/types/PageJson';

export interface WidgetConfig {
  autoSize?: boolean;
  maxRows?: number;
  minRows?: number;
  rows?: number;
  /**
   * 是否开启智能推荐
   */
  recommendFlag?: boolean;
  /**
   * 是否自动展开智能推荐
   */
  autoExpandRecommendFlag?: boolean;
}

interface Values {

}

export interface InputProps {
  onChange?: Function;
  disabled?: boolean;
  ocrFlag?: boolean;
  /**
   * 加在输入框后面的元素
   */
  addonAfter?: React.ReactElement;
  handleOcr?: Function;
  field: Field<'TextField'>;
  values: Values;
  required?: boolean;
  value: string;
  type?: 'number' | 'text' | 'idcard' | 'digit' | 'safe-password' | 'nickname' | 'password' | 'Url' | 'captcha';
  placeholder?: string;
  title?: string;
  cardMode?: boolean;
  align?: 'left' | 'right';
  error?: string;
  inputType?: string;
  variableFlag?: boolean;
  historyInputFlag?: boolean;
  historyInputComponentId?: string;
  useForAi?: boolean;
}

export interface RetProps {
  children?: React.ReactElement;
  brief?: React.ReactElement;
  hiddenRight?: boolean;
}
