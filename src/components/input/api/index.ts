import useSWR from 'swr';
import { fetch, _ } from '@/util';
import appStore from '@/store/app';

/**
 * 智能推荐
 * @param {string} value
 */

export function useBestSolution(value: string) {
  const url = `bss/v1/${appStore.tenantId}/openSearch?draftFlag=false&page=0&size=10`;

  const fetcher = (_url: string) => {
    const [curUrl, queryText] = _url.split('#');
    const body = { channel: 'GLOBAL_SEARCH', queryText, requestId: _.uuid(), type: ['all'] };
    return fetch(curUrl, body, 'post');
  };

  const { data, isLoading, error } = useSWR(value && `${url}#${value}`, fetcher, { revalidateOnFocus: false, revalidateOnReconnect: false, dedupingInterval: 60000 });

  const result = data?.content?.[0]?.result || [];

  return { data: result, isLoading, error };
}

export function useSearchChannel(channel: string) {
  const url = `search/v1/${appStore.tenantId}/search-channel/all?channelCode=${channel}`;
  const { data, error, isLoading } = useSWR(url, fetch, { revalidateOnFocus: false, revalidateOnReconnect: false, dedupingInterval: 60000 });
  return {
    data: data?.[0],
    isLoading,
    error,
  };
}
