import Taro from '@tarojs/taro';
import { View } from '@tarojs/components';
import { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import debounce from 'lodash/debounce';

import classnames from 'classnames';
import { useIntl } from '@/util/intl';
import { _ } from '@/util';
import { addAction } from '@/util/guance';
import eventStore from '@/store/service-catalogs';
import GlobalPlace from '@/components/yq-modal/GlobalPlace';
import YqIcon from '@/components/yq-icon';
import appStore from '@/store/app';
import useCommonPage from '@/hooks/use-common-page';
import CResultList from '../../packageOther/pages/search/components/complete-result-list';
import { useBestSolution, useSearchChannel } from './api';
import styles from './InputRecommend.module.less';

const expandRef = { current: false };
function Expander({ children, field, setPopupTop, setClosePopup, popupTop }) {
  const intl = useIntl();
  const childrenMemo = useMemo(() => children, [children]);
  const [expand, setExpand] = useState(expandRef.current);
  useEffect(() => {
    eventStore.showSearchDropdown = true;
    return () => {
      eventStore.showSearchDropdown = false;
    };
  }, []);
  function toggleExpand(e) {
    e.stopPropagation();
    // eslint-disable-next-line no-chinese/no-chinese
    addAction('点击智能推荐的展开或收起更多', {
      autoExpandRecommendFlag: field.widgetConfig.autoExpandRecommendFlag,
      value: e,
    });
    setPopupTop(0);
    setClosePopup(true);
  }

  return (
    <>
      <View
        style={expand ? {
          height: `calc(100vh - ${popupTop + 32 + 60}px)`,
        } : undefined}
        onTouchStart={() => {
          setExpand(true);
          expandRef.current = true;
          if (process.env.TARO_ENV === 'h5') {
            // @ts-ignore
            document.activeElement?.blur?.();
          }
        }}
        className={classnames(styles.globalSearch, expand && styles.globalSearchExpand)}
        onClick={e => e.stopPropagation()}
      >
        {childrenMemo}
      </View>
      <View onClick={toggleExpand} className={styles.expandRecommend}>
        <View>{intl.formatMessage({ id: 'yqc.mobile.comment.pack.up', defaultMessage: '收起' })}{intl.formatMessage({ id: 'yqc.mobile.intelligentrecommendation', defaultMessage: '智能推荐' })}</View>
        <YqIcon type="Up" theme="outline" style={{ marginLeft: _.pxToRem(8) }} size={24} fill="#12274d" />
      </View>
    </>
  );
}

function PopupTop({ popupTop, hiddenPopup, children }) {
  const c = useMemo(() => children, [children]);
  return (
    <View style={{ top: `${popupTop}px` }} onClick={hiddenPopup} className={styles.afterContent}>
      {c}
    </View>
  );
}
const InputRecommend = observer((props: any) => {
  const { popupTop, setPopupTop, setClosePopup, value: propsValue, field, scrollInputToTop } = props;
  const showTipRef = useRef(false);
  const [value, setValue] = useState('');
  const channel = 'SUBMIT_SERVICE_ITEM';
  const setSearchValue = useCallback(debounce((_value) => {
    setValue(_value);
    expandRef.current = false;
  }, field?.widgetConfig?.searchInterval || 300), []);
  useEffect(() => {
    setSearchValue(propsValue);
  }, [propsValue]);
  useEffect(() => {
    return () => {
      eventStore.showSearchDropdown = false;
    };
  }, []);

  const { data: solutionData } = useBestSolution(value);
  const { tenantId } = appStore;

  const supportTenantId = Taro.getCurrentInstance()?.router?.params?.supportTenantId;
  if (supportTenantId) return null;

  const { data: searchSetting } = useSearchChannel(channel);
  const url = useMemo(() => `search/v1/${tenantId}/yq_search?searchChannel=${channel}`, [tenantId]);
  const commonBody = useMemo(() => ({ jumpAddressType: channel, queryText: value, serviceCatalogFlag: true }), [value]);
  const body = useCallback((type: string) => ({ ...commonBody, type: [type] }), [commonBody]);
  function isScopeEnable(scope: string) {
    return searchSetting?.searchScopes?.some(s => s.searchTypeScopeCode === scope);
  }

  const { data: sList, totalElements: sElements, isLoading: sLoading } = useCommonPage(isScopeEnable('SC_CATALOG') ? url : '', value && body('SC_CATALOG'), 'post');
  const { data: kList, totalElements: kElements, isLoading: kLoading } = useCommonPage(isScopeEnable('KNOWLEDGE_CENTER') ? url : '', value && body('KNOWLEDGE_CENTER'), 'post');
  const { data: tList, totalElements: tElements, isLoading: tLoading } = useCommonPage(isScopeEnable('TICKETS') ? url : '', value && body('TICKETS'), 'post');

  const intl = useIntl();

  useEffect(() => {
    if (!value) {
      setTimeout(() => {
        hiddenPopup();
      }, 400);
    }
  }, [value]);

  function hiddenPopup() {
    // recommendFlag
    // autoExpandRecommendFlag
    // eslint-disable-next-line no-chinese/no-chinese
    addAction('点击灰色区域收起智能推荐', {
      autoExpandRecommendFlag: field.widgetConfig.autoExpandRecommendFlag,
    });
    setPopupTop(0);
    eventStore.showSearchDropdown = false;
  }

  if (!field?.widgetConfig?.recommendFlag) {
    return null;
  }

  const renderName = () => {
    if (solutionData[0]?.title) {
      return intl.formatMessage(({ id: 'yqc.mobile.intelligentRecommendation', defaultMessage: '智能推荐' }));
    } else if (sList[0]?.title) {
      return intl.formatMessage(({ id: 'yqc.mobile.service', defaultMessage: '服务' }));
    } else if (kList[0]?.title) {
      return intl.formatMessage(({ id: 'yqc.mobile.knowledge', defaultMessage: '知识' }));
    } else if (tList[0]?.title) {
      return intl.formatMessage(({ id: 'yqc.mobile.ticket', defaultMessage: '单据' }));
    } else {
      return intl.formatMessage(({ id: 'yqc.mobile.intelligentRecommendation', defaultMessage: '智能推荐' }));
    }
  };

  function renderRecommendTips() {
    if (!showTipRef.current && solutionData.length + sList?.length + kList?.length + tList?.length > 0) {
      showTipRef.current = true;
    }
    return (
      <View
        onClick={() => {
          // eslint-disable-next-line no-chinese/no-chinese
          addAction('点击打开智能推荐', {
            autoExpandRecommendFlag: field.widgetConfig.autoExpandRecommendFlag,
          });
          scrollInputToTop(true);
        }}
        className={classnames(styles.recommendTips, value && (solutionData.length + sList?.length + kList?.length + tList?.length > 0 || showTipRef.current) && styles.show)}
      >
        <View className={styles.firstRow}>
          <View className={styles.tipTitle}>
            <YqIcon style={{ marginRight: _.pxToRem(12), borderRadius: _.pxToRem(8) }} size={32} type="icon-bestbest-solution" />
            <View>{intl.formatMessage(({ id: 'yqc.mobile.similar.problem', defaultMessage: '有相似问题的解决方案' }))}</View>
          </View>
          <View className={styles.watchMore}>
            {intl.formatMessage(({ id: 'yqc.mobile.view.more', defaultMessage: '查看更多' }))}
          </View>
        </View>
        <View className={styles.secondRow}>
          {renderName()}
          ：
          {(solutionData[0]?.title || sList[0]?.title || kList[0]?.titleNormal || tList[0]?.title || '').replace(/<[^>]+>/g, '')}
        </View>
      </View>
    );
  }
  const cList = useMemo((() => value && <CResultList channel="SUBMIT_SERVICE_ITEM" isCreateOrderRecommend value={value} setTab={() => {}} />), [value]);
  return popupTop > 0 ? (
    <GlobalPlace>
      <PopupTop popupTop={popupTop} hiddenPopup={hiddenPopup}>
        <Expander popupTop={popupTop} setPopupTop={setPopupTop} field={field} setClosePopup={setClosePopup}>{cList}</Expander>
      </PopupTop>
    </GlobalPlace>
  ) : renderRecommendTips();
});

export default InputRecommend;
