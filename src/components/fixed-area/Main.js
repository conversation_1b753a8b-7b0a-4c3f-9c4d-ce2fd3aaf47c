import { Component, useEffect } from 'react';
import Taro from '@tarojs/taro';
import { observer, inject } from 'mobx-react';
import { toJS, observable } from 'mobx';
import { constants } from '@/util';
import appStore from '@/store/app';
import { Input } from '@/components/index';
import MasterDetail from '@/components/master-detail';
import './index.scss';

/**
 * TODO：组件需要使用prop-types定义
 */

const store = observable({
  person: {
    id: undefined,
    realName: undefined,
  },
});

function FixedArea(props) {
  props = {
    bgColor: '#2979ff',
    color: '#fff',
    loading: false,
    ...props,
  };

  const onRequestForChange = (item) => {
    store.person = {
      id: item?.id,
      realName: item?.realName,
    };
    props?.onChange(item, 'requestFor');
    props?.onChange(item.id, 'requestFor:id');// 高航改
    props?.onChange(item.realName, 'requestFor:realName');// 高航改
  };

  const renderGoods = () => {
    const { hiddenDeliveryTimeFlag, hiddenPriceFlag, hiddenQtyFlag, applicantShowFlag } = props.formFieldHidden;
    const showRequestFlag = !(hiddenDeliveryTimeFlag && hiddenPriceFlag && hiddenQtyFlag) || applicantShowFlag;
    !showRequestFlag && props.handleHiddenRequestFor();
    return (
      <>
        {hiddenDeliveryTimeFlag !== true && (
          <Input
            intl={props.intl}
            title={props.intl.formatMessage({ id: 'yqc.mobile.estimated.delivery.time', defaultMessage: '预计交付时间' })}
            value={props.fixedSectionData.deliveryDate || '--'}
            name="creationDate"
            disabled
            output={false}
          />
        )}
        {hiddenPriceFlag !== true && (
          <Input
            intl={props.intl}
            title={props.intl.formatMessage({ id: 'yqc.mobile.price', defaultMessage: '单价' })}
            value={props.fixedSectionData.price / (props.fixedSectionData.quantity || toJS(props.fixedSectionData).usageCount || 1)}
            name="price"
            type="number"
            disabled
            output={false}
          />
        )}
        {hiddenPriceFlag !== true && (
          <Input
            intl={props.intl}
            title={props.intl.formatMessage({ id: 'yqc.mobile.event.all.price', defaultMessage: '总价' })}
            value={props.fixedSectionData.price}
            name="price"
            type="number"
            disabled
            output={false}
          />
        )}
        {hiddenQtyFlag !== true && (
          <Input
            intl={props.intl}
            title={props.intl.formatMessage({ id: 'yqc.mobile.quantity', defaultMessage: '数量' })}
            value={props.fixedSectionData.quantity || toJS(props.fixedSectionData).usageCount || 1}
            name="quantity"
            type="number"
          />
        )}
        {showRequestFlag && renderRequestor()}
      </>
    );
  };
  const renderRequestor = () => {
    const { applicantShowFlag } = props.fixedSectionData || {};
    return (
      <MasterDetail
        required
        disabled={!applicantShowFlag}
        lovCode="USER"
        title={props.intl.formatMessage({ id: 'yqc.mobile.who.request.for', defaultMessage: '申请人' })}
        onChange={onRequestForChange}
        value={store.person}
      />
    );
  };

  const renderService = () => {
    const { hiddenDeliveryTimeFlag, applicantShowFlag } = props.formFieldHidden;
    const showRequestFlag = !hiddenDeliveryTimeFlag || applicantShowFlag;
    !showRequestFlag && props.handleHiddenRequestFor();
    return (
      <>
        {hiddenDeliveryTimeFlag !== true && <Input
          intl={props.intl}
          title={props.intl.formatMessage({ id: 'yqc.mobile.estimated.delivery.time', defaultMessage: '预计交付时间' })}
          value={toJS(props.fixedSectionData).deliveryDate || '--'}
          name="creationDate"
          editable={false}
          output={false}
        />}
        {showRequestFlag && renderRequestor()}
      </>
    );
  };

  const renderRecord = () => {
    const { applicantShowFlag } = props.fixedSectionData;
    !applicantShowFlag && props.handleHiddenRequestFor();
    return applicantShowFlag && renderRequestor();
  };
  const isDraft = Taro?.getCurrentInstance()?.router?.params.draftId;
  useEffect(() => {
    if (!isDraft) {
      // 如果是新建的项目
      store.person.id = appStore.self.id;
      store.person.realName = appStore.self.realName;
    }
  }, []);

  useEffect(() => {
    if (isDraft) {
      // 如果是已有的草稿
      store.person.id = props?.formFieldValues['requestFor:id'] ? props?.formFieldValues['requestFor:id'] : '';
      store.person.realName = props?.formFieldValues['requestFor:realName'] ? props?.formFieldValues['requestFor:realName'] : '';
    }
  }, [props.formFieldValues]);

  if (!props.fixedSectionData) return null;
  if (props.fixedSectionData.type === constants.FixedFormType.GOODS) {
    return renderGoods();
  }

  if (props.fixedSectionData.type === constants.FixedFormType.SERVICE) {
    return renderService();
  }

  if (props.fixedSectionData.type === constants.FixedFormType.RECORD) {
    return renderRecord();
  }

  return null;
}

export default observer(FixedArea);
