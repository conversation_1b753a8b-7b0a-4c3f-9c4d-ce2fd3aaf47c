/* eslint-disable react-hooks/exhaustive-deps */
import Taro from '@tarojs/taro';
import { View } from '@tarojs/components';
import { useEffect } from 'react';
import { observer, useLocalObservable } from 'mobx-react-lite';
import { Empty } from '@taroify/core';
import { fetch, _ } from '@/util';
import YqIcon from '@/components/yq-icon';
import YqFile from '@/components/yq-file';
import YqAvatar from '@/components/yq-avatar';
import Loading from '@/components/loading';
import YqComponentTitleFrame from '@/components/yq-component-title-frame';
import appStore from '@/store/app';
import DOMPurify from '../../util/dompurify';
import './index.scss';

const { sanitize } = DOMPurify;

function ApprovalHistoryDynamic({ intl, field, values }) {
  const store = useLocalObservable(() => ({
    loading: false,
    records: [],
    async fetch() {
      const instanceId = values ? values['wf_instance_id:id'] : '';
      if (!instanceId) {
        Taro.atMessage({
          type: 'warning',
          message: intl.formatMessage({ id: 'yqc.mobile.configure.instance.id', defaultMessage: '审批历史需配置流程实例id' }),
        });
        return;
      }
      const url = `itsm/v1/${appStore.tenantId}/approval_history/all?cacheTS=0&instanceId=${instanceId}`;
      store.loading = true;
      const res = await fetch(url).catch(() => {});
      if (!res || res.failed) {
        Taro.atMessage({
          type: 'error',
          message: !res ? intl.formatMessage({ id: 'yqc.mobile.api.wrong', defaultMessage: '接口出现报错，请联系管理员' }) : res.message,
        });
        store.loading = false;
        return;
      }
      store.records = res;
      store.loading = false;
    },
  }));

  useEffect(() => {
    appStore.fromDesigner !== 'true' && store.fetch();
  }, []);

  const renderActionIcon = (actionType) => {
    let icon = '';
    switch (actionType) {
      case 'TODO':
        icon = <YqIcon fill="#2979FF" theme="filled" type="ArrowCircleRight" size={32} />;
        break;
      case 'PASS':
        icon = <YqIcon fill="#7BC95A" theme="filled" type="CheckOne" size={32} />;
        break;
      case 'REJECT':
        icon = <YqIcon fill="#FF4949" theme="filled" type="CloseOne" size={32} />;
        break;
      case 'TRANSFER':
        icon = <YqIcon fill="#FF9100" theme="filled" type="UpdateRotation" size={32} />;
        break;
      default:
        break;
    }
    return icon;
  };

  const renderApprovalItem = (record) => {
    const { type, journalDTO, action, approvalNode, actionName, transferTargetName, approvalRemark } = record || {};
    if (type === 'REPLY') {
      const { attachments } = journalDTO || {};
      const journalHtml = _.transRichTextToHtml(journalDTO.content);
      return (
        <View className="approval-history-body-content-reply">
          <View dangerouslySetInnerHTML={{ __html: sanitize(journalHtml) }} />
          <YqFile attachments={attachments} />
        </View>
      );
    } else if (type === 'APPROVAL') {
      const prefixCls = 'approval-history-dynamic';
      return (
        <View className={`${prefixCls}`}>
          <View className={`${prefixCls}-top`}>
            <View className={`${prefixCls}-top-icon`}>
              {renderActionIcon(action)}
            </View>
            <View className={`${prefixCls}-top-description`}>
              {approvalNode}{actionName ?? `：${actionName}`}
            </View>
          </View>
          <View className={`${prefixCls}-bottom`}>
            {transferTargetName && (
              <View className={`${prefixCls}-bottom-transfer`}>
                {intl.formatMessage({ id: 'yqc.mobile.approval.transfer.to', defaultMessage: '转交至: ' })}{transferTargetName}
              </View>
            )}
            {approvalRemark && (
              <View className={`${prefixCls}-bottom-remark`}>
                {action === 'TRANSFER' ? intl.formatMessage({ id: 'yqc.mobile.remark', defaultMessage: '备注' }) : intl.formatMessage({ id: 'yqc.mobile.approval.opinion', defaultMessage: '审批意见' })}
                {`: ${approvalRemark}`}
              </View>
            )}
          </View>
        </View>
      );
    } else {
      return null;
    }
  };

  const renderApprovalContent = (record) => {
    const {
      updatePersonName,
      updatePersonId,
      updatedAt,
      actionName,
      operationTargetName,
      imageUrl,
    } = record;
    const title = `${updatePersonName || '-'} ${actionName} ${operationTargetName || ''}`;
    return (
      <View key={record.id} style={{ position: 'relative' }}>
        <View className="approval-history-avatar">
          <YqAvatar personId={updatePersonId} fileKey={imageUrl}>{updatePersonName}</YqAvatar>
        </View>
        <View className="approval-history-border" />
        <View className="approval-history-body">
          <View className="approval-history-body-header">
            <View>{title}</View>
            <View style={{ fontSize: '14px', color: '#8C8C8C' }}>{updatedAt}</View>
          </View>
          <View className="approval-history-body-content">
            {renderApprovalItem(record)}
          </View>
        </View>
      </View>
    );
  };

  return (
    <Loading loading={store.loading}>
      <YqComponentTitleFrame header={field.name || intl.formatMessage({ id: 'yqc.mobile.approval.history', defaultMessage: '审批历史' })} />
      {store.records?.length > 0 ? (
        <View className="approval-history">
          {store.records.map(renderApprovalContent)}
        </View>
      ) : (
        <Empty>
          <Empty.Description>
            {intl.formatMessage({ id: 'yqc.mobile.no.approval.history', defaultMessage: '暂无审批历史' })}
          </Empty.Description>
        </Empty>
      )}
    </Loading>
  );
}

export default observer(ApprovalHistoryDynamic);
