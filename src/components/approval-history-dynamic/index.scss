.approval-history {
  width: 100%;
  height: 100%;
  overflow-y: hidden;
  background-color: #fff;
  &-content {
    width: 100%;
    min-height: 100px;
    position: relative;
  }
  &-body {
    width: calc(100% - 150px);
    height: 100%;
    margin-left: 110px;
    margin-bottom: 40px;
    //background-color: red;
    &-header {
      width: 100%;
      min-height: 41px;
      padding-top: 9px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 32px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #262626;
      line-height: 32px;
      word-break: break-all;
    }
    &-content {
      width: 100%;
      height: calc(100% - 41px);
      &-reply {
        width: 100%;
        margin-top: 30px;
        margin-bottom: 20px;
        color: #333;
        .yq-image {
          width: 100%;
        }
        ol {
          margin-left: 32px;
        }
        ul {
          margin-left: 32px;
        }
      }
    }
  }
  &-avatar {
    position: absolute;
    left: 30px;
    top: 0;
    z-index: 1
  }
  &-border {
    width: 1px;
    height: calc(100% - 20px);
    position: absolute;
    left: 58px;
    top: 50px;
    border: 1px dashed #e8e8e8;
  }
  &-title {
    height: 45px;
    margin: 10px 24px 16px 24px;
    font-size: 32px;
    font-family: PingFangSC-Medium, PingFang SC;
    color: #262626;
  }
}
.approval-history-dynamic {
  width: 100%;
  border-radius: 8px;
  border: 1px solid rgba(0,0,0,0.06);
  padding: 16px 32px;
  margin-top: 30px;
  margin-bottom: 20px;
  font-size: 24px;
  color: #595959;
  &-top {
    display: flex;
    align-items: center;
    &-icon {
      width: 40px;
      margin-right: 16px;
      display: flex;
      align-items: center;
    }
    &-description {
      height: 32px;
    }
  }
  &-bottom {
    padding-left: 56px;
    &-transfer {
      margin-top: 8px;
    }
    &-remark {
      margin-top: 8px;
    }
  }
}
