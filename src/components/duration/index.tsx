import React, { useCallback, useEffect, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Text, View } from '@tarojs/components';
import { Input } from '@taroify/core';
import dayjs from 'dayjs';
import duration, { DurationUnitType } from 'dayjs/plugin/duration';
import { toJS } from 'mobx';
import { useIntl } from '@/util/intl';
import _ from '@/util/util';
import MbRow from '@/components/mb-row';
import { DurationProps, DurationInputProps } from './Duration';
import styles from './Duration.module.less';

dayjs.extend(duration);

const durationUnitList: DurationUnitType[] = ['years', 'months', 'days', 'hours', 'minutes', 'seconds'];

function DurationInput(props: DurationInputProps): React.ReactElement {
  const { type, handleChange = () => { }, analysisValue, intl, disabled = false } = props;
  const UNITMAP = {
    days: intl.formatMessage({ id: 'yqc.mobile.model.day', defaultMessage: '天' }),
    hours: intl.formatMessage({ id: 'yqc.mobile.model.hour', defaultMessage: '时' }),
    minutes: intl.formatMessage({ id: 'yqc.mobile.model.minute', defaultMessage: '分' }),
    seconds: intl.formatMessage({ id: 'yqc.mobile.model.second', defaultMessage: '秒' }),
  };

  const handleFoucs = useCallback((e) => {
    if (analysisValue?.toString?.() === '0') {
      setTimeout(() => {
        e.target?.children?.[0]?.select();
      }, 100);
    }
  }, [analysisValue]);

  return (
    <View className={styles.durationInputWrapper}>
      <Input
        disabled={disabled}
        className={styles.durationInput}
        value={analysisValue}
        onBlur={(e) => handleChange(e.detail.value, type)}
        onFocus={handleFoucs}
        type="number"
      />
      <Text className={styles.durationText}>{UNITMAP[type]}</Text>
    </View>
  );
}

function Duration(props: DurationProps): React.ReactElement {
  const { title, disabled, required, value, onChange = () => { }, field, values, cardMode = false } = props;

  const intl = useIntl();

  const { durationUnit = 'seconds', durationMode = [] } = field?.widgetConfig || {};

  const sortDurationMode = useMemo(() => durationMode.slice().sort((a, b) => (a > b ? 1 : -1)), [durationMode]);

  // useEffect(() => {
  //   if (typeof value !== 'number') {
  //     const changeValue = dayjs.duration(field?.duration || {}).as(durationUnit);
  //     onChange(changeValue);
  //   }
  // }, [value, field]);

  const preUnitList = useMemo(() => {
    const index = durationUnitList.findIndex(v => v === sortDurationMode[0]);
    return index === -1 ? [] : durationUnitList.slice(0, index);
  }, [sortDurationMode]);

  const formatterValue = useMemo(() => {
    const result = _.convertDurationToMods(Number(value), durationUnit, toJS(sortDurationMode));
    // if (typeof value !== 'number') {
    //   Object.assign(result, field?.duration || {});
    // }
    return dayjs.duration(result);
  }, [value, durationUnit, sortDurationMode, preUnitList]);

  const handleChange = useCallback((modeValue, mode) => {
    const filterDurationMode = durationMode.filter(m => m !== mode);
    const result = { [mode]: modeValue };
    filterDurationMode.forEach(m => {
      Object.assign(result, { [m]: formatterValue.get(m) });
    });
    const changeValue = dayjs.duration(result).as(durationUnit);
    onChange(changeValue);
  }, [formatterValue, durationUnit, onChange, durationMode]);

  const renderDurationItem = useCallback((mode) => (
    <DurationInput
      type={mode}
      handleChange={handleChange}
      intl={intl}
      analysisValue={formatterValue.get(mode)}
      disabled={disabled}
    />
  ), [formatterValue, disabled, intl, handleChange]);

  return (
    <MbRow cardMode={cardMode} disabled={disabled} required={required} field={field} values={values} label={title}>
      <View className={styles.duration}>
        {sortDurationMode.length > 0 && sortDurationMode.map(renderDurationItem)}
      </View>
    </MbRow>
  );
}

export default observer(Duration);
