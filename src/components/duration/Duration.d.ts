/* eslint-disable no-unused-vars */
import { IntlShape } from '@/util/intl';

type Mode = 'days' | 'hours' | 'minutes' | 'seconds';

interface WidgetConfig {
  durationMode: Array<Mode>; // 显示模式 ["days", "hours", "minutes", "seconds"]
  durationUnit: Mode; // 数据单位 后台数据的存储单位
}

interface Field {
  id: string;
  name: string;
  code: string;
  placeHolder: string;
  widgetConfig: WidgetConfig;
  widgetType: string;
}

export interface DurationInputProps {
  type: Mode;
  handleChange: (value: any, type: Mode) => void;
  intl: IntlShape;
  analysisValue: any;
  disabled?: boolean;
}

export interface DurationProps {
  title: string;
  required?: boolean;
  disabled?: boolean;
  value: string | number;
  placeHolder?: string;
  onChange: (value: any) => void;
  field: Field;
  cardMode?: boolean;
  values: any;
}
