.card {
  background-color: #fff;
  margin-bottom: 12px;


  &.cardWrapper {
    margin: 24px !important;
    border: 1px solid rgba(203, 210, 220, 0.5);
    border-radius: 8px;
    overflow: hidden;
  }

  .title {
    min-height: 92px;
    line-height: 92px;
    padding: 0 26px;
    font-size: 32px;
    font-weight: 500;
    font-family: PingFangSC-Medium, PingFang SC;
    border-bottom: 1px solid rgba(203, 210, 220, 0.5);
    display: flex;
    //justify-content: s;
    align-items: center;
    position: relative;
    &.prefix {
      &::before {
        content: "";
        display: inline-block;
        width: 0.15rem;
        height: 0.8rem;
        margin-top: 6px;
        margin-right: 12px;
        background-color: #2979ff;
        vertical-align: sub;
        border-radius: 12px;
      }
    }
    .rightFoldArea {
      position: absolute;
      right: 0;
      //background-color: red;
      width: 100px;
      flex-shrink: 0;
      display: flex;
      height: 100%;
      align-items: center;
      justify-content: center;
    }
    &.cardHeader {
      background: rgba(98, 198, 255, 0.12);
      padding-bottom: 10px;
    }
  }

  .content {
    :global {
      .page-loader-wrapper {
        overflow: hidden;
      }
      .taroify-list__placeholder {
        margin-bottom: 0 !important;
      }
    }
    &.cardContent {
      padding: 20px 0;
    }
    transition: 0.2s all;
  }
}

.popupWrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  :global {
    .page-loader-wrapper {
      height: auto;
    }
  }
}

.groupBtn {
  padding: 22px 24px;
  border-top: 1px solid rgba(18, 39, 77, 0.1);
  display: flex;
  align-items: center;
  flex-shrink: 0;
  z-index: 1012;

  .groupBtnItem {
    margin: 0 16px;
  }
}

.firstRow {
  padding-top: 20px;
  padding-bottom: 11px;
  line-height: 45px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.secondRow {
  font-size: 26px;
  font-weight: 400;
  display: flex;
  justify-content: space-between;
  font-family: PingFangSC-Regular, PingFang SC;

  .label {
    color: rgba(18, 39, 77, 0.65);
    display: inline-block;
  }

  .value {
    color: #12274D;
    display: inline-block;
  }

  .total {
    display: inline-flex;
    //justify-content: center;
    align-items: baseline;

    .label {
      color: #12274D;
      font-size: 28px;
      line-height: 44px;
      display: inline-block;
    }

    .value {
      color: #2979ff;
      font-size: 32px;

      font-weight: 500;
      font-family: PingFangSC-Medium, PingFang SC;
      line-height: 44px;
      display: inline-block;
    }
  }
}


.ai-title {
  display: inline-flex;
  cursor: pointer;
  font-size: 24px;
  color: #fff;
  font-family: DingTalk-JinBuTi,DingTalk;
  font-weight: normal;
  height: 48px;
  background: linear-gradient(135deg, #53FFC6, #439CFF, #BB4BFF);
  border-radius: 24px;
  padding: 0 16px 0 4px;
  vertical-align: middle;
  align-items: center;
  margin-left: 24px;
  &.loading {
    background-size: 200% 200%;
    animation: flowColors 1s infinite;
  }

  .ai-icon {
    width: 40px;
    height: 40px;
    margin: 4px 0;
  }

  .ai-name {
    margin-left: 8px;
  }
}

@keyframes flowColors {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
