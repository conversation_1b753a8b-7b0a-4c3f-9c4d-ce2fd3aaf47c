import Taro from '@tarojs/taro';
import { ScrollView, View } from '@tarojs/components';
import React, { useEffect, useRef, useState } from 'react';
import { observer, useLocalObservable } from 'mobx-react';
import classNames from 'classnames';
import { useIntl } from '@/util/intl';
import YqIcon from '@/components/yq-icon';
import Modal from '@/components/yq-modal';
import { _, fetch } from '@/util';
import appStore from '@/store/app';
import { PopupHeader } from '@/components/yq-component-popup';
import styles from './Card.module.less';
import PageLoader from '../page-loader';
import AiImg from './AiIcon';

interface CardProps {
  pageStore?
  showHeader?: boolean;
  className?: string;
  style?: React.CSSProperties;
  titleStyle?: React.CSSProperties;
  contentStyle?: React.CSSProperties;
  children: any;
  title: any;
  headerPrefix?: boolean;
  cardFlag?: boolean;
  formFieldValues?: any;
  section?: any;
  price?: number;
  quantity?: number;
}

const CardHeader = observer((props) => {
  const { jsonData, formFieldValues, title, modal, handleConfirm } = props;

  const intl = useIntl();

  const handleOk = () => {
    handleConfirm?.();
    modal?.close();
  };

  return (
    <View className={styles.popupWrapper}>
      <PopupHeader
        title={`${intl.formatMessage({ id: 'yqc.mobile.edit', defaultMessage: '编辑' })}${title}`}
        handleOk={handleOk}
        handleCancel={() => modal?.close()}
      />
      <ScrollView scrollY style={{ flex: 1, height: 0 }}>
        <PageLoader isStatic formFieldValues={formFieldValues} jsonData={jsonData} />
      </ScrollView>
    </View>
  );
});

const Card: React.FC<CardProps> = function Card({
  showHeader = true,
  pageStore,
  headerPrefix = false,
  className = '',
  style = {},
  titleStyle = {},
  contentStyle = {},
  children,
  title,
  price,
  quantity,
  cardFlag = false,
  formFieldValues = {},
  section = {},
}) {
  const intl = useIntl();
  const [aiLoading, setAiLoading] = useState(false);
  const calHeightRef = useRef<HTMLDivElement>();

  const store = useLocalObservable(() => ({
    jsonData: {},
    formFieldValues: {},
  }));

  const childrenList = React.Children.map(children, (child) => {
    if (cardFlag) {
      const cloneChild = child ? React.cloneElement(child, { ...child.props, disabled: true, cardMode: true }) : null;
      return cloneChild;
    } else {
      return child || null;
    }
  });

  // section.hiddenFlag: true: 不可折叠，false: 可折叠
  // section.activeFlag: true: 默认不折叠, false: 默认折叠
  const [foldState, setFoldState] = useState(section.hiddenFlag === false && section.activeFlag === false && !cardFlag);

  const handleOpen = () => {
    const _section = { ...section };
    _section.mode = 'REDUCED';

    store.jsonData = { sections: section?.length > 0 ? section : [_section] };
    store.formFieldValues = { ...formFieldValues };

    Modal.open({
      popupProps: { style: { height: '80vh', zIndex: 999 } },
      backdropProps: { style: { zIndex: 998 } },
      children: (
        <CardHeader
          title={_section?.name || title}
          jsonData={store.jsonData}
          formFieldValues={store.formFieldValues}
          handleConfirm={() => {
            Object.assign(formFieldValues, { ...store.formFieldValues });
          }}
        />
      ),
    });
  };

  async function handleAiPrompt() {
    try {
      setAiLoading(true);

      const res = await fetch(`ai/v1/${appStore.tenantId}/callPromptTemplate/run/${section.promptTemplateId}?businessObjectCode=${pageStore.businessObjectCode}&ticketId=${formFieldValues.id}`, undefined, 'POST');
      Object.assign(formFieldValues, { ...res.changedParams });
    } catch (e) {
      Taro.atMessage({ message: e.message, type: 'error' });
    } finally {
      setAiLoading(false);
    }
  }

  useEffect(() => {
    if (calHeightRef.current && section.hiddenFlag === false && section?.mode !== 'REDUCED') {
      if (foldState) { // 折叠
        calHeightRef.current.style.height = '0px';
        calHeightRef.current.style.opacity = '0.1';
      } else if (foldState === false) {
        setTimeout(() => {
          if (calHeightRef && calHeightRef.current && calHeightRef.current.scrollHeight > 0) {
            calHeightRef.current.style.height = `${calHeightRef.current.scrollHeight}px`;
            calHeightRef.current.style.opacity = '1';
          }
        }, 100);
      }
    }
  }, [foldState, childrenList]);

  const renderHeader = () => {
    const titleCls = classNames(styles.title, 'taroify-ellipsis', { [styles.prefix]: headerPrefix });
    if (showHeader && !cardFlag) {
      return <View className={titleCls} style={titleStyle}>
        <View>{title}</View>
        {section?.aiPromptFlag && <div
          className={classNames(styles['ai-title'], { [styles.loading]: aiLoading })}
          onClick={() => handleAiPrompt()}
        >
          <AiImg />
          <span className={styles['ai-name']}>{section.aiPromptTitle}</span>
        </div>}
        {section?.hiddenFlag === false && <div
          className={styles.rightFoldArea}
          onClick={() => {
            setFoldState(!foldState);
          }}
        >
          <YqIcon type={foldState ? 'down' : 'up'} size={36} fill="#12274d" opacity={0.65} />
        </div>}
      </View>;
    }
    if (cardFlag) {
      return (
        <View className={classNames(titleCls, { [styles.cardHeader]: cardFlag })} style={titleStyle}>
          <View className={styles.firstRow}>
            {title}
            <YqIcon onClick={handleOpen} type="write" size={36} fill="#12274d" opacity={0.65} />
          </View>
          {(price || quantity) && <View className={styles.secondRow}>
            <View>
              {price !== undefined && <View className={styles.label}>{intl.formatMessage({ id: 'yqc.mobile.price', defaultMessage: '单价' })}： </View>}
              {price !== undefined && <View style={{ marginRight: _.pxToRem(32) }} className={styles.value}>{price}</View>}
              {quantity !== undefined && <View className={styles.label}>{intl.formatMessage({ id: 'yqc.mobile.quantity', defaultMessage: '数量' })}：</View>}
              {quantity !== undefined && <View style={{ marginRight: _.pxToRem(32) }} className={styles.value}>{quantity}</View>}
            </View>
            {(price || quantity) && <View className={styles.total}>
              <View className={styles.label}>{intl.formatMessage({ id: 'yqc.mobile.total', defaultMessage: '合计' })}： </View>
              <View className={styles.value}>{(price || 0) * (quantity || 0)}</View>
            </View>}
          </View>}
        </View>
      );
    }
    return null;
  };

  return (
    <View className={classNames([styles.card, className], { [styles.cardWrapper]: cardFlag })} style={style}>
      {renderHeader()}
      {childrenList?.length ? (
        <View
          className={classNames(styles.content, { [styles.cardContent]: cardFlag })}
          style={!section.hiddenFlag && !cardFlag ? { ...contentStyle, overflow: 'hidden' } : { ...contentStyle }}
          ref={calHeightRef}
        >
          {childrenList}
        </View>
      ) : null}
    </View>
  );
};

export default observer(Card);
