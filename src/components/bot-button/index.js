import { useState, useEffect, useRef } from 'react';
import { View, Image } from '@tarojs/components';
import { Badge } from '@taroify/core';
import { observer } from 'mobx-react-lite';
import { injectIntl } from '@/util/intl';
import { ICON_SERVER } from '@config';
import appStore from '@/store/app';
import { addAction } from '@/util/guance';
import { _ } from '@/util';
import './index.scss';

const BotWrapper = observer(() => {
  const [pos, setPos] = useState({
    right: 24,
    bottom: 93,
  });
  const [imgLoading, setImageLoading] = useState(true);
  const posRef = useRef(pos);

  return (
    <View
      catchMove
      style={{
      // display: imgLoading ? 'none' : 'flex',
      // 图片加载失败也显示一张失败的图片
        display: 'flex',
        right: `${pos.right}px`,
        bottom: `calc(${pos.bottom}px + constant(safe-area-inset-bottom))`,
        bottom: `calc(${pos.bottom}px + env(safe-area-inset-bottom))`,
        zIndex: appStore.botZindex || 1000,
        backgroundColor: appStore.botImage ? undefined : '#2979ff',
      }}
      className="bot-button"
    >
      <Badge content={appStore.botUnreadCount || ''} max={99} />
      <BotButton setImageLoading={setImageLoading} setPos={setPos} posRef={posRef} />
    </View>

  );
});

const BotButton = injectIntl(observer(({ posRef, setPos, setImageLoading }) => {
  // 底部tabbar的高度
  const [tabbarHeight, setTabbarHeight] = useState(0);
  const [botHeight, setBotHeight] = useState(0);
  // 存取屏幕的宽高
  const [screenHeight, setsSreenHeight] = useState(0);
  const [screenWidth, setScreenWidth] = useState(0);

  const disRef = useRef({
    disY: 0, // 鼠标按下时光标的Y值
    disX: 0, // 鼠标按下时光标的Y值
    totalMove: 0, // 总移动距离小于10认为是点击
  });

  useEffect(() => {
    const tabH = document?.getElementsByClassName('new-home-bar')[0]?.clientHeight || 0;
    // 获取客服图标宽高
    const botH = document?.getElementsByClassName('bot-button')[0]?.clientHeight;
    // 获取屏幕宽高
    const screenH = process.env.TARO_ENV === 'weapp' ? wx.getSystemInfoSync().windowHeight : document.documentElement.clientHeight;
    const screenW = process.env.TARO_ENV === 'weapp' ? wx.getSystemInfoSync().windowWidth : document.documentElement.clientWidth;
    setTabbarHeight(tabH);
    setBotHeight(botH);
    setsSreenHeight(screenH);
    setScreenWidth(screenW);
  }, []);

  function handleTouchMove(e) {
    e.stopPropagation();
    e.preventDefault();
    let bottom = -(e.touches[0].clientY - disRef.current.disY) + posRef.current.bottom;
    let right = -(e.touches[0].clientX - disRef.current.disX) + posRef.current.right;
    // 防止左侧和顶部溢出屏幕
    if (right > screenWidth - botHeight) {
      right = screenWidth - botHeight;
    }
    if (bottom > screenHeight - botHeight) {
      bottom = screenHeight - botHeight;
    }
    const newPos = {
      bottom: bottom > tabbarHeight ? bottom : tabbarHeight,
      right: right > 0 ? right : 0,
    };
    setPos(newPos);
    posRef.current = newPos;
    disRef.current = {
      totalMove: disRef.current.totalMove + (e.touches[0].clientY - disRef.current.disY) ** 2 + (e.touches[0].clientX - disRef.current.disX) ** 2,
      disY: e.touches[0].clientY,
      disX: e.touches[0].clientX,
    };
  }

  function handleTouchStart(e) {
    e.stopPropagation();
    e.preventDefault();
    disRef.current = {
      totalMove: 0,
      disY: e.touches[0].clientY,
      disX: e.touches[0].clientX,
    };
  }
  function handleTouchEnd() {
    if (disRef.current.totalMove < 10) {
      // eslint-disable-next-line no-chinese/no-chinese
      addAction('点悬浮的机器人按钮进的机器人');
      _.navigateTo({
        url: '/pages/botpress/index?botUrl=asdasda',
      });
    }
  }

  return (
    <Image
      draggable={false}
      onLoad={() => {
        setImageLoading(false);
      }}
      onTouchMove={handleTouchMove}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      src={appStore.botImage || `${ICON_SERVER}/static/mobile-robottwo.svg`}
      style={{
        width: appStore.botImage ? '100%' : '50%',
        height: appStore.botImage ? '100%' : '50%',
        borderRadius: '50%',
      }}
    />
  );
}));

export default BotWrapper;
