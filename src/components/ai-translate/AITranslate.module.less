.frame {
  width: 100%;
  .frameLine {
    width: 100%;
    .card {
      margin-bottom: 20px;
      background: #F7F9FC;
      border-radius: 8px;
      border: 2px solid #EEF4FC;
      min-height: 60px;
      padding: 16px 18px;
      .title {
        display: flex;
        height: 50px;
        color:  rgba(18, 39, 77, 0.65);
        align-items: center;
        .icon {
          width: 36px;
          height: 36px;
        }
      }
      .content {
        margin-top: 10px;
      }
    }
  }
}
.translatePopupFrame {
  position: absolute;
  width: 100%;
  pointer-events: none;
  height: 100%;
}
.closeBtn {
  pointer-events: auto;
  background: rgba(18,39,77,0.45);
  width: 24px;
  height: 24px;
  border-radius: 24px;
  position: absolute;
  bottom: 240px;
  left: 10%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.translatePopup, .translatePopupMask {
  width: min(760px, 80vw);
  background: linear-gradient( 135deg, rgba(83, 255, 198, 0.08) 0%, rgba(67, 156, 255, 0.08) 52%, rgba(187, 75, 255, 0.08) 100%);
  box-shadow: 0px 6px 30px 0px rgba(18,39,77,0.12);
  border-radius: 8px;
  height: 110px;
  position: absolute;
  bottom: 170px;
  display: flex;
  z-index: 501;
  left: 50%;
  transform: translateX(-45%);
  justify-content: space-between;
  padding: 10px 30px;
  align-items: center;
  .text {
    width: 460px;
    display: flex;
    margin-top: 16px;
    justify-content: center;
    align-items: center;
  }
  .translateALlBtn {
    pointer-events: auto;
    width: 154px;
    height: 62px;
    border-radius: 48px;
    box-shadow: 0px 6px 30px 0px rgba(18,39,77,0.12);
    background: linear-gradient( 135deg, #53FFC6 0%, #439CFF 49%, #BB4BFF 100%);
    color: white;
    font-size: 28px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.translatePopupMask {
  background-color: white;
  box-shadow: none;
  z-index: 500;
}

.translateLoadingFrame {
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: flex;
  align-items: center;
  flex-direction: column;
  padding: 180px 60px;
  color: #12274D;
  .loadingImg {
    width: 150px;
    height: 150px;
  }
  .imgTitle {
    margin-top: 48px;
    font-size: 40px;
  }
  .imgDescription {
    margin-top: 30px;
    font-size: 32px;
    opacity: 0.65;
    text-align: center;
  }
  .cancel {
    margin-top: 64px;
    flex: unset !important;
    width: 180px;
    height: 64px;
    flex-grow: 0;
    flex-shrink: 0;
  }
}
