import { observer } from 'mobx-react-lite';
import { View } from '@tarojs/components';
import { useEffect, useState, useContext } from 'react';
import { Picker } from '@taroify/core';
import { ICON_SERVER } from '@config';
import { MobXProviderContext } from 'mobx-react';
import { YqIcon } from '@/components/index';
import { useIntl } from '@/util/intl';
import Modal from '@/components/yq-modal';
import appStore from '@/store/app';
import GlobalPlace from '@/components/yq-modal/GlobalPlace';
import YqButton from '@/components/yq-button';
import styles from './AITranslate.module.less';

interface AiTranslateProps {
  code?: string; // 适用于全文翻译中的pageLoader标识某字段
  from?: 'PAGE_LOADER' | 'REPLY';
  recordId?: string; // 适用于全文翻译中的回复，标识某条回复
  singleItemTranslate?: string; // 适用于单条回复翻译
  style?: any;
}

interface TranslateAllRepliesProps {
  businessObjectId: string;
  ticketId: string;
  records: any; // 目的是取每个fieldValue字段，组成一个模拟的加载数据
}

// 字段翻译和回复翻译出现的框
const AiTranslate = observer((props: AiTranslateProps) => {
  const { code, from = 'PAGE_LOADER', recordId, singleItemTranslate, style } = props;
  const intl = useIntl();
  const { translateStore } = useContext(MobXProviderContext);
  const [translatedText, setText] = useState<string | undefined>();
  if (!translateStore) return null;
  useEffect(() => {
    const res = translateStore?.getTicketTranslateRes();
    if (res) { // 全文翻译时
      if (from === 'PAGE_LOADER' && code) {
        setText(res?.[code]);
      } else if (from === 'REPLY' && recordId) {
        setText(res?.journals?.[recordId]);
      }
    }
    if (singleItemTranslate) { // 单条翻译时
      setText(singleItemTranslate);
    }
  }, [code, from, translateStore?.ticketTranslateRes, singleItemTranslate]);
  if (!translatedText) return null;
  if (window.location.href.includes('udmTenantId')) return null; // 屏蔽上下游
  return (
    <View className={styles.frame} style={{ padding: from === 'REPLY' ? '0' : '0 18px', ...style }}>
      <View className={styles.frameLine}>
        <View className={styles.card}>
          <View className={styles.title}>
            <View className={styles.icon}>
              <img src={`${ICON_SERVER}/static/icon_translation.png`} alt="" />
            </View>
            <View style={{ marginLeft: '8px' }}>{intl.formatMessage({ id: 'yqc.mobile.translate.aitranslate', defaultMessage: 'AI翻译' })}</View>
          </View>
          <View className={styles.content}>{translatedText}</View>
        </View>
      </View>
    </View>
  );
});

// 选择语言的弹窗，以hook的形式被调用，点击确认后执行callback
export const useSelectLanguage = (callback: Function, type: 'translate' | 'ticketTranslate') => {
  const intl = useIntl();
  const {
    serviceSetting: { ticketTranslationSettingVO },
  } = appStore;
  const { autoLanguage, languageValueList } = ticketTranslationSettingVO || {};
  const { translateStore } = useContext(MobXProviderContext);
  if (!translateStore) return () => {};
  if (window.location.href.includes('udmTenantId')) return () => {}; // 屏蔽上下游
  function SelectModal({ modal }: any) {
    return (
      <View>
        <Picker
          defaultValue={autoLanguage} // 自动选择默认语言
          onCancel={modal.close}
          onConfirm={([lang]) => {
            if (type === 'translate') translateStore.textLanguage = lang; // 设置段落翻译语言
            if (type === 'ticketTranslate') translateStore.language = lang; // 设置工单翻译语言
            callback(); // 执行回调函数，一般是翻译
            modal.close();
          }}
        >
          <Picker.Toolbar>
            <Picker.Button>{intl.formatMessage({ id: 'yqc.mobile.cancel', defaultMessage: '取消' })}</Picker.Button>
            <Picker.Title>{intl.formatMessage({ id: 'yqc.mobile.translate.selectlanguage', defaultMessage: '选择语言' })}</Picker.Title>
            <Picker.Button>{intl.formatMessage({ id: 'yqc.mobile.confirm', defaultMessage: '确认' })}</Picker.Button>
          </Picker.Toolbar>
          <Picker.Column>{languageValueList?.map((lang) => <Picker.Option value={lang.code}>{lang.value}</Picker.Option>)}</Picker.Column>
        </Picker>
      </View>
    );
  }
  return () => {
    Modal.open({
      popupProps: {
        style: { zIndex: '2024', boxShadow: '0px -10px 20px -10px #C7CEDA' },
      },
      children: <SelectModal />,
    });
  };
};

// 提示“翻译全部回复”的弹窗，以组件的形式被调用
export function TranslateAllReplies(props: TranslateAllRepliesProps) {
  const intl = useIntl();
  const { translateStore } = useContext(MobXProviderContext);
  const { businessObjectId, ticketId, records } = props;
  const handleClick = async () => {
    const mockLoading = {} as any;
    records.forEach((record: any) => {
      mockLoading[record.fieldValue] = '...';
    });
    translateStore.ticketTranslateRes = { journals: mockLoading }; // 这是构造模拟加载的数据，形成...的加载界面
    translateStore.showTranslateAllReplies = false;
    _.setLocalStorage('showTranslateAllReplies', 'false');
    await translateStore.fetchTicketTranslate(businessObjectId, ticketId, true, undefined, 'LAST_ONE');
  };
  useEffect(() => {
    setTimeout(() => {
      translateStore.showTranslateAllReplies = false;
    }, 5000);
  }, []);
  if (!translateStore) return null;
  return (
    <View>
      <GlobalPlace wrapperClassName={styles.translatePopupFrame}>
        <View
          className={styles.closeBtn}
          onClick={() => {
            translateStore.showTranslateAllReplies = false;
            _.setLocalStorage('showTranslateAllReplies', 'false');
          }}
        >
          <YqIcon type="close" size={16} fill="#ffffff" />
        </View>
        <View className={styles.translatePopup}>
          <YqIcon type="icon-yan-copilot-icon" size={58} />
          <View className={styles.text}>
            <img src={`${ICON_SERVER}/static/mobile_img/translate_all_reply.png`} alt="" />
          </View>
          <View className={styles.translateALlBtn} onClick={handleClick}>{intl.formatMessage({ id: 'yqc.mobile.translate.translateallreplies', defaultMessage: '全部翻译' })}</View>
        </View>
        <View className={styles.translatePopupMask} />
      </GlobalPlace>
    </View>
  );
}

// 翻译等待界面
export function TranslateLoading() {
  const intl = useIntl();
  const { translateStore } = useContext(MobXProviderContext);
  if (!translateStore) return null;
  return (
    <View className={styles.translateLoadingFrame}>
      <View className={styles.loadingImg}>
        <img src={`${ICON_SERVER}/static/ai_loading.gif`} alt="" />
      </View>
      <View className={styles.imgTitle}>{intl.formatMessage({ id: 'yqc.mobile.translate.translating2', defaultMessage: '智能翻译中' })}</View>
      <View className={styles.imgDescription}>{intl.formatMessage({ id: 'yqc.mobile.translate.description', defaultMessage: '文本识别需要您稍作等待，AI数字助理正在全力翻译，请不要关闭页面或点击取消' })}</View>
      <YqButton
        className={styles.cancel}
        onClick={() => {
          // 点击取消，结束轮询
          translateStore.translateCancelFlag = true;
          translateStore.setLoadingFlag(false);
        }}
        fontColor="#595959"
        name={intl.formatMessage({ id: 'yqc.mobile.translate.cancel', defaultMessage: '取消翻译' })}
        mode="cancel"
      />
    </View>
  );
}

export default AiTranslate;
