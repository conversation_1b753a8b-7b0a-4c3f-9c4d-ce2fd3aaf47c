import React, { useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { useIntl } from '@/util/intl';
import MbRow from '@/components/mb-row';
import YqIcon from '@/components/yq-icon';
import Modal from '@/components/yq-modal';
import { BasicProps } from '@/components/field/ts';
import styles from './Location.module.less';

const LocationSelect: React.FC<BasicProps> = function LocationSelect(props) {
  const {
    field,
    values,
  } = props;
  const intl = useIntl();
  let realValue = {} as any;
  try {
    realValue = JSON.parse(values?.[field?.widgetConfig?.locationFieldCode || '']);
  } catch (err) {
    console.error('location parse error', err);
  }
  useEffect(() => {
    function listenLocationFrameChanged(evt) {
      if (evt.data.module === 'locationPicker') {
        values[field.widgetConfig.locationFieldCode || ''] = JSON.stringify(evt.data);
      }
    }
    window.addEventListener('message', listenLocationFrameChanged);
    return () => {
      window.removeEventListener('message', listenLocationFrameChanged);
    };
  }, []);
  function handleOpenLocation() {
    const modal = Modal.open({
      title: intl.formatMessage({ id: 'yqc.mobile.select.location', defaultMessage: '选择位置' }),
      popupProps: { style: { height: '90vh' } },
      onOk: () => {
        modal.close();
      },
      onCancel: () => {
        modal.close();
      },
      children: (
        <iframe
          title="location"
          allow="geolocation"
          id="mapPage"
          width="100%"
          height="100%"
          frameBorder="0"
          scrolling="no"
          className={styles.frame}
          src={field.widgetConfig.locationUrl}
        />
      ),
    });
  }
  return (
    <MbRow
      clearButton
      label={intl.formatMessage({ id: 'yqc.mobile.location', defaultMessage: '位置' })}
      field={field}
      values={values}
      onClick={handleOpenLocation}
      value={realValue?.poiaddress}
      handleClear={() => {
        values[field.widgetConfig.locationFieldCode || ''] = JSON.stringify({});
      }}
      placeholder={intl.formatMessage({ id: 'yqc.mobile.please.select.location', defaultMessage: '请选择位置' })}
      rightIcon={<YqIcon type="icon-seemore" fill="#12274d" opacity={0.45} size={28} />}
    />
  );
};

export default observer(LocationSelect);
