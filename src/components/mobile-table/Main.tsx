import { observer } from 'mobx-react-lite';
import ListPreviewer from '@/components/list-previewer';

function MobileTable(props) {
  const { pageStore, values, pcFlag, ticketId, variableFlag } = props;

  return (
    <ListPreviewer
      pcFlag={pcFlag}
      searchable
      clickLoader
      innerList
      formValues={values}
      tableFieldRecord={props.field}
      store={pageStore}
      ticketId={ticketId}// 单据id
      variableFlag={variableFlag} // 服务请求变量视图标识
    />
  );
}

export default observer(MobileTable);
