/* eslint-disable react/jsx-key */
import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import classnames from 'classnames';
import './nps.less';

const NpsRate = ({ range, type, onChange, value }) => {
  const [rateNum, setRateNum] = useState(null);
  const arr = [...(new Array(10).keys())];
  const arr2 = [...(new Array(6).keys())];
  if (type === 'Number') {
    return <div className="nps-rate">
      {arr.map((i, index) => {
        return <div className={classnames('nps-rate-item', { 'nps-rate-item-active': value === index + 1 })} onClick={() => { onChange(index + 1); }}>{index + 1}</div>;
      })}
    </div>;
  } else if (type === 'Emojis') {
    return <div className="nps-rate">
      {arr2.map((i, index) => {
        return <div className={classnames('nps-rate-emoji', `nps-rate-emoji-active${value}`, { 'nps-rate-emoji-active': value >= index })} onClick={() => { onChange(index); }} />;
      })}
    </div>;
  }
  return null;
};

export default observer(NpsRate);
