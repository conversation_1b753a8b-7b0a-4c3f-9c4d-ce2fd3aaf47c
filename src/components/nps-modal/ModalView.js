import React, { useState, useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { Textarea, Input, Toast } from '@taroify/core';
import classnames from 'classnames';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { ICON_SERVER } from '@config';
import { _, fetch } from '@/util';
import { useIntl } from '@/util/intl';
import YqButton from '@/components/yq-button';
import Button from '../yq-button';
import Icon from '../yq-icon';
import NpsRate from './nps-rate/NpsRate.js';
import './index.less';

function ModalView({ tenantId, modal, type, instanceId: Iid }) {
  const [questionData, setQuestionData] = useState([]);
  const [surveyQuestionData, setSurveyQuestionData] = useState([]);
  const [npsScore, setNpsScore] = useState(null);
  const [functionScore, setFunctionScore] = useState({});
  const [npsModal, setNpsModal] = useState(null);
  const [surveyModal, setSurveyModal] = useState(null);
  const [npsRecommendation, setNpsRecommendation] = useState({});
  const [npsRecommendationCode, setNpsRecommendationCode] = useState({});
  const [npsText, setNpsText] = useState({});
  const [finish, setFinish] = useState(false);
  const [invite, setInvite] = useState(false);
  const ref = useRef();
  const [instanceId, setInstanceId] = useState(Iid);
  const [nextInstanceId, setNextInstanceId] = useState(null);

  async function queryNpsInfo() {
    const res = await fetch('asmt/v1/nps/questions?type=NPS');
    setQuestionData(res);
  }

  async function querySurveyInfo() {
    const res = await fetch('asmt/v1/nps/questions?type=SATISFACTION_SURVEY');
    setSurveyQuestionData(res);
  }

  const intl = useIntl();

  useEffect(() => {
    if (type === 'SATISFACTION_SURVEY') {
      querySurveyInfo();
    } else {
      queryNpsInfo();
    }
  }, []);

  function changeNpsText(v, code) {
    const obj = npsText;
    obj[code] = v;
    setNpsText(obj);
  }

  function changeEmojiScore(v, code) {
    const arr = { ...functionScore };
    arr[code] = v;
    setFunctionScore(arr);
  }

  function changeScore(value) {
    setNpsScore(value);
  }

  function handleInvite() {
    handleClose();
    _.navigateTo({ url: `/packageOther/pages/nps-survey/index?instanceId=${instanceId}` });
  }

  function rendererChildren(i, valueRef) {
    const widgetConfig = JSON.parse(i.widgetConfig);
    const questionType = widgetConfig.type;
    const questionRange = widgetConfig.range;
    switch (i.widgetType) {
      case 'Score':
        return (
          <div className="survey-content">
            {i.name && <div className="survey-content-title">{i.name}</div>}
            {questionType === 'Emojis'
              ? <NpsRate range={questionRange} type={questionType} onChange={(v) => changeEmojiScore(v, i.code)} value={functionScore[i.code]} />
              : <NpsRate range={questionRange} type={questionType} onChange={changeScore} value={npsScore} />}
          </div>
        );
      case 'ScoreCard':
        if (!npsRecommendationCode?.[valueRef]?.includes(i.code)) return null;
        return (
          <div className="survey-content">
            {i.name && <div className="survey-content-title">{i.name}</div>}
            <NpsRate range={questionRange} type="Emojis" onChange={(v) => changeEmojiScore(v, i.code)} value={functionScore[i.code]} />
          </div>
        );
      case 'Text':
        return (
          <div className="survey-content">
            {i.name && <div className="survey-content-title">{i.name}</div>}
            <Textarea placeholder={intl.formatMessage({ id: 'yqc.mobile.inputyoursuggestion', defaultMessage: '请输入您的建议' })} cols={300} onChange={(v) => changeNpsText(v.target.value, i.code)} value={npsText[i.code]} />
          </div>
        );
      case 'Multiple':
        return (
          <div className="survey-content">
            {i.name && <div className="survey-content-title">{i.name}</div>}
            <div className="survey-content-list">
              {i.children.map((r) => {
                return <div className={classnames('survey-content-card', { 'survey-content-card-active': (npsRecommendation[i.code] || []).includes(r.id) })} onClick={() => changeTips(r, i.code)}>{r.name}</div>;
              })}
            </div>
          </div>
        );
      default:
        return (
          <div className="survey-content">
            <div className="survey-content-title">{i.name}</div>
          </div>
        );
    }
  }

  function rendererSurveyQuestion(i) {
    const widgetConfig = JSON.parse(i.widgetConfig);
    const valueRef = widgetConfig?.valueRef;
    return (
      <div className="survey-content">
        <div className="survey-content-title-top"><span className="survey-content-title-icon" />{i.name}</div>
        {i.children && i.children.map(r => rendererChildren(r, valueRef))}
      </div>
    );
  }

  function handleSubmit(flag) {
    if (!!npsRecommendationCode?.NPS_RECOMMEND_OPTIMIZE_DIRECTION?.includes('NPS_OTHER') && !npsText.NPS_LEAVE_A_MESSAGE && npsScore <= 7) {
      if (ref.current) {
        ref.current?.focus();
        ref.current?.blur();
      }
      return false;
    } else {
      const data = questionData.map(i => {
        let value;
        switch (i.widgetType) {
          case 'Score':
            value = npsScore;
            break;
          case 'Text':
            value = npsText[i.code];
            break;
          case 'Multiple':
            value = JSON.stringify(npsRecommendation[i.code]);
            break;
          default:
            break;
        }
        return { instanceId, questionId: i.id, questionValue: value };
      });
      const res = fetch(`asmt/v1/${tenantId}/nps/apply?instanceId=${instanceId}`, data, 'POST');
      setFinish(true);
    }
  }

  function changeTips(i, code) {
    const obj = npsRecommendation;
    const codeObj = npsRecommendationCode;
    const arr = obj[code] ? [...obj[code]] : [];
    const codeArr = codeObj[code] ? [...codeObj[code]] : [];
    let arr1 = [];
    let arr2 = [];
    const id = i.id;
    const icode = i.code;
    if (arr.includes(id)) {
      arr1 = arr.filter(r => r !== id);
      arr2 = codeArr.filter(r => r !== icode);
      obj[code] = arr1;
      codeObj[code] = arr2;
      setNpsRecommendation({ ...obj });
      setNpsRecommendationCode({ ...codeObj });
    } else {
      arr.push(id);
      codeArr.push(icode);
      obj[code] = arr;
      codeObj[code] = codeArr;
      setNpsRecommendation({ ...obj });
      setNpsRecommendationCode({ ...codeObj });
    }
  }

  function handleClose() {
    modal.close();
    setQuestionData([]);
    setSurveyQuestionData([]);
    setNpsScore(null);
    setFunctionScore({});
    setNpsModal(null);
    setSurveyModal(null);
    setNpsRecommendation({});
    setNpsRecommendationCode({});
    setNpsText({});
    setFinish(false);
    setInvite(false);
    setInstanceId(null);
    setNextInstanceId(null);
  }

  function handleSubmitSurvey() {
    const dataArr = surveyQuestionData.map(v => v.children).flat();
    const data = dataArr.map(i => {
      let value;
      switch (i.widgetType) {
        case 'Score':
          value = functionScore[i.code] ? functionScore[i.code] + 1 : functionScore[i.code];
          break;
        case 'ScoreCard':
          value = functionScore[i.code] ? functionScore[i.code] + 1 : functionScore[i.code];
          break;
        case 'Text':
          value = npsText[i.code];
          break;
        case 'Multiple':
          value = JSON.stringify(npsRecommendation[i.code]);
          break;
        default:
          break;
      }
      return { instanceId, questionId: i.id, questionValue: value };
    });
    if (data.every(i => !i.questionValue)) {
      Taro.atMessage({ type: 'error', message: intl.formatMessage({ id: 'yqc.mobile.message.question', defaultMessage: '请填写问卷' }) });
      return;
    }
    const res = fetch(`asmt/v1/${tenantId}/nps/apply?instanceId=${instanceId}`, data, 'POST');
    setFinish(true);
  }

  function rendererQuestion(i) {
    const widgetConfig = JSON.parse(i.widgetConfig);
    const questionType = widgetConfig.type;
    const questionRange = widgetConfig.range;
    switch (i.widgetType) {
      case 'Score':
        return (
          <div className="survey-content">
            {i.name && <div className="survey-content-title">{i.name}</div>}
            <NpsRate range={questionRange} type={questionType} onChange={changeScore} value={npsScore} />
          </div>
        );
      case 'Text':
        if (!npsScore || npsScore > 7) return null;
        return (
          <div className="survey-content">
            {i.name && <div className="survey-content-title">{!!npsRecommendationCode?.NPS_RECOMMEND_OPTIMIZE_DIRECTION?.includes('NPS_OTHER') && <span className="survey-content-required">*</span>}{i.name}</div>}
            <Textarea ref={ref} placeholder={intl.formatMessage({ id: 'yqc.mobile.inputyoursuggestion', defaultMessage: '请输入您的建议' })} cols={300} required={!!npsRecommendationCode?.NPS_RECOMMEND_OPTIMIZE_DIRECTION?.includes('NPS_OTHER')} onChange={(v) => changeNpsText(v.target.value, i.code)} value={npsText[i.code]} />
          </div>
        );
      case 'Multiple':
        if (!npsScore || npsScore > 7) return null;
        return (
          <div className="survey-content">
            {i.name && <div className="survey-content-title">{i.name}</div>}
            <div className="survey-content-list">
              {i.children.map((r) => {
                return <div className={classnames('survey-content-card', { 'survey-content-card-active': (npsRecommendation[i.code] || []).includes(r.id) })} onClick={() => changeTips(r, i.code)}>{r.name}</div>;
              })}
            </div>
          </div>
        );
      default:
        return (
          <div className="survey-content">
            <div className="survey-content-title">{i.name}</div>
          </div>
        );
    }
  }

  if (finish) {
    return (
      <div className="nps-survey-wrapper">
        <Icon className="nps-survey-wrapper-close" type="close" theme="filled" fill="#fff" size={72} onClick={handleClose} />
        <div className="nps-survey-wrapper-offsetY">
          <div className="nps-survey-wrapper-title">燕千云诚邀您成为产品体验官</div>
          <img src={`${ICON_SERVER}/static/mobile_img/xinzhi-bg-3.png`} alt="" className="nps-survey-wrapper-headerImg" />
        </div>
        <div className="nps-survey-wrapper-content">
          <div className="nps-survey-wrapper-content-form">
            <div className="survey-form survey-form-feedback">
              <Icon theme="filled" fill="#1AB335" type="check-one" size={140} />
              <div className="survey-form-feedback-title">提交成功</div>
              <div className="survey-form-feedback-text">尊敬的产品体验官，您的反馈是我们前进的动力, 感谢您的参与</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (type === 'SATISFACTION_SURVEY' && invite) {
    return (
      <div className="nps-survey-wrapper nps-survey-wrapper-big">
        <Icon className="nps-survey-wrapper-close" type="close" theme="filled" fill="#fff" size={72} onClick={handleClose} />
        <div className="nps-survey-wrapper-content">
          <div className="nps-survey-wrapper-content-form-header">
            产品使用体验问卷
          </div>
          <div className="nps-survey-wrapper-content-form">
            <div className="survey-form">
              {surveyQuestionData?.map(i => rendererSurveyQuestion(i))}
            </div>
            <div className="nps-survey-wrapper-content-footer">
              <Button color="primary" onClick={() => handleSubmitSurvey()}>提交</Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (type === 'SATISFACTION_SURVEY') {
    return (
      <div className="nps-survey-wrapper">
        <Icon className="nps-survey-wrapper-close" type="close" fill="#fff" theme="filled" size={72} onClick={handleClose} />
        <div className="nps-survey-wrapper-offsetY">
          <div className="nps-survey-wrapper-title">燕千云诚邀您成为产品体验官</div>
          <img src={`${ICON_SERVER}/static/mobile_img/xinzhi-bg-3.png`} alt="" className="nps-survey-wrapper-headerImg" />
        </div>
        <div className="nps-survey-wrapper-content">
          <div className="nps-survey-wrapper-content-form">
            <div className="survey-form">
              <div className="survey-form survey-form-feedback survey-form-invite">
                <div className="survey-form-feedback-title">邀请函</div>
                <div className="survey-form-feedback-text">为了更好的为您提供服务，诚挚邀请您花费2分钟时间填写本次问卷，我们非常重视每位用户的宝贵意见，感谢您的参与</div>
              </div>
            </div>
            <View className="yq-btn-group nps-survey-wrapper-content-footer">
              <YqButton className="gold-btn-fill h76" onClick={handleInvite}>
                接受邀请
              </YqButton>
              <YqButton className="gold-btn h76" onClick={handleClose} style={{ marginRight: 0 }} loadingColor="#fff">
                暂不参与
              </YqButton>
            </View>
            {/* <div className="nps-survey-wrapper-content-footer"> */}
            {/*  <Button className="nps-survey-wrapper-content-footer-btn" onClick={handleInvite}>接受邀请</Button> */}
            {/*  <Button className="nps-survey-wrapper-content-footer-btn" onClick={handleClose}>暂不考虑</Button> */}
            {/* </div> */}
          </div>
        </div>
      </div>
    );
  }

  if (type === 'NPS') {
    return (
      <div className="nps-survey-wrapper">
        <Icon className="nps-survey-wrapper-close" type="close" fill="#fff" theme="filled" size={72} onClick={handleClose} />
        <div className="nps-survey-wrapper-offsetY">
          <div className="nps-survey-wrapper-title">燕千云诚邀您成为产品体验官</div>
          <img src={`${ICON_SERVER}/static/mobile_img/xinzhi-bg-3.png`} alt="" className="nps-survey-wrapper-headerImg" />
        </div>
        <div className="nps-survey-wrapper-content">
          <div className="nps-survey-wrapper-content-form">
            <div className="survey-form survey-form-nps">
              {questionData?.map(i => rendererQuestion(i))}
            </div>
            <div className="nps-survey-wrapper-content-footer">
              <Button className="nps-survey-wrapper-content-footer-btn" onClick={() => { handleSubmit(); }}>提交</Button>
            </div>
          </div>
        </div>
      </div>
    );
  }
  return null;
}

export default observer(ModalView);
