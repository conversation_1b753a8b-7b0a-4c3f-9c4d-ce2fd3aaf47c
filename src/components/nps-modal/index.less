.nps-survey {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: absolute;
  .mt32 {
    margin-top: 32px;
  }
  @media screen and (max-width: 480px) {
    .taroify-popup {
      left: 30px !important;
    }
  }
  .taroify-popup {
    top: 180px;
    background-color: transparent;
    overflow: visible;
    width: 100%;
  }
  .c7n-pro-modal-content {
    box-shadow: none !important;
    background-color: transparent !important;
    overflow: initial !important;
  }
  .c7n-pro-modal-body {
    max-height: calc(100vh - 240px);
  }
  .nps-survey-wrapper {
    overflow: visible;
    position: relative;
    width: calc(100vw - 60px);
    max-width: 500PX;
    &-big {
      width: 100vw;
      height: 100px;
      min-height: 100px;
      flex: 1;
      overflow: scroll;
      .nps-survey-wrapper-content {
        padding-top: 0;
      }
      .survey-form {
        max-height: unset !important;
        height: 100%;
      }
    }
    &-headerImg {
      width: 100%;
      height: 200px;
      position: absolute;
      top: 150px;
    }
    &-close {
      position: absolute;
      left: 50%;
      bottom: -152px;
      z-index: 10;
      transform: translate(-50%);
      border-radius: 50%;
      border: 3px solid #fff;
      padding: 14px;
    }
    &-offsetY {
      transform: translateY(-50px);
    }
    &-offsetY {
      height: 165px;
    }
    &-title {
      width: 468px;
      height: 64px;
      font-size: 36px;
      font-family: AlimamaShuHeiTi-Bold, AlimamaShuHeiTi;
      font-weight: bold;
      color: #FFFFFF;
      line-height: 64px;
      position: absolute;
      left: 50%;
      top: 174px;
      z-index: 10;
      transform: translate(-50%);
    }
    &-content {
      background-color: #fff;
      padding: 0 15px;
      padding-top: 146px;
      border-radius: 6px;
      &-form {
        padding: 0 25px 16px;
        &-header {
          background: rgba(247, 248, 250, 0.85);
          padding: 32px 0 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 20px;
          font-weight: 500;
          color: #12274D;
          line-height: 28px;
        }
      }
      .survey-form {
        padding: 20px;
        background-color: #fff;
        max-height: 1000px;
        overflow-y: auto;
        &-nps {
            max-height: 480px !important;
        }
        .survey-content {
          background-color: #fff;
          .taroify-textarea {
            background: #f6f7f9;
            padding: 16px;
            border-radius: 8px;
          }
          .taroify-native-textarea {
            height: 184px;
          }
          &-title {
            font-size: 28px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #12274D;
            line-height: 44px;
            margin-bottom: 24px;
            &-top {
              font-size: 32px;
              font-family: PingFangSC-Medium, PingFang SC;
              font-weight: 500;
              color: #12274D;
              line-height: 48px;
              margin-bottom: 24px;
              display: flex;
              align-items: center;
            }
            &-icon {
              width: 8px;
              height: 32px;
              background: #2979FF;
              border-radius: 4px;
              margin-right: 24px;
              display: inline-block;
            }
            .survey-content-required {
              display: inline-block;
              margin-right: 0.04rem;
              color: #d50000;
              font-size: .14rem;
              line-height: 1;
            }
          }
          &-list {
            display: flex;
            margin-bottom: 8px;
            flex-wrap: wrap;
          }
          &-card {
            height: 64px;
            background: #F2F3F5;
            border-radius: 4px;
            font-size: 28px;
            font-weight: 400;
            color: #12274D;
            display: flex;
            padding: 0 24px;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            margin-bottom: 16px;
            margin-right: 16px;
            &-active {
              background: #2979FF;
              color: #fff;
            }
          }
        }
        &-feedback,&-invite {
          padding: 60px;
          padding-top: 0px;
          display: flex;
          flex-direction: column;
          align-items: center;
          &-title {
            font-size: 40px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #12274D;
            line-height: 56px;
          }
          &-text {
            width: 580px;
            margin-top: 24px;
            font-size: 30px;
            text-align: center;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(18,39,77,0.65);
            line-height: 48px;
          }
        }
        &-invite {
          padding: 40px 46px;
          padding-top: 0px;
        }
      }
      &-footer {
        padding: 16px;
        border-radius: 4px;
        border-top: 1px solid rgba(203,210,220,0.5);
        background: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        &-btn {
          color: #fff !important;
          background: linear-gradient(135deg, #DEBE78 0%, #BE9E69 100%) !important;
          border: none;
        }
      }
    }
  }
}
