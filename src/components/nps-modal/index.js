import React from 'react';
import Modal from '@/components/yq-modal';
import { fetch } from '@/util';
import ModalView from './ModalView';

const openNpsSurvey = async ({ tenantId, triggerType }) => {
  const res = await fetch(`asmt/v1/${tenantId}/nps/apply?channel=MOBILE&triggerType=${triggerType}`);
  let instanceId;
  if (res?.[0] && !res.failed) {
    if (res[0].type === 'NPS') {
      instanceId = res[0].instanceId;
      fetch(`asmt/v1/${tenantId}/nps/apply?instanceId=${res[0].instanceId}&triggerType=${triggerType}`, undefined, 'PUT');
    } else if (res[0].type === 'SATISFACTION_SURVEY') {
      instanceId = res[0].instanceId;
      fetch(`asmt/v1/${tenantId}/nps/apply?instanceId=${res[0].instanceId}&triggerType=${triggerType}`, undefined, 'PUT');
    }
    Modal.open({
      children: (
        <ModalView tenantId={tenantId} type={res[0].type} instanceId={instanceId} />
      ),
      popupProps: {
        placement: 'center',

      },
      header: null,
      footer: null,
      bodyStyle: { padding: 0 },
      wrapperClassName: 'nps-survey',
    });
  }
};

export default openNpsSurvey;
