import { useEffect, useRef, useState } from 'react';
import { injectIntl } from '@/util/intl';
import Taro from '@tarojs/taro';
import { observer } from 'mobx-react-lite';
import { View } from '@tarojs/components';
import { addAction } from '@/util/guance';
import IconImageRenderer from '@/components/icon-image-renderer';
import { _ } from '@/util';

import chatStore from '../../packageIntelligent/stores/chat';

import './index.scss';

const shouldHiddenPaths = ['/packageIntelligent/pages/conversation-list/index', '/packageIntelligent/pages/chat-room/index'];

const BotBanner = observer(({ intl }) => {
  const [show, setShow] = useState(false);
  const timer = useRef(null);

  useEffect(() => {
    const [currentPath] = Taro.getCurrentInstance().router.path?.split?.('?') || [];
    if (!shouldHiddenPaths.includes(currentPath)) {
      Taro.eventCenter.once('globalMessageTips', handleShowTips);
      // 重置定时器
      clearTimeout(timer.current);
      chatStore.curPath = '';
      timer.current = setTimeout(() => {
        setShow(false);
        timer.current = null;
      }, 3000);
    }
    return () => {
      Taro.eventCenter.off('globalMessageTips', handleShowTips);
    };
  }, [chatStore.globalBannerDisplayStamp]);

  function handleShowTips() {
    // eslint-disable-next-line no-chinese/no-chinese
    addAction('客服全局提醒');
    const [currentPath] = Taro.getCurrentInstance().router.path?.split?.('?') || [];
    if (!shouldHiddenPaths.includes(currentPath)) {
      setShow(true);
    }
  }

  function handleClick() {
    // eslint-disable-next-line no-chinese/no-chinese
    addAction('点了客服全局提醒');
    _.navigateTo({ url: '/packageIntelligent/pages/conversation-list/index' });
    setShow(false);
  }

  return (
    <View className={`global-banner ${show ? 'display' : ''}`} onClick={handleClick}>
      <View className={`global-banner-inner ${show ? 'display' : ''}`}>
        <View>
          <IconImageRenderer
            type="icon"
            icon="people"
            iconFill="#fff"
            theme="filled"
            iconSize={12}
            wrapperSize={20}
            borderRadius={4}
            backgroundColor="#f96c40"
          />
        </View>
        <View className="global-banner-text">{intl.formatMessage({ id: 'yqc.mobile.conversation.new', defaultMessage: '客服中心有新消息' })}</View>
        <View className="global-banner-info">{chatStore.globalBannerDisplayStamp}</View>
      </View>
    </View>
  );
});

export default injectIntl(BotBanner);
