import React, { useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from '@/util/intl';
import cmpMap from './cmp-mapping/index';

function CustomComponent(props) {
  const { field } = props;
  const component = useMemo(() => cmpMap[field?.widgetConfig?.module], [field?.widgetConfig?.module]);
  if (component) {
    return React.createElement(component, { ...props, defaultExpand: true, itemId: props.values?.id });
  }
  // NOTE: 2024 年 9 月 11 日 【白春强】需求，移动端视图渲染不再在页面显示不支持的组件提醒
  //   只在控制台打印提示
  console.warn(`未知自定义组件-${field?.widgetConfig?.module}`);
  return null;
}

export default observer(injectIntl(CustomComponent));
