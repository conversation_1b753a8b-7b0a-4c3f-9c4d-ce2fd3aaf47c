import get from 'lodash/get';
import { observer } from 'mobx-react-lite';
import { useEffect, useState } from 'react';
import MasterDetail from '@/components/master-detail';
import appStore from '@/store/app';
import { fetch } from '@/util';

function CustomLov(props) {
  let mapFields; let url; let paramFields;
  const [config, setConfig] = useState<any>({});
  try {
    mapFields = JSON.parse(props.field?.widgetConfig?.customConfig?.find((item) => item.key === 'mapFields')?.value);
    url = props.field?.widgetConfig?.customConfig?.find((item) => item.key === 'url')?.value;
    paramFields = JSON.parse(props.field?.widgetConfig?.customConfig?.find((item) => item.key === 'paramFields')?.value);
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error('cbj', e);
  }
  function handleChange(value) {
    props.onChange(value);
    Object.keys(mapFields).forEach(key => {
      props.values[mapFields[key]] = value[key];
    });
    // props.values
  }
  function getLovParams() {
    const lovParams = {};
    if (paramFields) {
      Object.keys(paramFields).forEach(key => {
        lovParams[paramFields[key]] = get(props.values, key?.replace(':', '.'));
      });
    }
    return lovParams;
  }
  useEffect(() => {
    (async () => {
      if (url) {
        const res = await fetch(`${url}/config`, 'get');
        setConfig(res);
      }
    })();
  }, [url]);
  // 'assetPhysicalName,assetPhysicalCode,model,assetCardCode,departName,storageLocation'.split(',')
  return (
    <MasterDetail
      required
      title={props.title}
      value={props.value}
      config={{
        url: `${url}/data`,
        method: 'GET',
        idField: 'assetCardCode',
        nameField: 'assetPhysicalName',
        searchable: true,
        searchField: config?.queryFields?.[0]?.name,
        displayFields: config?.fields?.map(v => ({ ...v, code: v.name })),
        params: getLovParams(),
      }}
      onChange={handleChange}
    />
  );
}

export default observer(CustomLov);
