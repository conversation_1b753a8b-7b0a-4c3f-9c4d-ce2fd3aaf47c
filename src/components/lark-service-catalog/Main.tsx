import Taro from '@tarojs/taro';
import { ScrollView, View } from '@tarojs/components';
import React, { useMemo, useState, useEffect, useRef } from 'react';
import { observer, useLocalObservable } from 'mobx-react-lite';
import { Loading, List } from '@taroify/core';
import classnames from 'classnames';
import { useIntl } from '@/util/intl';
import { _ } from '@/util';
import YqIcon from '@/components/yq-icon';
import IconImageRenderer from '@/components/icon-image-renderer';
import EmptyTips from '@/components/empty-tips';
import YqNavbar from '@/components/yq-navbar';
import YqEmpty from '@/components/yq-empty';
import YqCell from '@/components/yq-cell';
import appStore from '@/store/app';
import larkTalkServiceStore from '@/store/lark-talk-service-catalog';
import useFeishuTalkRecord from './useFeishuTalkRecord';
import Page from '../../global-component/page';
import styles from './ServiceCatalog.module.less';
import { TabbarRecord } from '../../global-component/tabbar/Tabbar';

interface ServiceCatalogProps {
  Navbar?: React.ReactElement;
  disableGlobalComponent?: boolean;
  style?: React.CSSProperties;
}

const ServiceCatalog: React.FC<ServiceCatalogProps> = function ServiceCatalog({
  disableGlobalComponent = false,
  Navbar = null,
  style = {},
}) {
  const intl = useIntl();

  const [feishuTalKRecord] = useFeishuTalkRecord(); // 飞书聊天转工单

  const { pageJson } = appStore;
  const params = useMemo(() => Taro.getCurrentInstance()?.router?.params, []);
  const catalogIds = decodeURIComponent(params?.catalogIds || '').split(',').filter(r => r);
  const choseModulesList = useMemo(() => {
    if (pageJson && pageJson.length > 0) {
      const tabbarRecord: TabbarRecord = pageJson.find((p: any) => p.name === 'tabbar');
      const {
        props: { moduleData },
      } = tabbarRecord;
      if (moduleData && moduleData.length > 0) {
        const showModules = moduleData?.filter?.((module) => {
          return !module.disabled;
        });
        return showModules;
      }
    }

    return [];
  }, [pageJson]);

  const [expand, _setExpand] = useState(true);
  const scrollDistance = useRef(0);
  function setExpand(v) {
    scrollDistance.current = 0;
    _setExpand(v);
  }

  const permissionModule = appStore.permissionModule; // 个人权限的所有模块

  const store = useLocalObservable(() => ({
    data: [] as any[],
    defaultTypeList: ['VOICE', 'OCR', 'CAMERA'],
    getCorrectdata: (allModules: Array<any>, choseModules): void => {
      const data: Array<any> = [];
      if (allModules && choseModules) {
        allModules.forEach(oneModule => {
          choseModules.forEach((oneChoseModule) => {
            if (oneChoseModule.code === oneModule.code || oneChoseModule.moduleId === oneModule.id) {
              data.push(oneModule);
            }
          });
        });
      }
      store.data = [...data];
    },
  }));

  useEffect(() => {
    if (permissionModule?.length) {
      store.getCorrectdata(permissionModule, choseModulesList);
    }
  }, [permissionModule, store, choseModulesList]);

  useEffect(() => {
    if (params?.rang === 'ALL' && feishuTalKRecord) {
      (async () => {
        larkTalkServiceStore.init();
        await queryServiceItem();
      })();
    } else {
      (async () => {
        larkTalkServiceStore.init();
        await queryServiceItem();
      })();
    }
  }, [params, feishuTalKRecord]);

  const queryServiceItem = async (): Promise<void> => {
    larkTalkServiceStore.changeScInit();
    if (params?.rang === 'ALL') {
      larkTalkServiceStore.scLoading = true;
      try {
        await larkTalkServiceStore.fetchScItems(feishuTalKRecord as any);
      } finally {
        larkTalkServiceStore.scLoading = false;
      }
    } else if (params?.rang === 'PART') {
      larkTalkServiceStore.scLoading = true;
      try {
        await larkTalkServiceStore.fetchConfig(params?.appid);
      } finally {
        larkTalkServiceStore.scLoading = false;
      }
    } else {
      // cancel
    }
  };

  // const handleScrollToLower = (): void => {
  //   larkTalkServiceStore.fetchScItems();
  // };

  const handleScItemClick = async (item) => {
    let url = `/pages/create-order/index?noBreadcrumb=true&itemId=${item?.id}`;
    if (appStore.isLark && params?.type === 'feishu_talk') {
      const larkParams = window && window.location && window.location.href ? window.location.href.split('?')[1] : '';
      if (larkParams) {
        url += `&${decodeURIComponent(larkParams)}`;
      }
      if (params?.rang === 'PART') {
        const itemConfig = larkTalkServiceStore.partConfig.find((v) => v.feishuTalkChatTransferTicketServiceItem.id === item.id);
        const promptCode = itemConfig?.feishuTalkChatTransferTicketPrompt?.code;
        const partParams = promptCode ? `promptTemplateCode=${promptCode}&promptFlag=${itemConfig?.feishuTalkChatTransferTicketPromptFlag}` : `promptFlag=${itemConfig?.feishuTalkChatTransferTicketPromptFlag}`;
        url += `&${decodeURIComponent(partParams)}`;
      }
    }

    _.navigateTo({ url });
  };

  const renderEmpty = (loading: boolean): React.ReactElement | null => {
    return loading ? null : <YqEmpty />;
  };

  const renderScItems = (scItem): React.ReactElement => {
    const { icon, shortDescription, id } = scItem;
    const name = scItem.name || scItem.serviceItemDTO?.name;
    const categoryNames = scItem.categoryNames || scItem.categoryName;
    let iconInfo = { type: 'icon', displayIcon: 'icon-module-cataloge', imageUrl: '', backgroundColor: '' };
    const path = categoryNames ? `${categoryNames.split(',')[0]}/${name}` : '';
    try {
      iconInfo = JSON.parse(icon);
    } catch {
      iconInfo = { type: 'icon', displayIcon: 'icon-module-cataloge', imageUrl: '', backgroundColor: '' };
    }
    const iconRender = (
      <IconImageRenderer
        wrapperSize={42}
        borderRadius={8}
        type={iconInfo.type}
        fileKey={iconInfo.imageUrl}
        backgroundColor="#F2F6FA"
        icon={iconInfo.displayIcon || 'icon-module-cataloge'}
        iconFill={iconInfo.backgroundColor}
        theme="filled"
        iconSize={50}
      />
    );
    return (
      <YqCell
        key={id}
        className={styles.serviceListItem}
        icon={iconRender}
        title={(
          <View className={styles.scItemTitleWrapper}>
            <View className={styles.scItemTitle}>{name}</View>
          </View>
        )}
        onClick={() => handleScItemClick(scItem)}
      >
        <React.Fragment>
          {shortDescription && <View className={styles.searchListItemDescription}>{shortDescription}</View>}
          {path && <View className={styles.searchListItemPath}>{path}</View>}
        </React.Fragment>
      </YqCell>
    );
  };

  const gotoAllService = () => {
    let url = '/packageOther/pages/service-catalog/index?showModule=true&hiddenFooter=true';
    if (appStore.isLark && params?.type === 'feishu_talk') {
      const larkParams = window && window.location && window.location.href ? window.location.href.split('?')[1] : '';
      if (larkParams) {
        url += `&${decodeURIComponent(larkParams)}`;
      }
    }
    _.navigateTo({ url });
  };

  return (
    <Page disableGlobalComponent={disableGlobalComponent} className={styles.page} wrapperProps={{ style }}>
      {Navbar || (
        <YqNavbar
          title={params?.title || intl.formatMessage({ id: 'yqc.mobile.prompt.service.catalog.title', defaultMessage: '推荐服务项' })}
        />
      )}
      <View className={styles.promptDesc}>
        {intl.formatMessage({ id: 'yqc.mobile.prompt.service.catalog.suggestion', defaultMessage: '根据您的问题，给您推荐了服务项：' })}
      </View>
      <ScrollView
        scrollY
        className={styles.serviceList}
        // onScrollToLower={handleScrollToLower}
        onScroll={() => {
          scrollDistance.current += 1;
          if (scrollDistance.current > 10) setExpand(false);
        }}
      >
        <List loading={larkTalkServiceStore.scLoading} hasMore={larkTalkServiceStore.hasMore}>
          {larkTalkServiceStore.scItems?.length ? larkTalkServiceStore.scItems.map(renderScItems) : renderEmpty(larkTalkServiceStore.scLoading)}
          <List.Placeholder>
            {larkTalkServiceStore.scLoading && <Loading>{intl.formatMessage({ id: 'yqc.mobile.loading', defaultMessage: '加载中...' })}</Loading>}
          </List.Placeholder>
        </List>
      </ScrollView>
      {params?.rang === 'ALL' ? (
        <View className={classnames(styles.options, styles.expand)}>
          <View style={{ height: expand ? 'unset' : _.pxToRem(64), transition: 'all 0.168s linear', overflow: 'hidden' }}>
            <View className={classnames(styles.row, styles.top)}>
              <YqIcon
                type={`icon-mobile-${expand ? 'down' : 'up'}-one`}
                size={80}
                fill="#d9d9d9"
                onClick={() => setExpand(!expand)}
              />
            </View>
            <View className={styles.content}>
              {intl.formatMessage({ id: 'yqc.mobile.prompt.service.catalog.tips1', defaultMessage: '您也可以点击' })}
              <a style={{ color: '#2979ff', cursor: 'pointer' }} onClick={() => gotoAllService()}>{intl.formatMessage({ id: 'yqc.mobile.prompt.service.catalog.tips2', defaultMessage: '查看全部服务项,' })}</a>
              {intl.formatMessage({ id: 'yqc.mobile.prompt.service.catalog.tips3', defaultMessage: '选择其他的服务项进行提单~' })}
            </View>
          </View>
        </View>
      ) : null}
    </Page>
  );
};

export default observer(ServiceCatalog);
