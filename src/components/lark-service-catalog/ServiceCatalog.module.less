.text-overflow {
  word-break: keep-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.page {
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: PingFangSC-Regular, PingFang SC;
  :global {
    .taroify-loading,
    .taroify-empty {
      width: 100%;
      height: unset;
      flex-grow: 1;
      --loading-color: #2979ff;
    }
    .taroify-list__placeholder {
      height: 100px;
      line-height: 100px;
    }
  }
}
.search {
  height: 88px;
  flex-shrink: 0;
  padding: 10px 24px;
  --search-content-border-radius: 8px;
  --form-item-icon-size: 36px;
  --cell-color: rgba(18, 39, 77, 0.45);
}

.serviceTitle {
  height: 88px;
  padding-left: 24px;
  // 小程序必须加上这两个属性才能滚动
  word-break: keep-all;
  white-space: nowrap;
  background-color: #fff;
  .item {
    max-width: 300px;
    min-width: 50px;
    height: 88px;
    display: inline-block;
    text-align: center;
    flex-shrink: 0;
    line-height: 88px;
    margin-right: 28px;
    position: relative;
    font-size: 30px;
    color: rgba(18, 39, 77, 0.85);
    .text-overflow();
    &.checked {
      font-family: PingFangSC-Medium, PingFang SC;
      color: #2979ff;
    }
  }
  .line {
    width: 100%;
    position: absolute;
    left: 0;
    bottom: 0;
    height: 6px;
    border-radius: 3px;
    background-color: #2979ff;
  }
}

.promptDesc {
  font-size: 0.8rem;
  font-weight: 700;
  padding: 0.6rem 0.75rem;
}

.serviceList {
  height: 0;
  flex-grow: 1;
  padding: 16px 0 60px 0;
  background-color: #fff;
  :global {
    .taroify-empty {
      padding-top: 50%;
    }
  }
  .serviceListItem {
    padding: 24px 30px;
    position: relative;
    .searchListItemDescription {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      font-size: 28px;
      color: rgba(18, 39, 77, 0.65);
      margin-bottom: 16px;
      word-break: break-all;
      white-space: break-spaces;
      line-height: 35px;
    }
    .searchListItemPath {
      max-width: 100%;
      display: inline-block;
      background: rgba(41, 121, 255, 0.08);
      border-radius: 8px;
      font-size: 20px;
      color: #2979ff;
      line-height: 36px;
      padding: 4px 12px;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
  .noMoreText {
    height: 40px;
    font-size: 24px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(18, 39, 77, 0.45);
    line-height: 40px;
  }
}

.popup {
  height: 88px;
  z-index: 100;
  :global {
    --dropdown-menu-title-padding: 0 24px;
    --dropdown-menu-title-font-size: 28px;
    --dropdown-menu-title-color: #12274d;
    --dropdown-menu-title-active-color: #2979ff;
    --gray-4: #12274d;
    .taroify-dropdown-menu__bar {
      height: 88px;
      box-shadow: none;
      align-items: center;
      .taroify-dropdown-menu-title {
        height: 60px;
        flex: unset;
        padding-right: 36px;
        margin-left: 24px;
        background-color: #f6f7f9;
        border-radius: 100px;
        &--active {
          background-color: #e9f1ff;
        }
      }
    }
    .taroify-popup {
      max-height: 900px;
      border-top: 1px solid fade(#cbd2dc, 50%);
      overflow: hidden;
    }
  }
}

.popup-active {
  height: 88px;
  z-index: 100;
  :global {
    --dropdown-menu-title-padding: 0 24px;
    --dropdown-menu-title-font-size: 28px;
    --dropdown-menu-title-color: #2979ff;
    --dropdown-menu-title-active-color: #2979ff;
    .taroify-dropdown-menu-title__content::after{
        border-color: transparent transparent #2979ff #2979ff;
    }
    .taroify-dropdown-menu__bar {
      height: 88px;
      box-shadow: none;
      align-items: center;
      .taroify-dropdown-menu-title {
        height: 60px;
        flex: unset;
        padding-right: 36px;
        margin-left: 24px;
        background-color: #e9f1ff;
        border-radius: 100px;
        &--active {
          background-color: #e9f1ff;
        }
      }
    }
    .taroify-popup {
      max-height: 900px;
      border-top: 1px solid fade(#cbd2dc, 50%);
      overflow: hidden;
    }
  }
}

.popupContent {
  min-height: 550px;
  display: flex;
  word-break: keep-all;
  white-space: nowrap;
  .popupListCol {
    width: 306px;
    // 兼容小程序
    display: inline-flex;
    flex-direction: column;
    max-height: 900px;
    overflow: auto;
    flex-shrink: 0;
    -ms-overflow-style: none;
    scrollbar-width: none;
    &::-webkit-scrollbar {
      display: none;
    }
    &:last-child {
      .popupListColItem.hasBorder,
      .restCol {
        border-right: none;
      }
    }
    &.isFirst {
      background-color: #f6f7f9;
      .popupListColItem {
        &.checked {
          background-color: #fff;
          color: #2979ff;
          position: relative;
          border-top: 1px solid #d8d8d8;
          border-bottom: 1px solid #d8d8d8;
          border-right: none;
          &::before {
            width: 5px;
            height: 32px;
            background-color: #2979ff;
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }
    }
    .restCol {
      height: 0;
      flex-grow: 1;
      border-right: 1px solid #d8d8d8;
    }
    .popupListColItem {
      width: 100%;
      height: 72px;
      display: flex;
      flex-shrink: 0;
      align-items: center;
      line-height: 72px;
      padding: 0 32px;
      color: #12274d;
      :global {
        .taroify-loading {
          width: 32px;
          height: 32px;
          flex-grow: 0;
          margin-left: 16px;
        }
      }
      &.hasBorder {
        border-right: 1px solid #d8d8d8;
      }
      &.checked {
        background: #F6F7F9;
        color: #2979ff;
      }
      .popupListColItemText {
        width: 0;
        flex-grow: 1;
        .text-overflow();
      }
    }
  }
}
.scItemTitleWrapper {
  display: flex;
  align-items: center;
  overflow: visible;
}
.scItemTitle {
  width: 100px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.scItemUpdateDate {
  height: 32px;
  font-size: 24px;
  color: rgba(18,39,77,0.45);
  line-height: 34px;
  margin-left: 16px;
}
.watchDraft {
  //margin-right: -30px;
  position: absolute;
  right: 0;
  width: 128px;
  height: 46px;
  background: rgba(203,210,220,0.32);
  border-radius: 100px 0 0 100px;

  font-size: 24px;
  color: rgba(18,39,77,0.65);
  line-height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.options {
  width: 100%;
  min-height: 120px;
  max-height: 100%;
  flex-shrink: 0;
  background-color: #fff;
  border-top: 1px solid rgba(203, 210, 220, 0.5);
  display: flex;
  flex-direction: column;
  padding-bottom: 10px;

  .row {
    width: 100%;
    display: flex;
    z-index: 1;
    background: #fff;
    justify-content: center;
    align-items: flex-start;
    flex-shrink: 0;

    &.top {
      margin-bottom: 24px;
    }

    :global {

      .i-icon-icon-mobile-down-one,
      .i-icon-icon-mobile-up-one {
        >svg {
          height: 30px;
        }
      }
    }

  }

  .content {
    display: block;
    flex-grow: 1;
    min-height: 3rem;
    font-size: 0.8rem;
    font-weight: 700;
    padding: 0.6rem 0.75rem;
  }
}
