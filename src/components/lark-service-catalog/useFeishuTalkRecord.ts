/* eslint-disable no-console */
import useSWR from 'swr';
import { useCallback, useEffect, useMemo, useState } from 'react';
import dayjs from 'dayjs';
import { _, fetch } from '@/util';
import appStore from '@/store/app';
import useUrlParams from '@/hooks/use-url-params';
import { FeishuTalkQueryProps, FeishuTalkRecord } from '../../packageOther/pages/create-order/CreateOrder';

function useFeishuTalkRecord(): [any[]] | [] {
  const tenantId = appStore.tenantId;

  const { appid, type } = useUrlParams<FeishuTalkQueryProps>();

  const [parseMsg, setParseMsg] = useState<string[]>([]);

  const [record, setRecord] = useState<FeishuTalkRecord | undefined>(undefined);

  const isFeishuTalk = useMemo(() => (type === 'feishu_talk' && appid && process.env.TARO_ENV === 'h5'), [appid, type]);

  const url = useMemo(() => (isFeishuTalk ? 'ecos/v1/lark/signature' : ''), [isFeishuTalk]);

  const fetcher = useCallback((_url: string) => {
    const urlObj = new URL(window?.location.href);
    // 注意：带 query
    const pageUrl = urlObj.origin + urlObj.pathname + urlObj.search;
    const body = {
      appKey: appid,
      url: pageUrl,
      tenantId,
    };
    return fetch(_url, body, 'get');
  }, [appid, tenantId]);

  const { data } = useSWR(url, fetcher, { revalidateIfStale: true, revalidateOnFocus: false });

  useEffect(() => {
    if (isFeishuTalk && data && window.h5sdk) {
      window.h5sdk.config({
        // 下面这 4 个字段，都需要从生成签名的后端接口返回
        appId: appid,
        timestamp: data.timeStamp,
        nonceStr: data.nonceStr,
        signature: data.signature,
        jsApiList: [
          // 声明需要使用的方法名
          'getBlockActionSourceDetail',
        ],
        onSuccess: (res) => console.log(`config: success ${JSON.stringify(res)}`),
      });
      window.h5sdk.error(err => {
        console.error('config error', JSON.stringify(err));
      });
      window.h5sdk.ready(() => {
        // 从页面 URL 中获取 triggerCode
        let launchQuery: any = new URLSearchParams(window?.location.search).get('bdp_launch_query');
        if (!launchQuery) {
          console.log('bdp_launch_query not found in URL');
          return;
        }
        launchQuery = JSON.parse(decodeURIComponent(launchQuery));
        const triggerCode = launchQuery.__trigger_id__;

        // 调用方法，传入 triggerCode，获取消息内容
        window.tt.getBlockActionSourceDetail({
          triggerCode,
          success(res) {
            window.sendLog({ type: 'feishuRecord', res });
            setRecord(res);
          },
          fail(res) {
            console.log('fail', res);
          },
        });
      });
    }
  }, [isFeishuTalk, data]);

  // const parseRecord = useMemo<string[]>(() => {
  //   if (record && record.content?.messages) {
  //     const { content: { messages } } = record;
  //     const parseMsgList = messages?.filter(msg => ['text', 'post', 'image'].includes(msg.messageType)).map(msg => {
  //       const sendTime = dayjs.unix(msg.createTime).format('YYYY-MM-DD HH:mm:ss');
  //       const message = JSON.parse(msg.content);
  //       if (msg.messageType === 'image') {
  //         return `${msg.sender?.name}(${sendTime}): <img src="${message?.url}" />`;
  //       }
  //       return `${msg.sender?.name}(${sendTime}): ${msg.messageType === 'text' ? message.text : message.content}`;
  //     }) || [];
  //     console.log('======feishu_talk: ', parseMsgList);
  //     return parseMsgList;
  //   } else {
  //     return [];
  //   }
  // }, [record]);

  useEffect(() => {
    if (record && record.content?.messages) {
      const { content: { messages } } = record;
      (async () => {
        for (const msg of messages.filter(msg => ['text', 'post', 'image'].includes(msg.messageType))) {
          const sendTime = dayjs.unix(msg.createTime).format('YYYY-MM-DD HH:mm:ss');
          const message = JSON.parse(msg.content);
          if (msg.messageType === 'image') {
            const urlObj = new URL(message?.url);
            const params = new URLSearchParams(urlObj.search);
            const imageKey = params.get('image_key');
            const res = await fetch(`/ecos/v1/${tenantId}/openApps/file/transfer`, {
              appId: appid,
              fileType: 'image',
              fileKey: imageKey,
              downloadUrl: message?.url,
            }, 'POST');
            if (res?.fileUrl) {
              setParseMsg(prev => [
                ...prev,
                `${msg.sender?.name}(${sendTime}): <img src="${res?.fileUrl}" />`,
              ]);
            } else {
              setParseMsg(prev => [
                ...prev,
                `${msg.sender?.name}(${sendTime}): <img src="${message?.url}" />`,
              ]);
            }
          } else {
            setParseMsg(prev => [
              ...prev,
              `${msg.sender?.name}(${sendTime}): ${msg.messageType === 'text' ? message.text : message.content}`,
            ]);
          }
        }
      })();
    }
  }, [record]);

  return [parseMsg];
}

export default useFeishuTalkRecord;
