import { useEffect, useState, useMemo } from 'react';
import { View } from '@tarojs/components';
import { observer, useLocalObservable } from 'mobx-react-lite';
import { injectIntl } from '@/util/intl';
import Taro from '@tarojs/taro';
import dayjs from 'dayjs';
import { WhiteSpace, Skeleton } from '@taroify/core';
import { ICON_SERVER } from '@config';
import appStore from '@/store/app';
import { fetch } from '@/util';
import './index.scss';
// import YqIcon from '../yq-icon';
import MbRow from '../mb-row';

function OcrSelect(props) {
  const { show, intl, field, pageStore, values } = props;
  const [style, setStyle] = useState({});
  const params = useMemo(() => Taro.getCurrentInstance().router.params, []);

  const store = useLocalObservable(() => ({
    setSubmitLoading: (flag) => { store.submitLoading = flag; },
    relationAssets: {},

    async getRelationAssetsByCode(code) {
      const url = `asset/v1/${appStore.tenantId}/itam-asset/getAssetFromCode?assetCode=${code}&assetType=HARDWARE`;
      store.setSubmitLoading(true);
      const result = await fetch(url, null, 'GET').catch(() => {});
      store.setSubmitLoading(false);
      store.relationAssets = result;
      values.assetInfo = result;
      values.assetType = 'HARDWARE';
      values.assetId = result?.id;
    },
  }));

  useEffect(() => {
    store.getRelationAssetsByCode(params.assetCode);
  }, []);

  function renderLoadingStyle() {
    return (
      <>
        <Skeleton variant="circle" />
        <WhiteSpace />
        <Skeleton style={{ width: '40%' }} />
        <WhiteSpace size="20px" />
        <Skeleton />
        <WhiteSpace />
        <Skeleton />
        <WhiteSpace />
        <Skeleton style={{ width: '60%' }} />
        <Skeleton />
        <WhiteSpace />
        <Skeleton />
        <WhiteSpace />
        <Skeleton />
        <WhiteSpace />
      </>
    );
  }

  function renderAssets() {
    if (!store.relationAssets) {
      return (
        <View className="asset-rl-content" style={{ textAlign: 'center', color: '#989898' }}>{intl.formatMessage({ id: 'yqc.mobile.asset.no.data', defaultMessage: '暂无关联资产' })}</View>
      );
    }

    return (
      <>
        <View className="asset-rl-header">
          <img src={`${ICON_SERVER}/static/mobile-package.png`} alt="" />
          <View className="asset-rl-title">{store.relationAssets?.code?.toUpperCase()}&nbsp;&nbsp;&nbsp;&nbsp;{store.relationAssets?.name}</View>
          {/* <YqIcon type='Delete' fill='#2979ff' size='40' /> */}
          <View />
        </View>
        <View className="asset-rl-content">
          <MbRow
            label={intl.formatMessage({ id: 'yqc.mobile.asset.manage.dep', defaultMessage: '管理部门：' })}
            value={store.relationAssets?.mgmtDeptName}
          />
          <MbRow
            label={intl.formatMessage({ id: 'yqc.mobile.asset.manager', defaultMessage: '管理人：' })}
            value={store.relationAssets?.mgmtUserName}
          />
          <MbRow
            label={intl.formatMessage({ id: 'yqc.mobile.asset.type', defaultMessage: '资产类别：' })}
            value={store.relationAssets?.categoryName}
          />
          <MbRow
            label={intl.formatMessage({ id: 'yqc.mobile.asset.product.type', defaultMessage: '产品型号：' })}
            value={store.relationAssets?.modelCode}
          />
          <MbRow
            label={intl.formatMessage({ id: 'yqc.mobile.asset.in.base.time', defaultMessage: '入库日期：' })}
            value={(store.relationAssets?.inStockDate && dayjs(store.relationAssets?.inStockDate).format('YYYY-MM-DD')) || ''}
          />
          <MbRow
            label={intl.formatMessage({ id: 'yqc.mobile.asset.plan.time', defaultMessage: '计划日期：' })}
            value={(store.relationAssets?.dueDate && dayjs(store.relationAssets?.dueDate).format('YYYY-MM-DD')) || ''}
          />
          <MbRow
            label={intl.formatMessage({ id: 'yqc.mobile.asset.intro', defaultMessage: '说明：' })}
            value={store.relationAssets?.description}
          />
        </View>
      </>
    );
  }
  return (
    <View className="asset-rl">
      <View className="asset-rl-box">
        {store.submitLoading ? renderLoadingStyle() : renderAssets()}
      </View>

    </View>
  );
}
export default injectIntl(observer(OcrSelect));
