@import "../../style/setting.scss";
.mb-row-clear-button {
  display: flex;
}
.nocopy {
  user-select: none !important;
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
}
.yq-quill-attachment-list-item {
  .mb-row-cell:last-child::after {
    display: none !important;
  }
}

.asset-rl-content {
  .mb-row-cell::after {
    display: none !important;
  }
}
.rich-right {
  word-break: break-all;
}
.mb-row-cell {
  .mb-row-cell {
    min-height: unset;
  }
  min-height: 92px;
  display: flex;
  align-items: center;
  &:last-child::after {
    display: block !important;
  }
  .taroify-cell__title {
    display: flex;
    flex-direction: column;
    align-self: flex-start;
    margin-top: 6px;
    flex-grow: 0;
    flex-shrink: 0;
    min-width: 204px;
    color: $labelColor;
    //justify-content: center;
  }
  .taroify-cell__value {
    width: 100px;
    //flex: 1;
    .taroify-native-input {
      &::placeholder {
        font-size: 32px;
      }
    }
  }
  &.placeholder {
    .taroify-form-control,
    .rich-right,
    .taroify-cell__value {
      color: $placeholderColor;
    }
  }
  &.hiddenRight {
    .taroify-cell__value {
      display: none;
    }
    .taroify-cell__title {
      flex: 1;
    }
  }
  &.taroify-cell--required::before {
    height: 100%;
    top: 0;
    padding-top: 20px;
    position: absolute;
  }
  .taroify-form-item__right-icon {
    height: 44px;
  }
  .taroify-form-feedback {
    font-size: 26px;
    color: #f34c4b;
    line-height: 40px;
  }
  &.disabled {
    background-color: $disabledBackgroundColor;
    textarea {
      color: $disabledTextColor !important;
    }
    .taroify-form-control,
    .taroify-cell__value,
    .taroify-input--disabled {
      color: $disabledTextColor !important;
      -webkit-text-fill-color: $disabledTextColor !important;
    }
    .taroify-cell__title {
      color: $labelDisabledColor;
    }
  }
  &.placeholder {
    .rich-right,
    *::placeholder {
      color: $placeholderColor !important;
      -webkit-text-fill-color: $placeholderColor !important;
    }
    .disable-text {
      color: $disabledPlaceholderColor !important;
      -webkit-text-fill-color: $disabledPlaceholderColor !important;
    }
    &.disabled {
      .taroify-form-control,
      .taroify-input--disabled,
      .taroify-cell__value {
        color: $disabledPlaceholderColor !important;
        -webkit-text-fill-color: $disabledPlaceholderColor !important;
      }
      *::placeholder {
        color: $disabledPlaceholderColor !important;
        -webkit-text-fill-color: $disabledPlaceholderColor !important;
      }
    }
  }
  .disable-text {
    color: $disabledTextColor;
  }
  .content {
    display: flex;
    .rightIcon {
      flex-shrink: 0;
      margin-left: 16px;
      display: flex;
      align-items: center;
      padding-top: 2px;
      .i-icon-icon-seemore {
        display: flex;
      }
    }
    .cellChildren {
      width: 0;
      flex-grow: 1;
      text-align: right;
      font-size: 32px;
      word-break: break-all;
      .taroify-native-input {
        font-size: 32px;
      }
    }
  }

  &.cardMode {
    background-color: #fff;
    min-height: unset;
    line-height: 48px;
    padding-top: 0;
    padding-bottom: 0;
    margin-top: 4px;
    margin-bottom: 4px;

    textarea {
      color: #12274d !important;
    }
    &.mb-row-cell {
      &.disabled {
        .taroify-form-control,
        .taroify-cell__value,
        .taroify-input--disabled {
          color: #12274d !important;
          -webkit-text-fill-color: #12274d !important;
        }
      }
    }
    .taroify-cell__title {
      font-size: 30px;
    }

    .disable-text {
      color: #12274d !important;
    }

    &::after {
      content: none;
    }
  }
}
