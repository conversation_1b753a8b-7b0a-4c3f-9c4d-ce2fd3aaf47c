import React, { useRef, ReactElement, useEffect } from 'react';
import classnames from 'classnames';
import { View } from '@tarojs/components';
import { Cell, Field } from '@taroify/core';
import Taro from '@tarojs/taro';
import { useIntl } from '@/util/intl';
import { _ } from '@/util';
import Tooltip from '../yq-tooltip';
import sectionField from '../../util/sectionField';
import { MbRowProps } from './main';
import { YqIcon } from '../index';
import './index.scss';

const imgPattern = /<img(.*?)>/g;
const htmlTagPattern = /<[^>]*>/g;

// export default MbRow;
export default function MbRow(props: MbRowProps) {
  const {
    value,
    brief,
    label,
    onClick = () => { },
    handleClear = () => { },
    className,
    rightIcon,
    error,
    disabled,
    clearButton,
    placeholder,
    hiddenRight,
    children,
    field,
    values,
    copyable = true,
    textValue,
    contentRef,
    userValue,
    cellClassName,
    cardMode = false,
  } = props;
  const ICell = children && !disabled ? Field : Cell;
  const intl = useIntl();
  const ref = useRef();
  const cellChildren = children || (hiddenRight ? '' : (userValue || value));

  useEffect(() => {
    if (field && value) {
      sectionField.validator(field, values?.[field?.code] || value, values, intl);
    }
  }, [value, field, values, intl]);

  const handleCopy = (): void => {
    let copyValue = textValue || value;
    if (React.isValidElement(copyValue) || !copyValue) return;
    if (field?.widgetType === 'RichText') {
      const imageReplace = `[${intl.formatMessage({ id: 'yqc.mobile.picture', defaultMessage: '图片' })}]`;
      copyValue = copyValue.replaceAll(imgPattern, imageReplace).replaceAll(htmlTagPattern, '').replaceAll('\u200B', '');
    }
    Taro.setClipboardData({
      data: copyValue,
      success() {
        Taro.atMessage({
          type: 'success',
          message: intl.formatMessage({ id: 'yqc.mobile.copy.successful', defaultMessage: '复制成功!' }),
        });
      },
    });
  };

  const renderClearButton = (): ReactElement | null => (disabled ? null : (
    <View
      className="mb-row-clear-button"
      onClick={(e) => {
        e.stopPropagation();
        e.preventDefault();
        handleClear();
      }}
    >
      <YqIcon type="CloseOne" theme="filled" size={36} fill="#909aad" />
    </View>
  ));

  const renderTitle = (): ReactElement => (
    <View
      style={{ userSelect: 'none', textAlign: 'center' }}
      className="nocopy"
      onClick={handleCopy}
    >
      {intl.formatMessage({ id: 'yqc.mobile.copy', defaultMessage: '复制' })}
    </View>
  );

  return (
    <Tooltip trigger={(copyable && (textValue || value) && typeof (textValue || value) === 'string') ? ['longPress'] : []} title={renderTitle()}>
      <ICell
        {...props}
        onClick={() => !disabled && onClick()}
        clickable={!disabled}
        feedback={error && children && <Field.Feedback align={props.align || 'right'} status="invalid">{error}</Field.Feedback>}
        className={classnames('mb-row-cell', { hiddenRight }, { placeholder: _.isEmpty(value) }, { disabled }, { cardMode }, className)}
        // placeholder={placeholder}
        title={(<View className="label-wrapper">{label}</View>)}
        rightIcon={undefined}
        brief={brief && (
          <>
            {brief}
            {error && <Field.Feedback align={props.align || 'right'} status="invalid">{error}</Field.Feedback>}
          </>
        )}
      >
        <View style={{ width: '100%' }}>
          <View
            className="content"
            ref={process.env.TARO_ENV === 'weapp' ? ref : contentRef}
            onClick={() => {
              if (process.env.TARO_ENV === 'weapp' && contentRef) {
                contentRef.current = ref.current;
              }
            }}
          >
            <View className={classnames(cellClassName, 'cellChildren')}>{cellChildren || placeholder || '\u200B'}</View>
            {!cardMode && (
              <View className="rightIcon">
                {(clearButton && !_.isEmpty(value) && !disabled) ? renderClearButton() : rightIcon}
              </View>
            )}
          </View>
          {!children && <Field.Feedback align="right" status="invalid">{error}</Field.Feedback>}
        </View>
      </ICell>
    </Tooltip>
  );
}
