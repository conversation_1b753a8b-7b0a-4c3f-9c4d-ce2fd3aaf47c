import { ReactElement } from 'react';

interface Field {
  widgetType: string;
}

interface Values {

}

export interface MbRowProps {
  value?: string | ReactElement;
  brief?: string | ReactElement | null | ReactElement[];
  placeholder?: string;
  label?: string;
  onClick?: Function;
  handleClear?: Function;
  className?: string;
  error?: string;
  rightIcon?: string | ReactElement | boolean | null;
  disabled?: boolean;
  clearButton?: boolean;
  hiddenRight?: boolean;
  children?: ReactElement | string;
  field?: Field;
  values?: Values;
  copyable?: boolean;
  required?: boolean;
  textValue?: string;
  contentRef?
  userValue?
  cellClassName?: string;
  cardMode?: boolean;
  align?: 'left' | 'right';
}
