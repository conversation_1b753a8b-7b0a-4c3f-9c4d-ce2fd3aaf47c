import React, { useRef } from 'react';
import { View } from '@tarojs/components';
import { observer } from 'mobx-react-lite';
import Taro from '@tarojs/taro';
import appStore from '@/store/app';

interface DraggableProps {
  className?: string;
  onClick?: () => void;
  children?: any
}

const Draggable: React.FC<DraggableProps> = function Draggable(props) {
  const { className = '', children, onClick = () => {} } = props;

  const zIndex = appStore.botZindex;

  const draggableRef = useRef<HTMLDivElement>();

  const viewArea = useRef({ width: Taro.getSystemInfoSync().windowWidth, height: Taro.getSystemInfoSync().windowHeight });

  const position = useRef({
    x: (Math.min(Taro.getSystemInfoSync().windowWidth, 500)) - 70,
    y: viewArea.current.height - 130,
  });

  const handleTouchStart = (e) => {
    e.preventDefault();
    e.stopPropagation();
    const { touches } = e;
    if (!Array.isArray(touches)) return;
    document.addEventListener('touchmove', handleTouchMove);
    document.addEventListener('touchend', hanldeTouchEnd);
  };

  const hanldeTouchEnd = (e) => {
    e.preventDefault();
    e.stopPropagation();
    document.removeEventListener('touchmove', handleTouchMove);
    document.removeEventListener('touchend', hanldeTouchEnd);
  };

  const handleTouchMove = (e) => {
    e.preventDefault();
    e.stopPropagation();
    const { width: windowWidth, height: windowHeight } = viewArea.current;
    const dragItem = draggableRef.current;
    const { width = 0, height = 0 } = dragItem?.getBoundingClientRect() || {};
    const x = e.touches[0].clientX;
    const y = e.touches[0].clientY;
    const leftAreaWidth = (windowWidth - 500) / 2;
    let left = x < leftAreaWidth ? leftAreaWidth : x;
    left = left > (leftAreaWidth + 500 - width) ? (500 - width) : (left - leftAreaWidth);
    let top = y < 0 ? 0 : y;
    top = top > (windowHeight - height) ? (windowHeight - height) : top;
    position.current = { x: left, y: top };
    if (dragItem) {
      dragItem.setAttribute('style', `position:absolute;left:${left}px;top:${top}px;z-index:${zIndex}`);
    }
  };

  return (
    <View
      ref={draggableRef}
      onTouchStart={handleTouchStart}
      onClick={onClick}
      className={className}
      style={{ position: 'absolute', left: position.current.x, top: position.current.y, zIndex }}
    >
      {children}
    </View>
  );
};

export default observer(Draggable);
