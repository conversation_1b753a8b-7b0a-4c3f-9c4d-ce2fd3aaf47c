import { useMemo, useState, useCallback, useEffect, useContext } from 'react';
import { useLocalObservable, observer } from 'mobx-react-lite';
import Taro from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { Empty, DropdownMenu } from '@taroify/core';
import appStore from '@/store/app';
import { fetch } from '@/util';
import Loading from '@/components/loading';
// import YqComponentTitleFrame from '@/components/yq-component-title-frame';
import styles from './ApprovalInfo.module.less';

const COLOR_MAP = {
  processed: '#79c729',
  processing: '#5c5cf9',
  'to be processed': '#a8d1f5',
  'not processed': '#c9c9c9',
  automaticallyProcessed: '#79c729',
};

const GREY_COLOR_MAP = {
  INTERRUPT: '#81888f',
  WITHDRAW: '#81888f',
  REJECTED: '#81888f',
  PROCESSED: '#81888f',
};

function ApprovalInfo({ intl, field, values }) {
  const statusIntlMap = useMemo(() => ({
    PROCESSED: intl.formatMessage({ id: 'lcr.renderer.desc.approve.complete', defaultMessage: '处理完成' }),
    APPROVED: intl.formatMessage({ id: 'lcr.renderer.desc.approve.approved', defaultMessage: '审批通过' }),
    REJECTED: intl.formatMessage({ id: 'lcr.renderer.desc.approve.rejected', defaultMessage: '审批拒绝' }),
    RETURN: intl.formatMessage({ id: 'lcr.renderer.desc.approve.return', defaultMessage: '审批驳回' }),
    AUTO_SKIP: intl.formatMessage({ id: 'lcr.renderer.desc.approve.skipped', defaultMessage: '自动跳过' }),
    WITHDRAW: intl.formatMessage({ id: 'lcr.renderer.desc.approve.withdraw', defaultMessage: '撤回' }),
    INTERRUPT: intl.formatMessage({ id: 'lcr.renderer.desc.approve.interrupt', defaultMessage: '中断' }),
    PENDING: intl.formatMessage({ id: 'lcr.renderer.desc.approve.pending', defaultMessage: '将要处理' }),
    PROCESSING: intl.formatMessage({ id: 'lcr.renderer.desc.approve.processing', defaultMessage: '审批中' }),
    UNPROCESSED: intl.formatMessage({ id: 'lcr.renderer.desc.approve.unprocessed', defaultMessage: '未处理' }),
    AUTOMATICALLY: intl.formatMessage({ id: 'lcr.renderer.desc.approve.automatically', defaultMessage: '已自动处理' }),
  }), []);

  const customConfig = field?.customConfig || [];
  const { idFieldCode, height , hiddenHeader } = customConfig.reduce((acc, cur) => {
    if (cur) {
      acc[cur.key] = cur.value;
    }
    return acc;
  }, {});
  const businessId = idFieldCode ? (values[idFieldCode]?.id || values[idFieldCode]) || values.id : values.id;
  const [currentWf, setCurrentWf] = useState('');

  const store = useLocalObservable(() => ({
    loading: false,
    wfInstances: [],
    records: [],
    async fetchWFInstance(id) {
      store.loading = true;
      const res = await fetch(`/workflow/v1/workflowInstances/${appStore.tenantId}/queryByBusiness/${id}`).catch(() => {});
      if (!res || res.failed) {
        Taro.atMessage({
          type: 'error',
          message: !res ? intl.formatMessage({ id: 'yqc.mobile.api.wrong', defaultMessage: '接口出现报错，请联系管理员' }) : res.message,
        });
        store.loading = false;
        return;
      }
      store.wfInstances = res;
      setCurrentWf(res?.[0]?.id || '');
      return res;
    },
    async fetchApprovalInfo(wfId) {
      const res = await fetch(`/workflow/v1/organizations/${appStore.tenantId}/workflows/${wfId}/list-approval`).catch(() => {});
      if (!res || res.failed) {
        Taro.atMessage({
          type: 'error',
          message: !res ? intl.formatMessage({ id: 'yqc.mobile.api.wrong', defaultMessage: '接口出现报错，请联系管理员' }) : res.message,
        });
        store.loading = false;
        return;
      }
      store.records = res;
      store.loading = false;
    },
  }));

  useEffect(() => {
    if (businessId) {
      (async () => {
        const wfInstance = await store.fetchWFInstance(businessId);
        // 默认选中第一个
        if (wfInstance?.[0]?.id) {
          await store.fetchApprovalInfo(wfInstance[0].id);
        } else {
          store.loading = false;
        }
      })();
    }
  }, [businessId]);

  const renderItem = (item) => {
    const {
      nodeCode, nodeName,
      assignee, endDate, commentContent,
      status, stage,
    } = item;

    let fontColor = '#fff';
    if (!GREY_COLOR_MAP[status]) {
      fontColor = stage === 'processed' ? COLOR_MAP[stage] : undefined;
    }
    const borderColor = GREY_COLOR_MAP[status] || COLOR_MAP[stage];

    return (
      <View key={nodeCode} className={styles.item}>
        <View className={styles.rowWithSpace}>
          <Text className={styles.name}>{nodeName}</Text>
          <Text
            className={styles.tag}
            style={{
              color: fontColor,
              border: `1px solid ${borderColor}`,
              backgroundColor: fontColor ? GREY_COLOR_MAP[status] : `${borderColor}12`,
            }}
          >{statusIntlMap[status] || status}</Text>
        </View>
        {assignee && (
          <View className={styles.row}>
            <Text className={styles.label}>{intl.formatMessage({ id: 'yqc.mobile.approval.info.approver', defaultMessage: '审批人：' })}</Text>
            <Text>{assignee}</Text>
          </View>
        )}
        {endDate && (
          <View className={styles.row}>
            <Text className={styles.label}>{intl.formatMessage({ id: 'yqc.mobile.approval.info.time', defaultMessage: '审批时间：' })}</Text>
            <Text>{endDate}</Text>
          </View>
        )}
        {commentContent && (
          <View className={styles.row}>
            <Text className={styles.label}>{intl.formatMessage({ id: 'yqc.mobile.approval.info.comment', defaultMessage: '审批意见：' })}</Text>
            <Text>{commentContent}</Text>
          </View>
        )}
      </View>
    );
  };

  const renderApprovalContent = (info) => {
    if (info.taskHistoryList) {
      if (info.status === 'processed') {
        // 已处理，需要按照已处理的逐个展示（过滤掉自动跳过）
        return info.taskHistoryList
          .filter(history => history.status !== 'AUTO_SKIP')
          .map(history => {
            return renderItem({
              ...info,
              stage: info.status || 'not processed',
              taskHistoryList: null,
              ...history,
              status: history.status === 'automaticallyProcessed' ? 'AUTOMATICALLY' : history.status || 'UNPROCESSED',
            });
          });
      } else {
        // 未处理或当前待处理的，汇总显示
        return renderItem({
          ...info,
          stage: info.status || 'to be processed',
          taskHistoryList: null,
          status: info.status === 'processing' ? 'PROCESSING' : 'PENDING',
          assignee: info.taskHistoryList.map(history => history.assignee).join('\\'),
        });
      }
    } else {
      // 无审批人信息，默认显示为将要处理
      return renderItem({
        ...info,
        stage: info.status || 'to be processed',
        // eslint-disable-next-line no-nested-ternary
        status: info.status === 'processing' ? 'PROCESSING' : (info.status === 'processed' ? 'PROCESSED' : 'PENDING'),
      });
    }
  };

  const renderDropMenu = () => {
    if (!store.wfInstances?.length) {
      return null;
    }
    return (
      <View className={styles.menu}>
        {hiddenHeader ? null : <DropdownMenu>
          <DropdownMenu.Item
            value={currentWf}
            onChange={setCurrentWf}
          >
            {store.wfInstances.map(option => (<DropdownMenu.Option value={option.id}>{option?.workflowName || ''}</DropdownMenu.Option>))}
          </DropdownMenu.Item>
        </DropdownMenu>}
      </View>
    );
  };

  const renderApprovalInfo = () => {
    return (
      <View className={styles.content}>
        {store.records.map(renderApprovalContent)}
      </View>
    );
  };

  return (
    <Loading loading={store.loading}>
      {/* <YqComponentTitleFrame header={field.name || intl.formatMessage({ id: 'yqc.mobile.approval.info', defaultMessage: '审批信息' })} /> */}
      {store.records?.length > 0 ? (
        <View className={styles.wrap} style={height ? { height: `${height}px` } : null}>
          {renderDropMenu()}
          {renderApprovalInfo()}
        </View>
      ) : (
        <Empty>
          <Empty.Description>
            {intl.formatMessage({ id: 'yqc.mobile.no.approval.info', defaultMessage: '暂无审批信息' })}
          </Empty.Description>
        </Empty>
      )}
    </Loading>
  );
}

export default observer(ApprovalInfo);
