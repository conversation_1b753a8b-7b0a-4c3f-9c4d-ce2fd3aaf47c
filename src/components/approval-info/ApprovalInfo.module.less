.wrap {
  padding: 0 40px;
  overflow: auto;

  .menu {
    margin: 40px 0;
  }

  .content {
    .item {
      background: #f8f8f8;
      padding: 32px 32px 16px;
      margin-bottom: 16px;
      border-radius: 4px;

      .rowWithSpace {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
      }

      .row {
        display: flex;
        align-items: flex-start;
        margin-bottom: 16px;
        color: rgba(0, 0, 0, .65);
      }

      .label {
        white-space: nowrap;
      }

      .name {
        font-size: 32px;
        font-weight: 500;
        margin-bottom: 16px;
      }

      .tag {
        font-weight: 400;
        display: inline-block;
        font-size: 24px;
        border-radius: 24px;
        text-align: center;
        line-height: 40px;
        white-space: nowrap;
        padding: 2px 16px;
      }
    }
  }
}
