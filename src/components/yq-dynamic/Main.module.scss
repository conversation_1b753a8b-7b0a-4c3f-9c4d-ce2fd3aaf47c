
.content {
  width: 100%;
  background-color: #fff;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px 32px 48px;

  .left {
    font-size: 32px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    line-height: 45px;
  }
  .right {
    font-size: 28px;
    color: #2979FF;
    line-height: 44px;
    display: flex;
    align-items: center;
  }
}

.itemRight {
  width: 100px;
  flex: 1;
  margin-left: 16px;
}
.itemHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}
.actionName {
  font-size: 30px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #12274D;
  line-height: 40px;
}
.actionDate {
  height: 32px;
  font-size: 24px;
  color: rgba(18,39,77,0.4500);
  line-height: 32px;
  flex-shrink: 0;
  margin-right: 5px;
}

.timeline {
  padding: 0 32px;
}

.popupItem {
  text-align: center;
}

.line {
  width: 100%;
  height: 1px;
  background-color: #bfbfbf;
  margin: 24px 0;
}
