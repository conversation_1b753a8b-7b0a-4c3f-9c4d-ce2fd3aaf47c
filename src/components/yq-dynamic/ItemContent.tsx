/* eslint-disable jsx-quotes */
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { observer } from 'mobx-react-lite';
import { useState } from 'react';
import appStore from '@/store/app';
import { useIntl } from '@/util/intl';
import YqIcon from '../yq-icon';
import { _, download } from '../../util';
import { getRichJson } from '../../util/quill-util';
import DOMPurify from '../../util/dompurify';
import styles from './ItemContent.module.scss';

const { sanitize } = DOMPurify;
interface ItemContentProps {
  record: any;
}

function ItemContent(props: ItemContentProps) {
  const { record } = props;
  const { type, itemDTOS, relatedTicket } = record;
  const intl = useIntl();
  const [showMore, setShowMore] = useState<boolean>(false);

  const handleDownload = async (fileKey: string): Promise<void> => {
    if (fileKey) {
      if ((appStore.isWxwork || appStore.isLark || appStore.isDingtalk || appStore.isWechat) && !_.isPc()) {
        _.navigateTo({
          url: `/packageOther/pages/confirm-download/index?fileKey=${fileKey}`,
        });
      } else {
        try {
          await download(fileKey, fileKey?.split('@')[1]);
        } catch (error) {
          // eslint-disable-next-line no-console
          console.error('error -- ', error);
        }
      }
    } else {
      Taro.atMessage({
        type: 'error',
        message: intl.formatMessage({ id: 'yqc.mobile.cannot.download', defaultMessage: '该文件暂时无法下载' }),
      });
    }
  };
  const renderWidgetTypeView = (widgetType, value) => {
    if (widgetType === 'RichText') {
      const jsonObj = getRichJson(value);
      return (
        <>
          <View dangerouslySetInnerHTML={{ __html: sanitize(_.transRichTextToHtml(value || '-')) }} />
          {jsonObj?.attachments?.map(f => (
            <View key={f.fileKey ? f.fileKey : f}>
              {f.fileKey ? f.fileKey?.split('@')?.[1] : f.split('@')?.[1]}
              <YqIcon type="download" size={24} fill="#000" style={{ marginLeft: '10px' }} onClick={() => handleDownload(f.fileKey)} />
            </View>
          ))}
        </>
      );
    } else if (widgetType === 'Upload') {
      const curValue = _.isJSON(value) ? JSON.parse(value) : value;
      return Array.isArray(curValue) ? curValue.map(v => (
        <View style={{ display: 'flex', alignItems: 'center' }}>
          <View dangerouslySetInnerHTML={{ __html: sanitize(v.name || v.fileKey.split('@')?.[1]) }} />
          <YqIcon type="download" size={24} fill="#000" style={{ marginLeft: '10px' }} onClick={() => handleDownload(v.fileKey)} />
        </View>)) : '';
    } else {
      return <View dangerouslySetInnerHTML={(_.getRichText(value || '-'))} />;
    }
  };

  const renderItemContentItem = (itemDTO) => {
    const realNewValue = itemDTO.newValueName || itemDTO.newValue || '';
    let newMessageContent = renderWidgetTypeView(itemDTO.widgetType, realNewValue);
    if (itemDTO.fieldCode === 'attachments' && _.isJSON(realNewValue)) {
      newMessageContent = (
        <View>
          {JSON.parse(realNewValue).map(f => (
            <View key={f.fileKey ? f.fileKey : f}>
              {f.fileKey ? f.fileKey?.split('@')?.[1] : f.split('@')?.[1]}
            </View>
          ))}
        </View>
      );
    }
    return (
      <View className={styles.textContent} key={itemDTO.id}>
        <View className={styles.leftText}>{itemDTO.fieldName}</View>
        <View className={styles.options}>{intl.formatMessage({ id: 'yqc.mobile.changeto', defaultMessage: '变更为' })}</View>
        <View className={styles.rightText}>{newMessageContent}</View>
      </View>
    );
  };

  const renderActionIcon = actionType => {
    let icon = <YqIcon fill="#2979FF" theme="filled" type="ArrowCircleRight" size={32} />;
    switch (actionType) {
      case 'TODO':
        icon = (
          <YqIcon
            fill="#2979FF"
            theme="filled"
            type="ArrowCircleRight"
            size={32}
          />
        );
        break;
      case 'PASS':
        icon = (
          <YqIcon fill="#7BC95A" theme="filled" type="CheckOne" size={32} />
        );
        break;
      case 'REJECT':
        icon = (
          <YqIcon fill="#FF4949" theme="filled" type="CloseOne" size={32} />
        );
        break;
      case 'TRANSFER':
        icon = (
          <YqIcon
            fill="#FF9100"
            theme="filled"
            type="UpdateRotation"
            size={32}
          />
        );
        break;
      default:
        break;
    }
    return icon;
  };

  if (type === 'RELATED_TICKET') {
    // 能进这里就表示relatedTicket一定有
    const relatedData = _.isJSON(relatedTicket)
      ? JSON.parse(relatedTicket)
      : [];
    return (
      <View className="dynamic-content">
        {relatedData.map(item => (
          <View className="dynamic-content-message" key={item.id}>
            <View className="dynamic-content-message-left">{item.number}</View>
            <View className="dynamic-content-message-right">
              {item.shortDescription}
            </View>
          </View>
        ))}
      </View>
    );
  } else if (type === 'APPROVAL') {
    const prefixCls = 'dynamic-approval';
    const {
      action,
      approvalNode,
      actionName,
      transferTargetName,
      approvalRemark,
    } = record || {};
    return (
      <View className={`${prefixCls}`}>
        <View className={`${prefixCls}-top`}>
          <View className={`${prefixCls}-top-icon`}>
            {renderActionIcon(action)}
          </View>
          <View className={`${prefixCls}-top-description`}>
            {approvalNode}
            {actionName ?? `：${actionName}`}
          </View>
        </View>
        <View className={`${prefixCls}-bottom`}>
          {transferTargetName && (
            <View className={`${prefixCls}-bottom-transfer`}>
              {intl.formatMessage({ id: 'yqc.mobile.approval.transfer.to', defaultMessage: '转交至: ' })}
              {transferTargetName}
            </View>
          )}
          {approvalRemark && (
            <View className={`${prefixCls}-bottom-remark`}>
              {action === 'TRANSFER'
                ? intl.formatMessage({ id: 'yqc.mobile.remark', defaultMessage: '备注' })
                : intl.formatMessage({ id: 'yqc.mobile.approval.opinion', defaultMessage: '审批意见' })}
              {`: ${approvalRemark}`}
            </View>
          )}
        </View>
      </View>
    );
  } else if (type === 'PARTICIPANT') {
    const { participantList } = record;
    return (
      <View className={`${styles.contentWrapper} ${styles.participant}`}>
        {participantList?.length > 0 && participantList.map(p => p.participant).join(', ')}
      </View>
    );
  } else if (type === 'CUSTOM_ACTION') {
    const { action, customTitle, customContent } = record;
    if (action === 'ACTION' && itemDTOS) {
      return <View className={styles.contentWrapper}>{itemDTOS.map(renderItemContentItem)}</View>;
    }
    if (customTitle || customContent) {
      const content = (customContent || '').split('\n');
      return (
        <View className={styles.contentWrapper}>
          <Text className={styles.title}>{customTitle}</Text>
          <View className={styles.textContent}>
            {content.map(item => <View>{item}</View>)}
          </View>
        </View>
      );
    }
    return null;
  } else if (itemDTOS && itemDTOS.length > 0) {
    return (
      <View className={styles.contentWrapper}>
        <View className={styles.content}>
          {itemDTOS.filter((_item, index) => index < 5 || showMore).map(renderItemContentItem)}
          {itemDTOS.length > 5 && !showMore && <View className={styles.showMore} onClick={() => setShowMore(true)}>{intl.formatMessage({ id: 'yqc.mobile.view.more', defaultMessage: '查看更多' })}</View>}
        </View>
      </View>
    );
  } else {
    return null;
  }
}

export default observer(ItemContent);
