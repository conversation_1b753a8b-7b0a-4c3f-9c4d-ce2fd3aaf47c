/* eslint-disable react-hooks/exhaustive-deps */
import Taro from '@tarojs/taro';
import { View } from '@tarojs/components';
import React, { useEffect, useState, useMemo } from 'react';
import { observer, useLocalObservable } from 'mobx-react-lite';
import { Timeline } from '@taroify/core';
import { injectIntl } from '@/util/intl';
import { fetch, _ } from '@/util';
import Loading from '@/components/loading';
import YqAvatar from '@/components/yq-avatar';
import Icon from '@/components/yq-icon';
import appStore from '@/store/app';
import './index.scss';
import styles from './Main.module.scss';
import ItemContent from './ItemContent';
import YqTooltip from '../yq-tooltip';
import YqEmpty from '../yq-empty';

const codeMap = {
  NEW_RECORD: 'CREATE',
  ACTION_RECORD: 'ACTION',
  UPDATE_RECORD: 'UPDATE',
  APPROVAL_RECORD: 'APPROVAL',
  RELATED_TICKET: 'RELATED_TICKET',
  REPLY_RECORD: 'REPLY',
  PARTICIPANT: 'PARTICIPANT',
  CUSTOM_ACTION: 'CUSTOM_ACTION',
};

function YqDynamic(props) {
  const {
    ticketId,
    viewCode,
    intl,
    viewId,
    widgetConfig = {},
    businessObjectId,
    name,
    pageStore = {},
    values,
  } = props;

  const [display, setDisplay] = useState(widgetConfig.dynamicPriorityDisplay || '');
  const { params } = useMemo(() => Taro.getCurrentInstance().router || { params: {} as any }, []);
  const displayMapping = {
    ACTION: intl.formatMessage({ id: 'yqc.mobile.message.history', defaultMessage: '动态记录' }),
    OPERATION: intl.formatMessage({ id: 'yqc.mobile.field.update', defaultMessage: '字段更新' }),
    OPEN_APP: intl.formatMessage({ id: 'yqc.mobile.dynamic.openapp', defaultMessage: '三方操作' }),
    TRANSFER: intl.formatMessage({ id: 'yqc.mobile.dynamic.transfer', defaultMessage: '转交记录' }),
  };

  const store = useLocalObservable(() => ({
    loading: false,
    records: [],
    getCurrentBody(dataType, displayFields) {
      const result = { dynamicDisplayDataType: [], dynamicDisplayFields: [] };
      if (dataType && dataType !== '') {
        result.dynamicDisplayDataType = dataType.split(',').map(type => codeMap[type]);
      }
      if (displayFields && displayFields !== '') {
        const fields = _.isJSON(displayFields) ? JSON.parse(displayFields) : displayFields;
        result.dynamicDisplayFields = fields.map(field => field.id);
      }
      return result;
    },
    async fetchDynamic(priorityDisplay, showLoading = true) {
      if (store.loading) return;
      if (!widgetConfig.dynamicDisplayContent) return;
      const realId = ticketId || params.ticketId;
      if (_.isEmpty(realId)) return;
      const realCode = viewCode || params.viewCode;
      const tenantId = appStore.tenantId;
      const {
        dynamicDisplayDataType, // 展示数据类型 六个类型 新建记录、动作记录、审批记录、关联单据、参与者和自定义按钮
        dynamicDisplayFields, // 动态展示字段 比如优先级、问题分类、报告人、紧急度等等
        /**
         * 2023-03-08
         * 下面这个有点坑，关联对象目前只能配置请求项，且视图上必须拖一个请求项的主键
         */
        // dynamicDisplayScItemVariable 是否展示服务项变量
        dynamicDisplayFieldCode, // 其他业务对象code
        dynamicDisplayObjectId, // 其他业务对象id
      } = widgetConfig || {};

      const data = store.getCurrentBody(dynamicDisplayDataType, dynamicDisplayFields);
      const fieldValue = dynamicDisplayFieldCode && (values[dynamicDisplayFieldCode]?.id || values[dynamicDisplayFieldCode]?.id);
      const boId = dynamicDisplayObjectId ? `&businessObjectId=${dynamicDisplayObjectId}` : '';
      const queryString = `?viewCode=${realCode}&viewId=${viewId}&type=${priorityDisplay}${boId}`;

      // const queryString = `?businessObjectId=${dynamicDisplayObjectId || businessObjectId}&viewCode=${realCode}&viewId=${viewId}&type=${priorityDisplay}`;
      const url = `itsm/v1/${tenantId}/activity/${fieldValue || realId}/list${queryString}`;
      showLoading && (store.loading = true);
      const res = await fetch(url, data, 'POST').catch(() => { });
      store.loading = false;
      if (!res || res.failed) {
        Taro.atMessage({
          type: 'error',
          message: !res ? intl.formatMessage({ id: 'yqc.mobile.api.wrong', defaultMessage: '接口出现报错，请联系管理员' }) : res.message,
        });
        return;
      }
      store.records = res;
    },
  }));

  useEffect(() => {
    // 若包含，则展示优先展示的字段；由于只有两个字段，且类型为以逗号分隔的字符串，若不包含则剩下的一个肯定是展示的字符串
    // 若都没有则名称默认为优先展示的字段，但不显示内容
    const flag = widgetConfig.dynamicDisplayContent?.includes(widgetConfig.dynamicPriorityDisplay);
    const operationType = flag ? widgetConfig.dynamicPriorityDisplay : widgetConfig.dynamicDisplayContent;
    setDisplay(operationType || widgetConfig.dynamicPriorityDisplay);
    // 设计器里的预览不需要显示数据，也无法显示数据
    if (appStore.fromDesigner !== 'true') {
      store.fetchDynamic(operationType, true);
    }
  }, []);

  const handleSelect = async (value) => {
    await store.fetchDynamic(value, false);
    setDisplay(value);
  };

  const renderActionTitle = (record) => {
    if (!record) return null;
    const type = record.type;
    const action = record.action;
    // 操作
    if (type === 'ACTION') return `${record.actionName}`;
    // 参与人
    if (type === 'PARTICIPANT') {
      if (action === 'ADD_PARTICIPANT') {
        return intl.formatMessage({ id: 'yqc.mobile.add.participants', defaultMessage: '添加参与人' });
      } else if (action === 'DELETE_PARTICIPANT') {
        return intl.formatMessage({ id: 'yqc.mobile.remove.participants', defaultMessage: '移除参与人' });
      } else if (action === 'ADD_GROUP') {
        return intl.formatMessage({ id: 'yqc.mobile.add.participating.group', defaultMessage: '添加参与组' });
      } else if (action === 'DELETE_GROUP') {
        return intl.formatMessage({ id: 'yqc.mobile.remove.participating.group', defaultMessage: '移除参与组' });
      }
      return null;
    }
    if (type === 'CUSTOM_ACTION') {
      return action === 'ACTION' ? record.taskName : record.customAction;
    }
    if (type === 'VARIABLE') {
      return intl.formatMessage({ id: 'yqc.mobile.update.variable', defaultMessage: '更新 变量' });
    }
    return `${record.actionName} ${record.operationTargetName || ''}`;
  };

  // TODO
  const renderDynamicItem = (record) => {
    const {
      updatePersonName,
      updatePersonId,
      imageUrl,
      action,
      type,
      updatedAt,
      times, // 这边后端告诉我的逻辑是，只要times存在，就一定是催办次数，不可能是别的次数。
    } = record;

    const actionName = type === 'CUSTOM_ACTION' && action === 'ACTION' ? record.actionName : '';
    return (
      <Timeline.Item key={record.id}>
        <Timeline.Content>
          <View className={styles.itemRight}>
            <View className={styles.itemHeader}>
              <View className={styles.actionName}>
                {`${updatePersonName} ${renderActionTitle(record) || ''} ${actionName}`}
                {times && (
                  <>
                    <Icon style={{ marginLeft: '5px', marginRight: '5px' }} type="remind" size={24} fill="#f34c4b" />
                    <span style={{ color: '#f34c4b', fontSize: '14px' }}>{intl.formatMessage({ id: 'yqc.mobile.button.press.times', defaultMessage: '已催第{times}次' }).replace('{times}', times)}</span>
                  </>
                )}
              </View>
              <View className={styles.actionDate}>
                {updatedAt || intl.formatMessage({ id: 'yqc.mobile.to.approve', defaultMessage: '待审批' })}
              </View>
            </View>
            <ItemContent record={record} />
          </View>
        </Timeline.Content>
        <Timeline.Separator>
          <Timeline.Dot>
            <YqAvatar personId={updatePersonId} size="small" fileKey={imageUrl}>{updatePersonName}</YqAvatar>
          </Timeline.Dot>
          <Timeline.Connector />
        </Timeline.Separator>
      </Timeline.Item>

    );
  };

  const renderContent = () => {
    if (store.records.length > 0) {
      return store.records.map(renderDynamicItem);
    }
    return (
      <YqEmpty description={intl.formatMessage({ id: 'yqc.mobile.no.dynamic', defaultMessage: '暂无动态' })} />
    );
  };

  return (
    <View>
      <Loading loading={store.loading} />
      <View className={styles.content}>
        <View className={styles.header}>
          <View className={styles.left}>{name || intl.formatMessage({ id: 'yqc.mobile.message.history', defaultMessage: '动态记录' })}</View>
          {widgetConfig.dynamicDisplayContent ? (
            <YqTooltip
              title={widgetConfig.dynamicDisplayContent.split(',').filter(v => v !== 'ALL').map((content, index, arr) => (
                <React.Fragment>
                  <View className={styles.popupItem} onClick={() => handleSelect(content)}>
                    {displayMapping[content]}
                  </View>
                  {index < arr.length - 1 && <View className={styles.line} />}
                </React.Fragment>
              ))}
              placement="bottomRight"
              scope={pageStore.scope}
              clickToClose
            >
              <View className={styles.right}>
                {displayMapping[display]}
                <Icon type="Down" theme="outline" size={40} fill="#2979ff" />
              </View>
            </YqTooltip>
          ) : null}
        </View>
        <View className={styles.timeline}>
          <Timeline position="right">
            {renderContent()}
          </Timeline>
        </View>
      </View>
    </View>
  );
}

export default injectIntl(observer(YqDynamic));
