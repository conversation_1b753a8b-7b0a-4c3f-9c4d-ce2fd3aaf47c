/* eslint-disable no-console */
/* eslint-disable camelcase */
/* eslint-disable react-hooks/exhaustive-deps */
import { View } from '@tarojs/components';
import { useMemo, useCallback, useEffect, useState, useRef } from 'react';
import { observer, useLocalObservable } from 'mobx-react-lite';
import Taro, { useDidShow } from '@tarojs/taro';
import { inject } from 'mobx-react';
import { Checkbox, Loading } from '@taroify/core';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { injectIntl } from '@/util/intl';
import { addAction } from '@/util/guance';
import YqNavBar from '@/components/yq-navbar';
import { _, constants, fetch } from '@/util';
import YqList from '@/components/yq-list';

import BatchAction from '../batch-action';
import './index.scss';
import '../ui-action/index.scss';
import Page from '../../global-component/page';

const surveyState = constants.SURVEY_STATE;

dayjs.extend(relativeTime);

function NewEventList(props) {
  const { handleListBack, identify, titleName, appStore, intl, setListStoreRef } = props;
  const [selectAll, setSelectAll] = useState(false);
  const [open, setOpen] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [openFeedback, setOpenFeedback] = useState(false);
  const storeRef = useRef<any>();
  const params = useMemo(() => Taro.getCurrentInstance()?.router?.params, []);
  const showNumber = useMemo(() => {
    return appStore.permissionModule?.find(module => module.code === identify)?.enableShowNumber || false;
  }, [appStore.permissionModule]);

  useDidShow(() => {
    setRefreshKey(Math.random());
  });

  const selectStore = useLocalObservable(() => ({
    enabledBatch: params?.enableBatch,
    businessObjectId: '',
    loading: false,
    firstSelectedItem: {} as any,
    submitLoading: false,
    businessObject: [] as any,
    actions: [] as any,
    curAction: {} as any,
    restFields: [] as any,
    selectedList: [] as any,
    formFieldValues: {} as any,
    failedTickets: [] as any,
    numbers: [] as any,
    isSubmit: false,
    statusList: [] as any[],
    priorityList: [] as any[],
    filterNumbers: [] as any,
    setSelectedList: (arr: Array<any>) => {
      if (arr.length === 1) {
        selectStore.businessObjectId = arr[0].businessObjectId;
        selectStore.firstSelectedItem = arr[0];
      }
      selectStore.selectedList = arr;
    },
    fetch: async (_businessObjectId: string, _tenantId: string) => {
      const tableName = selectStore.firstSelectedItem.sourceTable || selectStore.firstSelectedItem.tableName;
      const ticketId = selectStore.firstSelectedItem.sourceTable ? selectStore.firstSelectedItem.sourceId : selectStore.firstSelectedItem.id;
      if (!tableName) {
        return;
      }
      selectStore.loading = true;
      const actionsUrl = `lc/v1/${_tenantId}/lc_actions/apply_list/${_.getTicketViewMapping(tableName)}?id=${ticketId}`;
      const objUrl = `lc/v1/${_tenantId}/object_fields/all/${_businessObjectId}`;
      selectStore.actions = [];
      selectStore.businessObject = [];
      await Promise.all([fetch(actionsUrl), fetch(objUrl)]).then((res) => {
        const [actionsRes, objRes] = res;
        selectStore.actions = Array.isArray(actionsRes) ? actionsRes : [];
        selectStore.businessObject = Array.isArray(objRes) ? objRes : [];
      }).catch((e) => {
        console.error(e);
        Taro.atMessage({
          type: 'error',
          message: intl.formatMessage({ id: 'yqc.mobile.api.wrong', defaultMessage: '接口出现报错，请联系管理员' }),
        });
      }).finally(() => {
        selectStore.loading = false;
      });
    },
    submit: async (_tenantId, _businessObjectId, _viewId, _params) => {
      // eslint-disable-next-line no-chinese/no-chinese
      addAction('我的请求我的任务批量操作', {
        identify,
      });
      selectStore.submitLoading = true;
      if (!_viewId) {
        const res = await fetch(`itsm/v1/${_tenantId}/sc_req_items/portal/${selectStore.firstSelectedItem.id}`);
        _viewId = res?.mobileViewId;
      }
      const submitUrl = `lc/v1/${_tenantId}/lc_actions/${_businessObjectId}/${_viewId}/batch`;
      const failedTickets = await fetch(submitUrl, _params, 'post').catch(() => {});
      selectStore.submitLoading = false;
      selectStore.isSubmit = true;
      if (failedTickets) {
        selectStore.failedTickets = Array.isArray(failedTickets) ? failedTickets : [];
      }
      setRefreshKey(Math.random());
    },
    fetchBubbles: async (_tenantId: string, _personId: string, _identify: string, activeFlagParam: string = '') => {
      const urlMap = {
        TASK: `itsm/v1/${_tenantId}/sc_req_items/mobile/requestOrTask/collect?userId=${_personId}&role=assign${activeFlagParam}`,
        REQUEST: `itsm/v1/${_tenantId}/sc_req_items/mobile/requestOrTask/collect?userId=${_personId}&role=request${activeFlagParam}`,
      };
      const url = urlMap[_identify];
      const data = await fetch(url).catch(() => {});
      if (data && !data.failed) {
        const { state, priority } = data;
        selectStore.statusList = Array.isArray(state) ? state : [];
        selectStore.priorityList = Array.isArray(priority) ? priority : [];
      }
    },
    latestSearchParams: '',
    fetchNumbers: async (searchParams: string, show?: boolean) => {
      selectStore.latestSearchParams = searchParams || selectStore.latestSearchParams;
      const searchStr = searchParams || selectStore.latestSearchParams;
      if ((show || showNumber) && searchStr && appStore.tenantId) {
        const url = `itsm/v1/${appStore.tenantId}/sc_req_items/mobile/requestAndTask/number?${searchStr}`;
        if (identify !== 'SURVEY') {
          const data = await fetch(url).catch(() => {});
          selectStore.filterNumbers = (identify === 'TASK' ? ['allTask', 'doingTask', 'endTask'] : ['allRequest', 'doingRequest', 'endRequest']).map(key => {
            return data.find(item => item.type === key).number || 0;
          });
        }
      }
    },
  }));

  useEffect(() => {
    if (showNumber) {
      selectStore.fetchNumbers('', showNumber);
    }
  }, [appStore.permissionModule, appStore.tenantId, showNumber]);
  useEffect(() => {
    if (appStore.tenantId && appStore.self.personId) {
      selectStore.fetchBubbles(appStore.tenantId, appStore.self.personId, identify, '&search_activeFlag=true');
    }
  }, [identify, appStore.tenantId, appStore.self.personId]);
  useEffect(() => {
    if (selectStore.firstSelectedItem.businessObjectId) {
      selectStore.fetch(selectStore.firstSelectedItem.businessObjectId, appStore.tenantId);
    }
  }, [selectStore.firstSelectedItem]);
  useEffect(() => {
    if (selectStore.selectedList.length === 0 && !selectAll) {
      selectStore.firstSelectedItem = {};
      selectStore.actions = [];
    }
  }, [selectStore.selectedList.length]);

  const { tableFilters, surveyFilters, customFilters, fields } = useMemo(() => {
    const _tableFilters = [
      { name: intl.formatMessage({ id: 'yqc.mobile.all', defaultMessage: '全部' }), id: 2 },
      { name: intl.formatMessage({ id: 'yqc.mobile.in.progress', defaultMessage: '进行中' }), search_activeFlag: '1', id: 1, defaultFlag: true },
      { name: intl.formatMessage({ id: 'yqc.mobile.closed', defaultMessage: '已关闭' }), search_activeFlag: '0', id: 0 },
    ];
    const _surveyFilters = [
      { name: intl.formatMessage({ id: 'yqc.mobile.ticket.to.do', defaultMessage: '待处理' }), id: 0, state: 'PENDING', defaultFlag: true },
      { name: intl.formatMessage({ id: 'yqc.mobile.expired', defaultMessage: '已过期' }), id: 1, state: 'OUT_OF_DATE' },
      { name: intl.formatMessage({ id: 'yqc.mobile.ticket.done', defaultMessage: '已完成' }), id: 2, state: 'COMPLETED' },
    ];
    const _customFilters = [
      {
        name: intl.formatMessage({ id: 'yqc.mobile.conversation.knowledge.title', defaultMessage: '标题' }),
        widgetType: 'Input',
        code: 'search_shortDescription',
        widgetConfig: {},
      },
      {
        name: intl.formatMessage({ id: 'yqc.mobile.number', defaultMessage: '编号' }),
        widgetType: 'Input',
        code: 'search_number',
        widgetConfig: {},
      },
    ];
    // 调查的列表字段所有不同
    const _fields = [
      { code: 'title', name: '', widgetConfig: {}, transformResponse: (_ignore, obj) => (obj?.ticketObjectsList?.short_description || obj.shortDescription || obj.surveyName || obj.short_description) },
      { code: 'number', widgetConfig: {}, name: identify === 'SURVEY' ? intl.formatMessage({ id: 'yqc.mobile.surveyNumber', defaultMessage: '调查编号' }) : intl.formatMessage({ id: 'yqc.mobile.ticketnumber', defaultMessage: '单据编号' }), transformResponse: (_ignore, obj) => (obj?.ticketObjectsList?.number || obj.number || obj.ticketNumber) },
      { code: 'serviceItemName', widgetConfig: {}, name: identify === 'SURVEY' ? '' : intl.formatMessage({ id: 'yqc.mobile.serviceitemname', defaultMessage: '服务项名称' }), transformResponse: (_ignore, obj) => (obj.scItemName || obj.itemName) },
      { code: 'state_id:name', name: '', widgetConfig: {}, transformResponse: (_ignore, obj) => (obj?.ticketObjectsList?.['state_id:name'] || obj.stateName || surveyState[obj.state]?.stateName) },
      { code: 'state_id:color', name: '', widgetConfig: {}, transformResponse: (_ignore, obj) => (obj?.ticketObjectsList?.state_color || obj.stateColor || surveyState[obj.state]?.stateColor) },
      { code: 'priority_id:name', name: '', widgetConfig: {}, transformResponse: (_ignore, obj) => (obj?.ticketObjectsList?.['priority_id:name'] || obj.priorityName || surveyState[obj.state]?.priorityName) },
      { code: 'priority_id:color', name: '', widgetConfig: {}, transformResponse: (_ignore, obj) => (obj?.ticketObjectsList?.priorityColor || obj.priorityColor || surveyState[obj.state]?.priorityColor) },
      { code: 'creationDate', name: identify === 'SURVEY' ? intl.formatMessage({ id: 'yqc.mobile.approval.start.at', defaultMessage: '发起时间' }) : intl.formatMessage({ id: 'yqc.mobile.submittime', defaultMessage: '提交时间' }), widgetConfig: {}, transformResponse: (_ignore, obj) => dayjs(obj.creationDate).format('YYYY-MM-DD') },
      { code: 'submittedName', name: identify === 'SURVEY' ? intl.formatMessage({ id: 'yqc.mobile.surveyObject', defaultMessage: '调查对象' }) : intl.formatMessage({ id: 'yqc.mobile.submittedname', defaultMessage: '提交人' }), widgetConfig: {}, transformResponse: (_ignore, obj) => (obj.submittedName || obj.requestForName) },
    ];
    return {
      tableFilters: _tableFilters,
      surveyFilters: _surveyFilters,
      customFilters: _customFilters,
      fields: _fields,
    };
  }, [identify]);

  const getMessages = id => {
    let defaultTitleName = '';
    let url = '';
    switch (id) {
      case 'SURVEY':
        defaultTitleName = intl.formatMessage({ id: 'yqc.mobile.home.my.survey', defaultMessage: '我的调查' });
        url = `asmt/v1/${appStore.tenantId}/assessment_instances/self`;
        break;
      case 'REQUEST':
        defaultTitleName = intl.formatMessage({ id: 'yqc.mobile.home.my.request', defaultMessage: '我的请求' });
        url = `itsm/v1/${appStore.tenantId}/sc_req_items/mobile/requestAndTask?role=${'request'}&userId=${appStore.self.person?.id}`;
        // url = `itsm/v1/${appStore.tenantId}/sc_req_items/mobile`;
        break;
      case 'TASK':
        defaultTitleName = intl.formatMessage({ id: 'yqc.mobile.home.my.task', defaultMessage: '我的任务' });
        url = `itsm/v1/${appStore.tenantId}/sc_req_items/mobile/requestAndTask?role=${'assign'}&userId=${appStore.self.person?.id}`;
        // url = `itsm/v1/${appStore.tenantId}/work_bench/all_my_tasks`;
        break;
      default:
        break;
    }
    return { defaultTitleName, url };
  };

  const { defaultTitleName, url } = getMessages(identify);
  const handleItemClick = (item) => {
    switch (identify) {
      case 'REQUEST':
      case 'TASK':
        _.navigateTo({
          url: `/pages/task-detail/index?ticketId=${item.sourceId || item.id}&viewCode=${_.getTicketViewMapping(item.sourceTable || item.tableName, item.number)}`,
        });
        break;
      case 'SURVEY':
        _.navigateTo({
          url: `/pages/survey-detail/index?instanceId=${item.id}`,
        });
        break;
      default:
        break;
    }
  };

  const handleSelectChange = (checked) => {
    if (selectStore.selectedList.length === 0) {
      selectStore.firstSelectedItem = storeRef.current?.data?.[0];
    }
    setSelectAll(checked);
  };

  function validate() {
    const { curAction, formFieldValues, restFields } = selectStore;
    if (!curAction.id) {
      Taro.atMessage({
        type: 'warning',
        message: intl.formatMessage({ id: 'yqc.mobile.select.your.actions', defaultMessage: '请选择要执行的操作' }),
      });
      return false;
    }
    const requiredList: Array<any> = [];
    const showField = _.isJSON(curAction.windowShowField) ? JSON.parse(curAction.windowShowField) : [];
    if (showField.length > 0) {
      showField.forEach(field => {
        if (field.required && ['', undefined, null].includes(formFieldValues[field.field])) {
          const { name } = restFields.find(item => item.code === field.field) || {}; // 一般情况下一定会有
          requiredList.push(name);
        }
      });
    }
    if (requiredList.length > 0) {
      Taro.atMessage({
        type: 'warning',
        message: `${intl.formatMessage({ id: 'yqc.mobile.please.input', defaultMessage: '请输入' })}${requiredList.join(' , ')}`,
      });
      return false;
    }
    return true;
  }
  const handleRestFieldChange = (value, name) => {
    selectStore.formFieldValues[name] = value;
  };
  // 批量操作的提交事件
  const handleConfirm = async () => {
    // 先校验是否有必填项没填
    if (!validate()) return;
    const { curAction, formFieldValues } = selectStore;
    const _params: Array<object> = [];
    // 一定有数据，否则不会进来
    Object.keys(formFieldValues).forEach(key => {
      if (typeof formFieldValues[key] === 'object' && !Array.isArray(formFieldValues[key])) {
        formFieldValues[key] = formFieldValues[key].id || formFieldValues[key].code;
      }
    });
    selectStore.selectedList.forEach((selected: any) => {
      const { number: code, id, object_version_number } = selected.ticketObjectsList || { ...selected, object_version_number: selected.objectVersionNumber };
      const param = { code, id, object_version_number, _action_id: curAction.id, _status: 'update' };
      Object.assign(param, formFieldValues);
      _params.push(param);
    });
    await selectStore.submit(appStore.tenantId, selectStore.businessObjectId, selectStore.selectedList[0].viewId, _params);
    const all = selectStore.selectedList.length;
    const success = selectStore.selectedList.length - selectStore.failedTickets.length;
    const failed = selectStore.failedTickets.length;
    handleClear();
    selectStore.numbers.push(...[all, success, failed]);
    selectStore.firstSelectedItem = {};
    selectStore.selectedList = [];
    setSelectAll(false);
    setOpen(false);
    setOpenFeedback(true);
    setSelectAll(false);
    // refresh(_.uuid());
  };
  // 清除批量操作内部存储的数据
  function handleClear() {
    selectStore.curAction = {};
    selectStore.restFields = [];
    selectStore.formFieldValues = {};
    selectStore.failedTickets = [];
    selectStore.numbers = [];
  }
  // 取消批量操作
  const handleCancel = () => {
    setOpen(false);
    handleClear();
  };

  // 选择某一个操作的变更事件
  const handleActionsChange = (value) => {
    Object.assign(selectStore.curAction, value);
    const showField = _.isJSON(value.windowShowField) ? JSON.parse(value.windowShowField) : [];
    const restFields: Array<any> = [];
    if (selectStore.businessObject.length > 0) {
      selectStore.businessObject.forEach((obj: any) => {
        showField.forEach(field => {
          if (field.field === obj.code) {
            Object.assign(obj, { required: field.required });
            restFields.push(obj);
          }
        });
      });
    }
    selectStore.restFields = restFields;
    selectStore.formFieldValues = {};
  };

  const selectFilter = useCallback((value) => {
    if (!selectStore.firstSelectedItem?.id) return true;
    const sameTableName = selectStore.firstSelectedItem.businessObjectId === value.businessObjectId;
    const sameState = selectStore.firstSelectedItem.stateId === value.stateId;
    return sameTableName && sameState;
  }, [selectStore.firstSelectedItem]);

  return (
    <Page className="new-event-list">
      <YqNavBar onLeftClick={appStore.hiddenFooter ? () => {} : handleListBack} title={titleName || defaultTitleName} />
      {appStore.self.person?.id && appStore.tenantId && (
        <YqList
          fixFilterFlag
          selectStore={selectStore}
          storeRef={s => { storeRef.current = s; }}
          selectFilter={selectFilter}
          refreshKey={refreshKey}
          showCheckbox={identify !== 'SURVEY' && !!params?.enableBatch}
          selectAll={selectAll}
          setSelectAll={setSelectAll}
          selectedList={selectStore.selectedList}
          setSelectedList={selectStore.setSelectedList}
          failedList={selectStore.failedTickets}
          handleTabClick={v => {
            if (v.toString() === '0') {
              selectStore.fetchBubbles(appStore.tenantId, appStore.self.personId, identify, '&search_activeFlag=false');
            }
            if (v.toString() === '1') {
              selectStore.fetchBubbles(appStore.tenantId, appStore.self.personId, identify, '&search_activeFlag=true');
            }
            if (v.toString() === '2') {
              selectStore.fetchBubbles(appStore.tenantId, appStore.self.personId, identify, '');
            }
          }}
          // renderer={NewListItem}
          filterData={identify === 'SURVEY' ? surveyFilters : tableFilters}
          customFilters={identify !== 'SURVEY' && customFilters as any}
          params={{ userId: appStore.self.person?.id }}
          customSurvey={identify === 'SURVEY'}
          searchFieldName="condition"
          hasPrefixSearchName={false}
          filterBubbles={identify !== 'SURVEY' ? [
            { name: intl.formatMessage({ id: 'yqc.mobile.status', defaultMessage: '状态' }), code: 'search_state', options: selectStore.statusList },
            { name: intl.formatMessage({ id: 'yqc.mobile.priority', defaultMessage: '优先级' }), code: 'search_priority', options: selectStore.priorityList },
          ] : []}
          url={url}
          fields={fields}
          fixedFilter
          canSearch
          identify={identify}
          setListStoreRef={setListStoreRef}
          onClick={handleItemClick}
          renderer={undefined}
          fixedFilterBubbles={undefined}
        />
      )}
      {identify !== 'SURVEY' && params?.enableBatch && (
        <View className="list-preview-buttons action-operation">
          <Checkbox
            className="list-preview-buttons-left"
            checked={selectAll}
            onChange={handleSelectChange}
          >
            {intl.formatMessage({ id: 'yqc.mobile.select.all', defaultMessage: '全选' })}
          </Checkbox>
          {selectStore.loading && <Loading style={{ width: '100%' }} />}
          {!selectStore.loading && <BatchAction
            mode="inline"
            open={open}
            setOpen={setOpen}
            handleCancel={handleCancel}
            handleConfirm={handleConfirm}
            selectStore={selectStore}
            handleActionsChange={handleActionsChange}
            handleRestFieldChange={handleRestFieldChange}
            openFeedback={openFeedback}
            setOpenFeedback={setOpenFeedback}
          />}
        </View>
      )}
    </Page>
  );
}

export default injectIntl(inject('appStore')(observer(NewEventList)));
