/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState } from 'react';
import { View } from '@tarojs/components';
import { Loading } from '@taroify/core';
import classnames from 'classnames';
import Taro from '@tarojs/taro';
import YqPreview from '@/components/yq-preview';
import Modal from '@/components/yq-modal';
import { _, download, constants } from '@/util';
import YqIcon from '@/components/yq-icon';
import { useIntl } from '@/util/intl';
import RcViewer from '@/components/rc-viewer';
import appStore from '@/store/app';
import styles from './Accessory.module.less';
import { site } from '../../../config';

interface YqAttachmentItemProps {
  file: any;
}
const AccessoryItem: React.FC<YqAttachmentItemProps> = function YQAttachmentItem({
  file,
}) {
  const intl = useIntl();
  const nameAndExtension = file.fileName.split('.');
  const fileKey = file.fileKey;
  const extension = '.'.concat(nameAndExtension.pop());
  const [downLoading, setDownloading] = useState(false);
  let isImage = false;
  if (fileKey && (fileKey.toLowerCase().includes('.jpeg') || fileKey.toLowerCase().includes('.jpg') || fileKey.toLowerCase().includes('.svg') || fileKey.toLowerCase().includes('.png') || fileKey.toLowerCase().includes('.gif'))) {
    isImage = true;
  }
  const fileUrl = `${site}hfle/yqc/v1/0/files/download-by-key?fileKey=${encodeURIComponent(fileKey)}&access_token=${appStore.accessToken}`;
  const handleDownload = async (): Promise<void> => {
    if (file.fileKey) {
      if ((appStore.isWxwork || appStore.isLark || appStore.isDingtalk || appStore.isWechat) && !_.isPc()) {
        _.navigateTo({
          url: `/packageOther/pages/confirm-download/index?fileKey=${file.fileKey}`,
        });
      } else {
        setDownloading(true);
        try {
          await download(fileKey, fileKey?.split('@')[1]);
        } catch (error) {
          // eslint-disable-next-line no-console
          console.error('error -- ', error);
        } finally {
          setDownloading(false);
        }
      }
    } else {
      Taro.atMessage({
        type: 'error',
        message: intl.formatMessage({ id: 'yqc.mobile.cannot.download', defaultMessage: '该文件暂时无法下载' }),
      });
    }
  };

  const uploadMap = {};
  const handlePreview = () => {
    if (!file.fileKey) return;
    if (isImage) return;
    const modalProps = {
      popupProps: {
        className: styles.previewPop,
      },
      wrapperClassName: 'yq-preview-wrapper',
      children: (
        <>
          <View className={styles.previewHeader}>
            {intl.formatMessage({ id: 'yqc.mobile.show.form', defaultMessage: '预览' })}
          </View>
          <View className={styles.previewContent}>
            <YqPreview blobUrl={uploadMap[fileKey]} fileKey={fileKey} />
          </View>
        </>
      ),
    };
    if (process.env.TARO_ENV === 'weapp') {
      _.previewFile(fileKey);
    } else {
      Modal.open(modalProps);
    }
  };

  const renderIcon = (iconSize: number = 64) => {
    const other = { color: '#6B7AD2', iconType: 'notes' };
    const { color, iconType } = constants.ICON_LIST.find(icon => icon.type === extension) || other;
    return <YqIcon type={iconType} theme="filled" size={iconSize} fill={color} />;
  };

  const renderName = () => {
    return (
      <View className={styles.name}>
        {file.fileName}
      </View>
    );
  };

  return (
    <View className={classnames(styles.attachmentItem)}>
      <View
        className={classnames(styles.attachmentItemRow)}
        onClick={handlePreview}
      >
        {isImage ? (
          <View className={styles.preview}>
            <RcViewer className={styles.previewViewer}>
              <img alt="" className={styles.previewImg} key={new Date().getTime()} src={fileKey?.startsWith('http') ? fileKey : fileUrl} />
            </RcViewer>
          </View>
        ) : null}
        <View className={styles.attachmentItemRowIcon}>{
          renderIcon(64)
        }</View>
        <View className={styles.attachmentItemRowText}>
          <View className={styles.attachmentItemRowTextName}>
            {renderName()}
          </View>
          <View className={styles.attachmentItemRowSmallTextName}>
            {file.fileSize ? (file.fileSize / 1024).toFixed(1) : '--'} KB
            {intl.formatMessage({ id: 'yqc.mobile.attachments.from', defaultMessage: '来自' })} {file.createdByName} {file.source}
          </View>
          <View className={styles.attachmentItemRowSmallTextName}>
            {file.creationDate.slice(0, -3)}
          </View>
        </View>

      </View>
      <View className={styles.attachmentItemButton} onClick={handleDownload} style={{ marginRight: '15px' }}>
        {downLoading ? <Loading size={20} /> : (
          <YqIcon type="icon-download" fill="#12274d" size={40} onClick={handleDownload} />
        )}
      </View>
    </View>
  );
};

export default AccessoryItem;
