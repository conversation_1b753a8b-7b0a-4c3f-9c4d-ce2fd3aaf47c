:global {
  .yq-preview-wrapper {
    z-index: 1001;
  }
}
.attachment {

  .upload {
    width: 216px;
    height: 64px !important;
    font-size: 28px;
    color: #2979ff;
    border-radius: 12px;
    flex: unset;
  }
}

.attachmentItem {
  height: 170px;
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  &.row {
    height: 64px;
  }
  .attachmentItemRow {
    height: 120px;
    width: 100%;
    overflow: hidden;
    //flex-grow: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: left;
    padding-left: 20px;
    position: relative;

    .preview {
      width: 100%;
      height: 100%;
      position: absolute;
    }
    .previewViewer {
      width: 100%;
      height: 100%;
      position: absolute;
      opacity: 0;
      z-index: 1;
    }
    .previewImg {
      width: 100%;
      height: 100%;
    }
    .attachmentItemRowIcon {
      width: 64px;
      height: 64px;
      margin-right: 12px;
    }
    .attachmentItemRowText {
      width: 0;
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #12274d;
      flex-grow: 1;
      margin-left: 10px;
      display: flex;
      flex-direction: column;
      .attachmentItemRowTextName {
        flex-grow: 1;
        word-break: keep-all;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .attachmentItemRowSmallTextName {
        flex-grow: 1;
        word-break: keep-all;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 28px;
        opacity: 0.65;
      }
      .attachmentItemRowTextSize {
        flex-shrink: 0;
        margin-left: 12px;
        color: rgba(18, 39, 77, 0.45);
      }
    }
  }
  .attachmentItemButton {
    flex-shrink: 0;
    margin-right: 5px;
    position: relative;
    display: flex;
    :global {
      .taroify-loading,
      .yq-mb-icon {
        margin-left: 30px;
      }
      .taroify-loading {
        color: #2979ff;
      }
    }
  }
  .attachmentItemLine {
    width: 100%;
    height: 10px;
    border-radius: 8px;

  }
  .progressCircleText {

    width: 100%;
    height: 100%;
    line-height: 75px;
    text-align: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: rgba(18, 39, 77, 0.45);
    font-size: 12px;
  }
}

.previewPop {
  min-height: 80%;
  height: auto;
  display: flex;
  flex-direction: column;
  z-index: 1020 !important;
  .previewHeader {
    text-align: center;
    line-height: 60px;
    padding: 10px 0;
    border-bottom: 2px solid #eee;
  }
  .previewContent {
    flex-grow: 1;
    display: flex;
    align-items: center;
    background-color: #fff !important;
  }
}
.attachmentItemRowIcon {
  :global {
    .yq-mb-icon-park {
      display: flex;
    }
  }
}
.name {
  word-break: keep-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  font-size: 30px;
  line-height: 42px;
  color: #12274d;
}
