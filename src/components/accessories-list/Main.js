import React, { useEffect, useState, useMemo, useRef } from 'react';
import { observer, useLocalObservable } from 'mobx-react-lite';
import { Empty, Loading, Search } from '@taroify/core';
import { injectIntl, useIntl } from '@/util/intl';
import appStore from '@/store/app';
import { _, fetch } from '@/util';
import AccessoryItem from '@/components/accessories-list/AccessoryItem';
import YqEmpty from '@/components/yq-empty';
import YqComponentTitleFrame from '@/components/yq-component-title-frame';
import './index.scss';

function AccessoriesList(props) {
  const { ticketId, viewId, businessObjectCode, pageStore } = props;
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState(false);
  const [locationTabIndex, setLocationTabIndex] = useState(undefined); // 本组件所在的tab的index
  const fieldId = props.field.id; // 本组件的field id
  const intl = useIntl();

  useEffect(() => {
    initFiles();
    setLocationTabIndex(traversalFindParent(pageStore?.jsonData?.sections));
  }, []);

  // 确定本组件所在的标签页，返回值是标签页的index。参数1：遍历对象 参数2：传递数值，用于返回值 参数3：目标field的id
  const traversalFindParent = (sectionArray, data = undefined, targetId = fieldId) => {
    let res;
    let correctData;
    let targetIndex;
    sectionArray.forEach((item, index) => {
      if (item.tag === 'Tab') {
        targetIndex = index;
      }
      if (item.fields && item.fields.length > 0) {
        res = traversalFindParent(item.fields, targetIndex || data);
      }
      if (item.children && item.children.length > 0) {
        res = traversalFindParent(item.children, targetIndex || data);
      }
      if (item.id === targetId) {
        correctData = data;
        return null;
      }
      if (!item.children && !item.fields) {
        return null;
      }
      correctData = res || correctData;
    });
    return correctData;
  };

  useEffect(() => {
    if (!locationTabIndex || locationTabIndex === pageStore.tabCurrent) {
      initFiles(); // 切换tab时，或者获取不到tab时，重新加载附件清单
    }
  }, [pageStore.tabCurrent]);

  const initFiles = async () => {
    setLoading(true);
    const url = `lc/v1/${appStore.tenantId}/views/queryAttachmentsByTicketId/${viewId}?businessObjectCode=${businessObjectCode}&replyDisplay=true&ticketId=${ticketId}`;
    const res = await fetch(url).finally(() => setLoading(false));
    if (res.length) {
      setFiles(res);
    }
    if (res.length > 4) {
      setSearch(true);
    }
  };
  const renderItems = () => {
    if (files.length) {
      return (
        <>
          {
            files.map((item, index) => {
              return (
                <AccessoryItem file={item} />
              );
            })
          }
        </>
      );
    } else if (loading) {
      return (
        <Loading size={20} />
      );
    } else {
      return (
        <YqEmpty description={intl.formatMessage({ id: 'yqc.mobile.no.attachments.detected', defaultMessage: '没有检查到附件' })} />
      );
    }
  };

  const handleSearch = async (e) => {
    const url = `lc/v1/${appStore.tenantId}/views/queryAttachmentsByTicketId/${viewId}?businessObjectCode=${businessObjectCode}&replyDisplay=true&ticketId=${ticketId}&param=${e.detail.value}`;
    const res = await fetch(url, {}, 'get').catch();
    setFiles(res);
  };

  const handleDownloadAll = async () => {
    const request = [];
    files.forEach((items) => {
      request.push(items.fileKey);
    });
    const url = `hfle/yqc/v1/${appStore.tenantId}/files/batch-download/file-keys`;
    const result = await fetch(url, request, 'post').catch((err) => console.error(err));
  };

  return (
    <>
      <YqComponentTitleFrame header={intl.formatMessage({ id: 'yqc.mobile.accessories.list', defaultMessage: '附件清单' })} />
      {search ? <Search onSearch={handleSearch} onClear={initFiles} /> : null}
      {renderItems()}
    </>

  );
}
export default injectIntl(observer(AccessoriesList));
