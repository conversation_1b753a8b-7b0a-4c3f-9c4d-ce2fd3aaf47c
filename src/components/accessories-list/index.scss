@import "../../style/setting.scss";
@import "../../style/setting.scss";

.asset-rl {
  padding: 12px 24px;
  background: white;


  &-box {
    border-radius: 8px;
    border: 2px solid $border;
  }

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: $background4;
    color: $black;
    font-size: 32px;
    font-weight: 500;
    border-radius: 8px 8px 0 0;
    line-height: 48px;

    .i-icon,
    img {
      width: 40px;
      height: 40px;
    }
  }

  &-title {
    max-width: 80%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .accessoriesHeader {
    padding:10px;
    font-size:0.8rem;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #12274D;
    line-height: 1.6rem;
  }
  &-content {
    padding: 16px 0;
    font-weight: 400;
    font-size: 28px;

    .mb-row {
      padding: 8px 24px;
      min-height: 44px;
      line-height: 44px;
    }

    .mb-row-label {
      color: $black1;
    }

    .mb-row-text {
      color: $black;
    }
  }
}
