import useSWR from 'swr';
import queryString from 'query-string';
import appStore from '@/store/app';
import { fetch } from '@/util';

function useRelationTickets({
  relationFlag,
  businessObjectCode,
  ticketId,
  fieldCode,
  relationObjectCode,
}) {
  const tenantId = appStore.tenantId;

  const queryFlag = relationFlag && businessObjectCode && ticketId && fieldCode && relationObjectCode;

  const query = { ticketRelationObjectCode: relationObjectCode, ticketRelationFieldCode: 'id' };

  const url = queryFlag ? `itsm/v1/${tenantId}/relation/ticket/${businessObjectCode}/${ticketId}/${fieldCode}/relation?${queryString.stringify(query)}` : '';

  return useSWR(url, fetch, { revalidateIfStale: true, revalidateOnFocus: false });
}

export { useRelationTickets };
