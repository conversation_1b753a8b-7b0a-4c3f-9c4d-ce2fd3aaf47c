import React from 'react';
import { View } from '@tarojs/components';
import classNames from 'classnames';
import YqAvatar from '@/components/yq-avatar';
import styles from './PersonTag.module.less';

interface PersonTagProps {
  name: string;
  personId?: string;
  mode?: 'simple';
}

const PersonTag: React.FC<PersonTagProps> = function PersonTag({ name, personId, mode }) {
  if (!name) {
    return null;
  }
  return (
    <View className={mode === 'simple' ? styles.simpleWrapper : styles.wrapper}>
      <YqAvatar personId={personId} classNames={styles.avatar}>{name}</YqAvatar>
      <View className={classNames(styles.name, 'taroify-ellipsis--l2')}>{name}</View>
    </View>
  );
};

export default PersonTag;
