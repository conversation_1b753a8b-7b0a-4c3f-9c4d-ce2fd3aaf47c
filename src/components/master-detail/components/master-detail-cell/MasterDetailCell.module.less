.cellWrapper {
  position: relative;
  margin: 0 -24px;
  padding: 0 24px;
  border-bottom: 1px solid #ededed;

  &.checked {
    padding-right: 74px;
    background-color: #f3f4f5;
  }

  &.showCheckbox {
    padding-left: 100px;
  }

  .checkIcon {
    position: absolute;
    right: 24px;
    top: 50%;
    transform: translateY(-50%);
  }
}

.checkbox {
  position: absolute;
  top: 30px;
  left: 24px;
}

.cell {
  padding: 24px 0 !important;
  min-height: 100px !important;

  .brief {
    margin-left: 0 !important;
  }

  .top {
    font-weight: 400 !important;

    &.nameChecked {
      color: #2979ff !important;
    }
  
    &.onlyName {
      margin-bottom: 0 !important;
    }
  }
}

.avatar {
  margin-right: 24px;
}

.output {
  margin: 5px 0;
  line-height: 36px;

  .outputTitle {
    width: unset;
    max-width: 200px;
    margin-right: 18px;
    flex-shrink: 0;
    word-break: break-word;
    white-space: pre-wrap;
    color: rgba(18, 39, 77, 0.65);
    opacity: 1;
  }

  .outputContent {
    word-break: break-word;
    white-space: pre-wrap;
    color: rgba(18, 39, 77, 0.65);
  }
}