import React, { useCallback } from 'react';
import classNames from 'classnames';
import { observer } from 'mobx-react-lite';
import { View, ITouchEvent } from '@tarojs/components';
import YqCell from '@/components/yq-cell';
import YqOutput from '@/components/yq-output';
import YqAvatar from '@/components/yq-avatar';
import YqIcon from '@/components/yq-icon';
import YqCheckboxWithIcon from '@/components/yq-checkbox-with-icon';
import { DisplayField } from '@/types/LovConfig';
import { MasterDetailCellProps } from '../../MasterDetail';
import styles from './MasterDetailCell.module.less';

const MasterDetailCell: React.FC<MasterDetailCellProps> = function MasterDetailCell(props) {
  const { record, nameField, displayFields, avatar, checked, showCheckbox, handleNodeClick, disabledParent } = props;

  const briefFlag = Array.isArray(displayFields) && displayFields.length > 0;

  const renderField = useCallback((field: DisplayField | string) => {
    if (typeof field === 'string') return record[field];

    return record[field.code] ? (
      <YqOutput
        key={field.code}
        className={styles.output}
        label={field.label}
        titleClassName={styles.outputTitle}
        contentClassName={styles.outputContent}
      >
        {record[field.code]}
      </YqOutput>
    ) : null;
  }, [record]);

  const renderAvatar = useCallback(() => {
    if (!avatar) return undefined;
    return <YqAvatar classNames={styles.avatar}>{record[nameField]}</YqAvatar>;
  }, [avatar, nameField, record]);

  const handleClick = useCallback((e: ITouchEvent) => {
    if (!disabledParent && !record.isLeaf) {
      e.stopPropagation();
      e.preventDefault();
      handleNodeClick(record);
    }
  }, [handleNodeClick, record, disabledParent]);

  return (
    <View className={classNames(styles.cellWrapper, { [styles.checked]: checked, [styles.showCheckbox]: showCheckbox })}>
      {showCheckbox && <YqCheckboxWithIcon className={styles.checkbox} checked={checked} onClick={handleClick} disabled={disabledParent && !record.isLeaf} />}
      <YqCell
        title={record[nameField]}
        className={styles.cell}
        topClassName={classNames(styles.top, { [styles.nameChecked]: checked, [styles.onlyName]: !briefFlag })}
        briefClassName={styles.brief}
        icon={renderAvatar()}
      >
        {briefFlag && displayFields?.map(renderField)}
      </YqCell>
      {showCheckbox && record?.isLeaf === false && <YqIcon className={styles.checkIcon} type="right" size={40} fill="#12274d" opacity={0.45} />}
      {checked && !showCheckbox && <YqIcon className={styles.checkIcon} type="check" size={40} fill="#2979ff" />}
    </View>
  );
};

export default observer(MasterDetailCell);
