import { ReactElement } from 'react';
import { DisplayField, LovFetchConfig } from '@/types/LovConfig';
import { CommonFieldProps } from '@/types/CommonField';

interface MasterDetailWidgetConfig {
  relationLovBusinessObjectId?: string; // 和field里的relationLovId应该是一个东西
  relationLovNameFieldCode?: string; // 名称字段
  relationLovValueFieldCode?: string; // 值字段
  mobileLovNameFieldCode: string; // 移动端专用的字段，如果展示多个字段则以逗号的形式分割
  mobileRelationTicketFlag?: boolean; // 是否关联单据
  mobileRelationTicketViewId?: string; // 关联单据的点击跳转视图
  variableFilter?: any[]; // 筛选变量
  condition?: any[]; // 过滤条件
  onlyLeafFlag?: boolean; // 是否禁用父级
}

interface MasterDetailField {
  name: string;
  id: string;
  code: string;
  error?: string;
  relationObjectCode?: string; // 关联对象code
  relationObjectId: string; // 关联对象id
  relationObjectName: string; // 关联对象名称
  relationLovName?: string; // 关联对象的引用值列表名称
  relationLovId?: string; // 关联对象的引用值列表id
  placeHolder?: string;
  widgetConfig: MasterDetailWidgetConfig;
  widgetType: string;
}

export interface MasterDetailProps extends CommonFieldProps {
  lovCode?: string;
  lovPara?: Record<string, any>;
  lovId?: string;
  customParams?: Record<string, any>;
  renderer?: ReactElement;
  avatar?: boolean;
  field?: MasterDetailField;
  value: Record<string, any> | Record<string, any>[]; // 多选时传入数组
  config?: LovFetchConfig;
  multiple?: boolean;
}

export interface MasterDetailCellProps {
  record: Record<string, any>;
  nameField: string;
  displayFields?: string[] | DisplayField[];
  showCheckbox: boolean;
  handleNodeClick(reocrd: Record<string, any>): void
  avatar?: boolean;
  checked: boolean;
  disabledParent: boolean;
}
