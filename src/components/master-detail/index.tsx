import React, { useCallback, useEffect, useMemo } from 'react';
import { observer, useLocalObservable } from 'mobx-react-lite';
import { runInAction } from 'mobx';
import { View } from '@tarojs/components';
import { useIntl } from '@/util/intl';
import MbRow from '@/components/mb-row';
import YqIcon from '@/components/yq-icon';
import Modal from '@/components/yq-modal';
import useLovConfig from '@/hooks/use-lov-config';
import { _ } from '@/util';
import MasterDetailCell from './components/master-detail-cell';
import PersonTag from './components/person-tag/PersonTag';
import { useRelationTickets } from './api';
import { MasterDetailProps } from './MasterDetail';
import MultipleSelect from '../multiple-select';

// 多对一关联
const MasterDetail: React.FC<MasterDetailProps> = function MasterDetail(props) {
  const {
    title,
    disabled,
    required,
    cardMode,
    field,
    placeholder,
    values,
    value,
    avatar,
    config: _config,
    lovCode,
    lovPara,
    lovId,
    customParams,
    businessObjectCode,
    ticketId,
    renderer: _renderer,
    onChange,
    multiple, // 支持多选
  } = props;

  const avatarFlag = field?.relationObjectCode === 'IAM_USER' || avatar;
  const relationFlag = field?.widgetConfig.mobileRelationTicketFlag;
  const disabledParent = !!field?.widgetConfig?.onlyLeafFlag; // 是否禁用父级
  const relationViewId = field?.widgetConfig.mobileRelationTicketViewId;

  const intl = useIntl();

  const [config, setConfig] = useLovConfig(field?.relationLovId || lovId, lovCode, lovPara);

  const { data: relations } = useRelationTickets({
    relationFlag,
    relationObjectCode: field?.relationObjectCode,
    businessObjectCode,
    ticketId,
    fieldCode: field?.code,
  });

  const params = useMemo(() => {
    const { variableFilter } = field?.widgetConfig || {};
    const variableParams = {};
    // 筛选变量
    if (Array.isArray(variableFilter) && variableFilter.length > 0) {
      variableFilter.forEach(({ variable, relatedFieldCode }) => {
        variableParams[variable] = values?.[relatedFieldCode]?.id || values?.[relatedFieldCode];
      });
      Object.keys(variableParams).forEach((key) => {
        if (typeof variableParams[key] === 'object' && variableParams[key] !== null && !key.endsWith(':_variable')) variableParams[key] = undefined;
      });
    }
    const isPostMethod = config?.method.toLocaleLowerCase() === 'post';
    const configParams = { ...(config?.params || {}), ...(customParams || {}) };
    const lovParams = isPostMethod ? {
      conditions: field?.widgetConfig?.condition || [],
      params: {
        __page_params: _.transformFormValue(variableParams || {}, { removeObject: true }),
        ..._.transformFormValue(values || {}, { removeObject: true }),
      },
      ...configParams,
    } : configParams;
    // 是否有父级字段
    if (config?.parentFieldCode) {
      if (isPostMethod) lovParams.params[config.parentFieldCode] = '0';
      else lovParams[config.parentFieldCode] = '0';
    }
    return lovParams;
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [field, JSON.stringify(values), config, customParams]);

  const store = useLocalObservable(() => ({
    url: '',
    params: {} as Record<string, any>,
    method: 'POST',
    showCheckbox: false,
    selectRecords: [] as Record<string, any>[], // 由于要支持多选，记录类型就默认为数组了
    breadcrumbs: [{ value: 'All', name: intl.formatMessage({ id: 'yqc.mobile.all', defaultMessage: '全部' }) }],
    breadcrumbsInit: () => {
      store.breadcrumbs = [{ value: 'All', name: intl.formatMessage({ id: 'yqc.mobile.all', defaultMessage: '全部' }) }];
    },
  }));

  useEffect(() => {
    if (_config && typeof _config === 'object') {
      setConfig(_config);
    }
  }, [_config, setConfig]);

  useEffect(() => {
    if (config?.url) {
      runInAction(() => {
        store.url = config.url;
        store.method = config.method;
      });
    }
  }, [config, store]);

  useEffect(() => {
    if (params) {
      runInAction(() => {
        store.params = params;
      });
    }
  }, [params, store]);

  useEffect(() => {
    runInAction(() => {
      // 初始化值
      if (value && typeof value === 'object') {
        store.selectRecords = Array.isArray(value) ? value : [value];
      } else {
        store.selectRecords = [];
      }
    });
  }, [value, store]);

  const handleSetParent = useCallback((parentQuery: string, forceUpdate?: boolean) => {
    const isPostMethod = config?.method.toLocaleLowerCase() === 'post';
    if (isPostMethod) {
      const flag = store.params?.params && typeof store.params.params === 'object';
      if (config?.parentFieldCode && flag && (config.parentFieldCode in store.params.params || forceUpdate)) {
        if (parentQuery === 'Delete' && config.parentFieldCode in store.params.params) {
          delete store.params.params[config.parentFieldCode];
        } else {
          store.params.params[config.parentFieldCode] = parentQuery !== 'All' ? parentQuery : '0';
        }
      }
    } else if (config?.parentFieldCode && (config.parentFieldCode in store.params || forceUpdate)) {
      if (parentQuery === 'Delete' && config.parentFieldCode in store.params) {
        delete store.params[config.parentFieldCode];
      } else {
        store.params[config.parentFieldCode] = parentQuery !== 'All' ? parentQuery : '0';
      }
    }
    // 当有筛选变量的时候 也应该去掉父级字段
    if (store.params?.conditions?.length > 0) {
      if (config?.parentFieldCode) {
        delete store.params?.params?.[config.parentFieldCode];
      }
    }
    store.params = { ...store.params }; // 触发更新
  }, [config, store]);

  const handleNodeClick = useCallback((record: Record<string, any>) => {
    runInAction(() => {
      if (multiple) {
        // 多选时，判断是否已经选择
        const checked = !!store.selectRecords.find(r => r[config?.idField || ''] === record[config?.idField || '']);
        if (checked) {
          store.selectRecords = store.selectRecords.filter(r => r[config?.idField || ''] !== record[config?.idField || '']);
        } else {
          store.selectRecords = [...store.selectRecords, { ...record, id: record[config?.idField || ''] }];
        }
      } else {
        store.selectRecords = [{ ...record, id: record[config?.idField || ''] }];
      }
    });
  }, [store, config]);

  const handleConfirm = useCallback(async (__: Record<string, any>, modal: any) => {
    // 单选模式下，对外返回单个对象
    onChange(multiple ? store.selectRecords : store.selectRecords[0]);
    modal?.close();
  }, [onChange, store]);

  // 最终返回false, 则多选组件内部不会处理选中数据
  const handlePopupRowClick = useCallback((record: Record<string, any>, modal) => {
    runInAction(() => {
      if (config?.parentFieldCode && record.isLeaf === false) { // 有父级字段，且不是子节点，就有下层
        // 面包屑是根据值列表配置的idField还是id ？
        store.breadcrumbs.push({ value: record[config?.idField || ''], name: record[config?.nameField || ''] });
        handleSetParent(record[config?.idField || '']);
      } else { // 否则才会选中当前的，如果是需要选中有父级字段的父级，则需要点击cell组件里的checkbox
        if (multiple) {
          // 多选时，判断是否已经选择
          const checked = !!store.selectRecords.find(r => r[config?.idField || ''] === record[config?.idField || '']);
          if (checked) {
            store.selectRecords = store.selectRecords.filter(r => r[config?.idField || ''] !== record[config?.idField || '']);
          } else {
            store.selectRecords = [...store.selectRecords, { ...record, id: record[config?.idField || ''] }];
          }
        } else {
          store.selectRecords = [{ ...record, id: record[config?.idField || ''] }];
          handleConfirm(record, modal);
        }
      }
    });
    return false;
  }, [config, store, handleSetParent]);

  const handleBreadcrumbClick = useCallback((v: string) => {
    runInAction(() => {
      const index = store.breadcrumbs.findIndex(b => b.value === v); // 肯定可以找到，不然就是数据有问题
      const curBreadcrumb = store.breadcrumbs[index];
      store.breadcrumbs = store.breadcrumbs.slice(0, index + 1);
      handleSetParent(curBreadcrumb.value);
    });
  }, [store, handleSetParent]);

  const handleSearch = useCallback((v: string) => {
    // 如果有父级字段，有搜索值就将父级字段删除掉，否则需要回填上去
    handleSetParent(!v ? 'All' : 'Delete', !v);
    store.breadcrumbsInit();
  }, [handleSetParent, store]);

  const renderRelations = useCallback(relation => (
    <View
      key={relation.id}
      style={{ color: '#2979ff', display: 'inline-block', width: 'auto' }}
      onClick={(e) => {
        e.stopPropagation();
        if (relationViewId) _.navigateTo({ url: `/pages/task-detail/index?ticketId=${relation.id}&viewId=${relationViewId}` });
      }}
    >
      {relation?.number}
    </View>
  ), [relationViewId]);

  const renderValue = useCallback(() => {
    const nameField = config?.nameField || '';
    const index = nameField.indexOf(':');
    const displayField = index === -1 ? nameField : nameField.substring(index + 1);
    // 外部传入：多选数组，单选对象
    const displayValue = multiple
      ? value && value?.map(v => v?.[displayField] || v?.[nameField] || v?.name || v?.real_nam)?.join(',')
      : value?.[displayField] || value?.[nameField] || value?.name || value?.real_name; // name 和 real_name 先特殊处理一下
    if (relationFlag) {
      // 展示关联单据的多对一之前就是这个逻辑，展示就是写死的number，ticket就是id，先这样写
      if (Array.isArray(relations)) return relations.map(renderRelations);
      else return value?.id && value?.number ? renderRelations(value) : null;
    }
    if (avatarFlag && displayValue) {
      return <PersonTag name={displayValue} personId={value?.[config?.idField || ''] || value?.id || value?.user_id} />;
    }
    return displayValue;
  }, [relationFlag, relations, config, value, avatarFlag, renderRelations]);

  const renderCell = useCallback((record: Record<string, any>, nameField: string) => (
    <MasterDetailCell
      disabledParent={disabledParent}
      avatar={avatarFlag}
      record={record}
      nameField={nameField}
      displayFields={config?.displayFields}
      showCheckbox={!!config?.parentFieldCode || !!multiple}
      handleNodeClick={handleNodeClick}
      checked={!!store.selectRecords.find(r => r[config?.idField || ''] === record[config?.idField || ''])}
    />
  ), [avatarFlag, config, store, handleNodeClick]);

  const renderer = useCallback((record: Record<string, any>, nameField: string) => {
    if (React.isValidElement(_renderer)) {
      const checked = !!store.selectRecords.find(r => r[config?.idField || ''] === record[config?.idField || '']);
      const rendererProps = { record, checked, onRowClick: handlePopupRowClick };
      return React.cloneElement(_renderer, rendererProps);
    } else {
      return renderCell(record, nameField);
    }
  }, [_renderer, config, handlePopupRowClick, renderCell, store]);

  const handleOpen = () => {
    store.breadcrumbsInit();
    handleSetParent('All', true);
    const modal = Modal.open({
      popupProps: { style: { height: '80vh' } },
      children: (
        <MultipleSelect
          breadcrumb={!!config?.parentFieldCode}
          search={config?.searchable}
          feFilter={!lovId && !lovCode}
          searchPrefix={config?.searchField}
          title={title}
          records={[]}
          store={store}
          confirmTips={intl.formatMessage({ id: 'yqc.mobile.confirm', defaultMessage: '确认' })}
          displayField={config?.nameField}
          valueField={config?.idField}
          renderCell={renderer}
          onRowClick={(record) => handlePopupRowClick(record, modal)}
          onBreadcrumbClick={handleBreadcrumbClick}
          onSearch={handleSearch}
          onConfirm={handleConfirm}
        />
      ),
      onClose: (callback: () => void) => {
        runInAction(() => {
          // 初始化值
          if (value && typeof value === 'object') {
            store.selectRecords = Array.isArray(value) ? value : [value];
          } else {
            store.selectRecords = [];
          }
        });
        callback();
      },
    });
  };

  return (
    <MbRow
      clearButton
      label={title}
      disabled={disabled}
      required={required}
      cardMode={cardMode}
      field={field}
      error={field?.error}
      values={values}
      onClick={handleOpen}
      value={renderValue()}
      handleClear={() => onChange('')}
      placeholder={cardMode ? '' : (placeholder || field?.placeHolder)}
      rightIcon={<YqIcon type="icon-seemore" fill="#12274d" opacity={0.45} size={28} />}
    />
  );
};

export default observer(MasterDetail);
