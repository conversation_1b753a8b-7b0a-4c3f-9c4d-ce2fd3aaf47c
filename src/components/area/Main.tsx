/* eslint-disable no-unused-vars */
import { Component } from 'react';
import { observer } from 'mobx-react';
import './index.scss';
import { Field } from '../index';

interface AreaProps {
  section: {
    fields: any []
  }
  alwayEditable?: boolean
  alwaysShow?: boolean
  values: object
  showHeader?: boolean
  onChange?: (value: any, name: any) => void
  onSelected?: (value: any, name: any) => void
}

@observer
class Area extends Component<AreaProps, any> {
  render() {
    const { section, values, alwaysShow, alwayEditable } = this.props;
    
    return (
      <>
        {section.fields.map(field => {
          return (
            <Field
              alwayEditable={alwayEditable}
              alwaysShow={alwaysShow}
              key={`field${ field.id}`}
              {...this.props}
              field={field}
              value={values[field.code]}
            />
          );
        })}
      </>
    );
  }
}

export default Area;
