import { Component } from 'react'
import { View } from '@tarojs/components'
import { AtDrawer } from 'taro-ui'
import { observer } from 'mobx-react'
import { Popup } from '@taroify/core'
import './index.scss'

@observer
class Drawer extends Component {

  static defaultProps = {
    show: false,
    mask: true,
    right: true,
    popup: false,
    onClose: () => { }
  }

  constructor() {
    super(...arguments)
    this.state = {
    }
  }

  componentDidMount() {

  }

  render() {
    const { popup, show, onClose, rounded, height } = this.props;
    const style = { height: height || '80%', overflow: 'hidden', zIndex: 9999 };
    if (rounded) {
      Object.assign(style, { borderTopRightRadius: '16px', borderTopLeftRadius: '16px' });
    }
    return (
      <>
        {popup ? (
          <Popup placement='bottom' style={style} open={show} closeable onClose={onClose}>
            {this.props.children}
          </Popup>
        ) : (
          <AtDrawer show={show} right {...this.props}>
            {this.props.children}
          </AtDrawer>
        )}
      </>
    )
  }
}

export default Drawer
