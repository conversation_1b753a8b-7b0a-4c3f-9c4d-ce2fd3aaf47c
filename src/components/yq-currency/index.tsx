import React, { useCallback, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Input } from '@taroify/core';
import { BaseEventOrig } from '@tarojs/components/types/common';
import MbRow from '@/components/mb-row';
import { YqCurrencyProps } from './YqCurrency';

// TODO: 整数位数根据配置显示
function YqCurrency(props: YqCurrencyProps): React.ReactElement {
  const { title, disabled, required, value, onChange = () => {}, field, placeHolder, values } = props;

  const placeholder = placeHolder || field?.placeHolder;

  const formatValue = useMemo(() => {
    const stringValue = value?.toString();
    if (!stringValue) return value;
    if (stringValue.includes('.')) {
      const [integer, float] = stringValue.split('.') || [];
      return `${`${integer}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') }.${ float}`;
    } else {
      return `${stringValue}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  }, [value]);

  const parser = useCallback((currency) => currency.replace(/\$\s?|(,*)/g, '').replace(/[^.\d]/g, ''), []);

  const handleChange = useCallback((e: BaseEventOrig) => {
    const currency = e.detail.value;
    let numberCurrency: string;
    if (currency?.includes('.')) {
      const [integer, float] = currency.split('.') || [];
      const currencyInteger = integer ? parser(integer) : '';
      const currencyFloat = float ? parser(float) : '';
      numberCurrency = [currencyInteger, currencyFloat].join('.');
    } else {
      numberCurrency = parser(currency);
    }
    onChange(numberCurrency);
  }, [onChange, parser]);

  return (
    <MbRow
      cardMode={props.cardMode}
      label={title}
      disabled={disabled}
      required={required}
      field={field}
      values={values}
      placeholder={props.cardMode ? '' : placeholder}
    >
      <Input placeholder={props.cardMode ? '' : placeholder} disabled={disabled} value={formatValue} onChange={(handleChange)} />
    </MbRow>
  );
}

export default observer(YqCurrency);
