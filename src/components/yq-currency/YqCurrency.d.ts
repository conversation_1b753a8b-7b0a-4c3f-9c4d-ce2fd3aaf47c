/* eslint-disable no-unused-vars */
interface WidgetConfig {
  currency: string; // 货币类型  一般为CNY
  decimalLength: number; // 小数位数
  integerLength: number; // 整数位数
}

interface Field {
  placeHolder: string;
  defaultValue: string; // 默认值
  name: string;
  code: string;
  id: string;
  widgetConfig: WidgetConfig;
  widgetType: string;
}

export interface YqCurrencyProps {
  title: string;
  required?: boolean;
  disabled?: boolean;
  value: any;
  placeHolder?: string;
  onChange: (value: any) => void;
  field: Field;
  cardMode?: boolean;
  values: any;
}
