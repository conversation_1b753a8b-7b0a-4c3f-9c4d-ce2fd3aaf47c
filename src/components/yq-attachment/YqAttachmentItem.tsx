/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { View } from '@tarojs/components';
import { Line, Circle } from 'rc-progress';
import Taro from '@tarojs/taro';
import isNumber from 'lodash/isNumber';
import mimeTypes from 'mime/lite';
import classnames from 'classnames';
import { Loading } from '@taroify/core';
import { useIntl } from '@/util/intl';
import { _, download, constants } from '@/util';
import YqIcon from '@/components/yq-icon';
import YqPreview from '@/components/yq-preview';
import Modal from '@/components/yq-modal';
import appStore from '@/store/app';
import YqCell from '@/components/yq-cell';
import styles from './YqAttachment.module.less';

interface YqAttachmentItemProps {
  file: any;
  disabled: boolean;
  disabledPreview: boolean;
  filesRef: React.MutableRefObject<any>;
  onChange(v: string): void;
  progressMode: 'line' | 'circle';
  uploadMap: object;
  uploadControllerMap: object;
  displayType: 'cell' | 'row';
  liteMode: boolean;
  hiddenDeleteBtn?: boolean;
}
const YQAttachmentItem: React.FC<YqAttachmentItemProps> = function YQAttachmentItem({
  file,
  disabled = false,
  disabledPreview,
  filesRef,
  onChange = () => {},
  progressMode,
  uploadMap,
  uploadControllerMap,
  displayType,
  liteMode,
  hiddenDeleteBtn,
}) {
  const intl = useIntl();
  const [progress, setProgress] = useState(0);
  const [downLoading, setDownloading] = useState(false);
  const [failed, setFailed] = useState({ failed: false, message: '' });
  const fromCustomerService = typeof file === 'string'; // 是否是从客服中心转工单来的
  const fileKey = fromCustomerService ? file : file?.fileKey;
  const udmTenantId = file?.udmTenantId; // 对于上下游的图片预览
  const extension = `.${(fileKey || file?.name || '.other').split('.').slice(-1)[0]}`;
  const mime = mimeTypes.getType(extension) || '';
  const isVideo = mime.includes('video');
  const isImage = mime.includes('image');
  useEffect(() => {
    if (file?.blobPath) {
      _.uploadFile(file.blobPath, file.name, undefined, ({ progress: _progress }, uploadTask) => {
        uploadControllerMap[file.blobPath] = uploadTask;
        setProgress(_progress > 99 ? 99 : _progress);
      }).then((res: any) => {
        const { data } = res;
        uploadMap[data.fileKey] = file.blobPath;
        let changedFiles = [];
        if (data.failed) {
          changedFiles = filesRef.current?.filter(f => f.blobPath !== file.blobPath);

          setFailed({ failed: true, message: data.message || intl.formatMessage({ id: 'yqc.mobile.fail.to.upload', defaultMessage: '上传失败' }) });
          Taro.atMessage({
            type: 'error',
            message: data.message || intl.formatMessage({ id: 'yqc.mobile.cannot.download', defaultMessage: '该文件暂时无法下载' }),
          });
        } else {
          changedFiles = filesRef.current?.map(f => (f.blobPath === file.blobPath ? {
            ...data,
            name: file.name || data.fileKey?.split('@')[1],
            size: file.size || data.fileSize,
          } : f));
        }
        filesRef.current = changedFiles;
        onChange(JSON.stringify(changedFiles));
      }).catch(e => {
        setFailed({ failed: true, message: e.message || intl.formatMessage({ id: 'yqc.mobile.fail.to.upload', defaultMessage: '上传失败' }) });
      });
    }
  }, [file?.blobPath]);

  const handleAbortOrDelete = () => {
    if (file.blobPath && uploadControllerMap[file.blobPath]) {
      uploadControllerMap[file.blobPath].abort();
    }
    const changedFiles = filesRef.current?.filter(f => {
      return !((f.blobPath && f.blobPath === file.blobPath) || (f.fileKey && f.fileKey === file.fileKey));
    });
    filesRef.current = changedFiles;
    onChange(JSON.stringify(changedFiles));
  };

  const handleDownload = async (): Promise<void> => {
    if (fileKey) {
      // 如果是在手机端的企微、飞书、钉钉、微信上
      if ((appStore.isWxwork || appStore.isLark || appStore.isDingtalk || appStore.isWechat) && !_.isPc()) {
        _.navigateTo({
          url: `/packageOther/pages/confirm-download/index?fileKey=${fileKey}`,
        });
      } else {
        setDownloading(true);
        try {
          await download(fileKey, fileKey?.split('@')[1]);
        } catch (error) {
          // eslint-disable-next-line no-console
          console.error('error -- ', error);
        } finally {
          setDownloading(false);
        }
      }
    } else {
      Taro.atMessage({
        type: 'error',
        message: intl.formatMessage({ id: 'yqc.mobile.cannot.download', defaultMessage: '该文件暂时无法下载' }),
      });
    }
  };

  const handlePreview = (e) => {
    // h5的图片用rc-viewer 来预览
    if (disabledPreview) return;
    if (process.env.TARO_ENV === 'h5' && isImage) return;
    e.stopPropagation();

    if (!fileKey) return;
    const modalProps = {
      popupProps: {
        className: styles.previewPop,
      },
      wrapperClassName: 'yq-preview-wrapper',
      children: (
        <>
          <View className={styles.previewHeader}>
            {intl.formatMessage({ id: 'yqc.mobile.show.form', defaultMessage: '预览' })}
          </View>
          <View className={styles.previewContent}>
            <YqPreview blobUrl={uploadMap[fileKey]} fileKey={fileKey} udmTenantId={udmTenantId} />
          </View>
        </>
      ),
    };
    if (process.env.TARO_ENV === 'weapp') {
      if (isVideo) {
        Modal.open(modalProps);
      } else {
        _.previewFile(fileKey);
      }
    } else {
      Modal.open(modalProps);
    }
  };

  const renderIcon = (iconSize: number = 64) => {
    if (file.blobPath && (progressMode === 'circle' || displayType === 'row')) {
      return (
        <View style={{ width: '100%', height: '100%', position: 'relative' }}>
          <Circle strokeWidth={4} trailWidth={4} strokeColor="#2979ff" percent={progress} />
          {displayType === 'cell' && <View className={styles.progressCircleText}>{progress}%</View>}
        </View>
      );
    }
    const other = { color: '#6B7AD2', iconType: 'notes' };
    const { color, iconType } = constants.ICON_LIST.find(icon => icon.type === extension) || other;
    return <YqIcon type={iconType} theme="filled" size={iconSize} fill={color} />;
  };

  const renderSize = () => {
    const size = file.fileSize;
    if (!isNumber(size)) return null;
    let fileSize = size;
    let count = 0;
    if (!fileSize) return '0B';
    while (fileSize / 1024 > 1 && count < 5) {
      fileSize /= 1024;
      count += 1;
    }
    return `${fileSize.toFixed(2)}${constants.SIZE_UNIT[count]}`;
  };

  const renderBrief = () => {
    if (failed.failed) return failed.message;
    if (file.blobPath && progressMode === 'line') {
      return <Line className={styles.attachmentItemLine} percent={progress} strokeWidth={1} trailWidth={10} strokeColor="#2979ff" />;
    }
    return renderSize();
  };

  const renderName = () => {
    if (fromCustomerService) return decodeURIComponent(file)?.split('@')[1];
    return (
      <View className={styles.name}>
        {file.name || file.fileKey?.split('@')[1]}
      </View>
    );
  };

  const renderButtons = () => {
    if (liteMode) return null;
    const deleteIcon = !hiddenDeleteBtn && (
      <YqIcon type="close-one" size={40} fill="#8a94a5" theme="filled" onClick={handleAbortOrDelete} />
    );
    const downloadIcon = downLoading ? <Loading size={20} /> : (
      <YqIcon type="icon-download" fill="#12274d" size={40} onClick={handleDownload} />
    );
    if (fromCustomerService || file.fileKey) {
      return (
        <React.Fragment>
          {process.env.TARO_ENV === 'h5' && downloadIcon}
          {!disabled && deleteIcon}
        </React.Fragment>
      );
    }
    return deleteIcon;
  };

  return (
    <View className={classnames(styles.attachmentItem, { [styles.row]: displayType === 'row' })}>
      {isImage && !disabledPreview && process.env.TARO_ENV === 'h5' && (
        <View style={{ width: '100%', height: '100%', position: 'absolute' }}>
          <View style={{ opacity: 0 }}><img className="yq-attachment-image-preview" src={_.getPreviewUrl(fileKey)} alt="" /></View>
        </View>
      )}
      {displayType === 'cell' ? (
        <YqCell
          className={classnames(styles.attachmentItemCell, { [styles.failed]: failed.failed })}
          icon={renderIcon()}
          title={renderName()}
          onClick={handlePreview}
        >
          {renderBrief()}
        </YqCell>
      ) : (
        <View onClick={handlePreview} className={classnames(styles.attachmentItemRow, { [styles.failed]: failed.failed })}>
          <View className={styles.attachmentItemRowIcon}>{renderIcon(32)}</View>
          <View className={styles.attachmentItemRowText}>
            <View className={styles.attachmentItemRowTextName}>{renderName()}</View>
            {isNumber(file.fileSize) && !liteMode && <View className={styles.attachmentItemRowTextSize}>({renderSize()})</View>}
          </View>
        </View>
      )}
      <View className={styles.attachmentItemButton} onClick={(e) => e.stopPropagation()}>
        {renderButtons()}
      </View>
    </View>
  );
};

export default YQAttachmentItem;
