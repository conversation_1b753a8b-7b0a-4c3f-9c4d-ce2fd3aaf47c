:global {
  .yq-preview-wrapper {
    z-index: 1001;
  }
}
.attachment {
  .upload {
    width: 216px;
    height: 64px !important;
    font-size: 28px;
    color: #2979ff;
    border-radius: 12px;
    flex: unset;
  }
}

.attachmentItem {
  height: 120px;
  position: relative;
  display: flex;
  align-items: center;
  &.row {
    height: 64px;
  }
  .attachmentItemCell {
    min-height: unset;
    padding: 0;
    flex-grow: 1;
    &.failed {
      :global {
        .yq-cell-content-brief,
        .yq-cell-content-top-title {
          color: #f34c4b;
        }
      }
    }
    :global {
      .yq-cell-content {
        margin-left: 8px;
      }
      .yq-cell-content-top-title {
        font-size: 30px;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #12274d;
      }

      .yq-cell-content-brief {
        min-height: 0;
        font-size: 24px;
        color: rgba(18, 39, 77, 0.45);
        font-family: PingFangSC-Regular, PingFang SC;
      }
    }
  }
  .attachmentItemRow {
    width: 0;
    flex-grow: 1;
    display: flex;
    align-items: center;
    .attachmentItemRowIcon {
      width: 32px;
      height: 32px;
      margin-right: 12px;
    }
    .attachmentItemRowText {
      width: 0;
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #12274d;
      flex-grow: 1;
      display: flex;
      .attachmentItemRowTextName {
        width: 0;
        flex-grow: 1;
        word-break: keep-all;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .attachmentItemRowTextSize {
        flex-shrink: 0;
        margin-left: 12px;
        color: rgba(18, 39, 77, 0.45);
      }
    }
  }
  .attachmentItemButton {
    flex-shrink: 0;
    display: flex;
    :global {
      .taroify-loading,
      .yq-mb-icon {
        margin-left: 36px;
      }
      .taroify-loading {
        color: #2979ff;
      }
    }
  }
  .attachmentItemLine {
    width: 100%;
    height: 10px;
    border-radius: 8px;
  }
  .progressCircleText {
    width: 100%;
    height: 100%;
    line-height: 75px;
    text-align: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: rgba(18, 39, 77, 0.45);
    font-size: 12px;
  }
}

.previewPop {
  min-height: 80%;
  height: auto;
  display: flex;
  flex-direction: column;
  z-index: 1020 !important;
  .previewHeader {
    text-align: center;
    line-height: 60px;
    padding: 10px 0;
    border-bottom: 2px solid #eee;
  }
  .previewContent {
    flex-grow: 1;
    display: flex;
    align-items: center;
    background-color: #fff !important;
  }
}
.attachmentItemRowIcon {
  :global {
    .yq-mb-icon-park {
      display: flex;
    }
  }
}
.name {
  word-break: keep-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  font-size: 30px;
  line-height: 42px;
  color: #12274d;
}
