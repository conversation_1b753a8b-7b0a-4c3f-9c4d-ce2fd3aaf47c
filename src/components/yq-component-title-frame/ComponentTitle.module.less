
.frame {
  display: flex;
  justify-content: space-between;
  .header {
    //padding:8px;
    font-size:0.8rem;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #12274D;
    line-height: 1.6rem;
  }
  .button {
    display: flex;
    align-items: center;
  }
}

.buttonFrame {
  display: flex;
  width: 100%;
  //background-color: #00bb00;
  padding: 12px 24px;
  .button {
    height: 2.2rem;
    margin-right: 20px;
    font-size: 0.76rem;

  }
  .buttonSecondary {
    background-color: white;
    border: 2px solid #2979ff;
    :global {
      .yq-button-name {
        color: #2979FF;
      }
      .yq-button {
        min-width: 30px!important;
        width: 50px!important;
      }
    }
  }
  .buttonPrimary {
    background-color: #2979FF;
  }
}
