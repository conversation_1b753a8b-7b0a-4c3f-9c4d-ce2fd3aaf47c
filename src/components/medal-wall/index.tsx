import React, { useContext, useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { ICON_SERVER } from '@config';
import { View, Text, ScrollView } from '@tarojs/components';
import classNames from 'classnames';
import { useIntl } from '@/util/intl';
import { fetch, _ } from '@/util';

import appStore from '@/store/app';
import Style from './Index.module.less';

function MainView() {
  const intl = useIntl();
  const [total, setTotal] = useState(0);
  const [medalList, setMedalList] = useState([]);
  useEffect(() => {
    queryTotalPoints();
    queryMedalList();
  }, []);

  // 总积分
  const queryTotalPoints = async () => {
    const res = await fetch(`/itsm/v1/${appStore.tenantId}/points/self`, null, 'get');
    if (res && !res.failed) {
      setTotal(res.totalPoints || 0);
    }
  };

  // 勋章列表
  const queryMedalList = async () => {
    const res = await fetch(`/itsm/v1/${appStore.tenantId}/badge/user/self`, null, 'get');
    if (res && !res.failed) {
      setMedalList(res);
    }
  };

  const rendererMedal = () => {
    if (!medalList?.length) {
      return <View className={Style.empty}>{intl.formatMessage({ id: 'yqc.mobile.no.data', defaultMessage: '暂无数据' })}</View>;
    } else {
      const medal = medalList?.map(i => {
        const medalImg = `${ICON_SERVER}/static/itsm/medal/${i.badgeIcon}`;
        return <View className={Style.gridItem}>
          <img src={medalImg} alt="" />
          <Text className={Style.badgeName}>{i.badgeName}</Text>
        </View>;
      });
      return (<View>
        <ScrollView scrollX className={Style.scoll}>
          <View className={classNames([Style.grid], { [Style.flex]: medalList?.length <= 6 })}>
            {medal}
          </View>
        </ScrollView>
        <View className={Style.total}>{intl.formatMessage({ id: 'yqc.mobile.self.medal.total', defaultMessage: '您已解锁{num}个勋章' }, { num: `${medalList.length}` })}</View>
      </View>);
    }
  };

  return (
    <View className={Style.area}>
      <View className={Style.border}>
        <View className={Style.head}>
          <View className={Style.headLeft}>
            <View className={Style.avatarBackGround} style={{ backgroundImage: `url(${ICON_SERVER}/static/itsm/medal/ydd-medal.svg)` }} />
            <View className={Style.slogan}>
              <View className={Style.title}>{intl.formatMessage({ id: 'yqc.mobile.self.medal.title.welcome', defaultMessage: '个人成就中心' })}</View>
            </View>
          </View>
          <View className={Style.headRight}>
            <Text className={Style.text}>{intl.formatMessage({ id: 'yqc.mobile.self.medal.integral.total', defaultMessage: '当前总积分' })}</Text>
            <Text className={Style.score}>{total}</Text>
          </View>
        </View>
        <View className={Style.content}>
          <View className={Style.contentTile}>
            <View className={Style.separateLeft}>
              <View className={Style.parallelogram} />
              <View className={Style.parallelogram} />
            </View>
            <View className={Style.text}>{intl.formatMessage({ id: 'yqc.mobile.self.medal.achievement', defaultMessage: '勋章成就' })}</View>
            <View className={Style.separateRight}>
              <View className={Style.parallelogram} />
              <View className={Style.parallelogram} />
            </View>
          </View>
          {rendererMedal()}
        </View>
      </View>
    </View>
  );
}

export default observer(MainView);
