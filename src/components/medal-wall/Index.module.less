
.area {
  background: linear-gradient(180deg, #5C606B 0%, #363B46 100%);
  color: #fff;
  width: 70%;
  min-width: 720px;
  max-width: 900px;
  margin: 24px auto;
  box-shadow: 0 4px 12px 0 rgba(18, 39, 77, 0.2);
  border-radius: 4px;
  padding: 12px;
  font-family: DingTalk, DingTalk;

  .border {
    border: 1px solid #5C6373;
    padding: 18px 10px 10px 10px;

    .content {
      background: linear-gradient(180deg, #606571 0%, #363B46 100%);
      padding: 32px;

      .contentTile {
        display: flex;
        justify-content: center;

        .text {
          font-size: 32px;
          font-weight: 500;
          margin: 0 16px;
        }

        .separateLeft {
          transform: skewX(20deg);
        }

        .separateRight {
          transform: skewX(-20deg);
        }

        .separateRight, .separateLeft {
          display: flex;
          align-items: center;

          .parallelogram {
            background: linear-gradient(180deg, rgba(238, 238, 238, 0.65) 0%, rgba(216, 216, 216, 0) 100%);
            width: 8px;
            height: 24px;
            margin-left: 8px;
          }
        }
      }

      .empty {
        margin-top: 24px;
        opacity: 0.45;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .scoll {
        overflow-x: auto;
      }

      .grid {
        margin-top: 24px;
        display: inline-grid;
        grid-template-rows: 1fr 1fr;
        grid-auto-flow: column;
        overflow-x: auto;

        .gridItem {
          width: 200px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          .badgeName {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-size: 24px;
            width: 90%;
            text-align: center;
            font-family: AlibabaPuHuiTi_2_65_Medium;
          }

          :global {
            .c7n-badge-count {
              background-color: #27FFFF;
            }
          }
        }
      }

      .flex {
        display: flex;
        flex-wrap: wrap;
        overflow-x: unset;
      }

      .total {
        font-size: 24px;
        opacity: 0.45;
        margin-top: 24px;
        text-align: center;
        letter-spacing: 2px;
      }
    }

    .head {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 32px;

      .headLeft {
        display: flex;

        .slogan {
          margin: 0 12px;

          .title {
            font-size: 32px;
          }

          .text {
            font-size: 22px;
            opacity: 0.85;
            margin-top: 4px;
          }
        }

        .avatarBackGround {
          width: 54px;
          background-size: contain;
          background-repeat: no-repeat;
          display: flex;
          justify-content: center;
          padding-top: 4px;
        }
      }

      .headRight {
        max-width: 40%;
        overflow: hidden;

        .text {
          font-size: 22px;
          opacity: 0.65;
        }

        .score {
          font-size: 40px;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
  }
}
