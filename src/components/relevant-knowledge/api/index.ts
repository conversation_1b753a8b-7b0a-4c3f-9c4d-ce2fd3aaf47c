import useSWR from 'swr';
import queryString from 'query-string';
import appStore from '@/store/app';
import lookupStore from '@/store/lookup';
import { fetch } from '@/util';

const options = { revalidateOnFocus: false, revalidateIfStale: true };

/**
 * 获取关联知识--生成的知识&&关联的知识
 * 这接口居然是分页的。。直接查全部吧
 */
function useFetchKnowledge({ ticketId, businessObjectCode, searchValue, relationType }) {
  const tenantId = appStore.tenantId;

  const query = {
    businessObjectCode,
    relationType,
    page: 0,
    size: 999,
  };

  if (searchValue?.trim()) {
    Object.assign(query, { param: searchValue });
  }

  const url = `itsm/v1/${tenantId}/ticket/element/${ticketId}?${queryString.stringify(query)}`;

  const { data, isLoading, mutate } = useSWR(ticketId && businessObjectCode ? url : '', fetch, options);

  const result = Array.isArray(data?.content) ? data.content as Array<Record<string, any>> : [];

  if (result.length > 0) {
    result.forEach(r => {
      if (r && typeof r === 'object') {
        Object.assign(r, { businessObjectCode });
      }
    });
    lookupStore.fetchLookupByCode('KNOWLEDGE_PUBLISH_STATE');
  }

  return { data: result, isLoading, mutate };
}

/**
 * 取消关联的知识
 */
async function unlinkedKnowledge({ knowledgeId, businessObjectCode }) {
  const tenantId = appStore.tenantId;

  const url = `itsm/v1/${tenantId}/ticket/element/disassociate/${knowledgeId}?businessObjectCode=${businessObjectCode}`;

  const res = await fetch(url, {}, 'post').catch(err => err?.data);

  return res;
}

/**
 * 关联知识
 */
async function linkKnowledge({ ticketId, businessObjectCode, data }) {
  const tenantId = appStore.tenantId;

  const url = `itsm/v1/${tenantId}/ticket/element/related/${ticketId}?businessObjectCode=${businessObjectCode}`;

  const res = await fetch(url, data, 'post').catch(err => err?.data);

  return res;
}

export { useFetchKnowledge, unlinkedKnowledge, linkKnowledge };
