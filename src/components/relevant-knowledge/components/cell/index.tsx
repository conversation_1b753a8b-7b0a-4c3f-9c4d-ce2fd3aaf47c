import React, { useCallback, useMemo } from 'react';
import { useIntl } from '@/util/intl';
import { observer } from 'mobx-react-lite';
import { View, Text } from '@tarojs/components';
import { Dialog, SwipeCell } from '@taroify/core';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import Taro from '@tarojs/taro';
import YqCell from '@/components/yq-cell';
import YqIcon from '@/components/yq-icon';
import YqStatus from '@/components/yq-status';
import { _ } from '@/util';
import lookupStore from '@/store/lookup';
import { unlinkedKnowledge } from '../../api';
import { CardProps } from '../../RelevantKnowledge';
import styles from './RelevantKnowledgeCell.module.less';

dayjs.extend(relativeTime);

const Card: React.FC<CardProps> = function Card(props) {
  const { record, openId, setOpenId, mutate, className = '', disabled = false } = props;

  const intl = useIntl();

  const stateList = lookupStore.lookups.get('KNOWLEDGE_PUBLISH_STATE');

  const iconType = useMemo(() => {
    const title = record.name || record.titleNormal;
    const type = record.type === 'knowledge' ? record.fileType : record.type; // 特殊处理一下
    return _.getIconTypeByFilename(title, type);
  }, [record]);

  const statusColor = useMemo(() => {
    const currentState = stateList?.find(state => state.code === record.publishStatus);
    return currentState?.color;
  }, [stateList, record]);

  const renderIcon = useMemo(() => {
    const type = record.mobileRelationType;
    const icon = <YqIcon type={iconType} size={45} />;
    if (!type || !['RELATED', 'QUOTE'].includes(type)) return icon;
    return (
      <View style={{ position: 'relative' }}>
        {icon}
        <YqIcon
          className={styles.iconSign}
          type={type === 'QUOTE' ? 'AtSign' : 'icon-introduce'}
          size={45}
          fill="#12274d"
          theme={type === 'QUOTE' ? 'outline' : 'filled'}
        />
      </View>
    );
  }, [iconType, record]);

  const handleUnlinked = async () => {
    Dialog.confirm({
      cancel: intl.formatMessage({ id: 'yqc.mobile.cancel', defaultMessage: '取消' }),
      confirm: intl.formatMessage({ id: 'yqc.mobile.confirm', defaultMessage: '确认' }),
      title: (
        <>
          <YqIcon type="attention" fill="#fd7d23" size={92} theme="filled" />
          <View className={styles.confirmText}>
            {intl.formatMessage({ id: 'yqc.mobile.do.you.want.to.unlink.the.current.knowledge', defaultMessage: '是否取消关联当前知识' })}
          </View>
        </>
      ),
      onConfirm: async () => {
        const { ticketElementId: knowledgeId, businessObjectCode } = record;
        const res = await unlinkedKnowledge({ knowledgeId, businessObjectCode });
        if (res === true) {
          Taro.atMessage({ type: 'success', message: intl.formatMessage({ id: 'yqc.mobile.unlinked.successful', defaultMessage: '取消关联成功' }) });
          mutate?.();
        }
      },
    });
  };

  const handleNavigateDetail = () => {
    if (disabled) return;
    const sourceId = Taro.getCurrentInstance().router.params?.ticketId || '';
    _.navigateTo({ url: `/packageOther/pages/knowledge-detail/index?id=${record.elementId}&spaceId=${record.spaceId}&sourceModule=TICKETS&sourceFunction=RELATED_KNOWLEDGE&sourceId=${sourceId}` });
  };

  const renderOutput = useCallback((id: string, text: string) => {
    return text ? (
      <Text className={styles.briefItem}>
        {`${intl.formatMessage({ id })}: ${text}`}
      </Text>
    ) : null;
  }, [intl]);

  return (
    <SwipeCell
      className={styles.cell}
      open={record.id === openId ? 'right' : 'outside'}
      onOpen={() => setOpenId?.(record.id)}
      disabled={disabled}
    >
      <YqCell
        border
        title={record.name || record.titleNormal}
        className={className}
        icon={renderIcon}
        value={record.publishStatusName && <YqStatus color={statusColor}>{record.publishStatusName}</YqStatus>}
        valueClassName={styles.state}
        onClick={handleNavigateDetail}
        briefClassName={record.type === 'FOLDER' && styles.folder}
      >
        <View className={styles.brief}>
          {renderOutput('Created by', record.createdByName || record.owner || record.ownerName)}
          {renderOutput('Update time', dayjs(record.lastUpdateDate).fromNow())}
        </View>
        {record.parentStructName && !disabled && <View className={styles.path}>{record.parentStructName}</View>}
        {record.type === 'FOLDER' && <YqIcon className={styles.folderIcon} type="right" size={50} fill="#12274d" opacity={0.45} />}
      </YqCell>
      {record.cancelFlag && (
        <SwipeCell.Actions side="right">
          <View onClick={handleUnlinked} className={styles.cancel}>
            {intl.formatMessage({ id: 'yqc.mobile.unlinked', defaultMessage: '取消关联' })}
          </View>
        </SwipeCell.Actions>
      )}
    </SwipeCell>
  );
};

export default observer(Card);
