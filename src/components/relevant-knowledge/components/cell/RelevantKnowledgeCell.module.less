:global {
  :local(.correctedPosition) {
    &.taroify-popup.taroify-dialog {
      left: 50% !important;
    }
  }
}

.cell {
  position: relative;

  .folder {
    padding-right: 40px;
  }

  .folderIcon {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
  }

  .state {
    opacity: 1 !important;
  }

  .brief {
    font-size: 24px;
    color: rgba(18, 39, 77, 0.55);
    line-height: 40px;
    display: flex;
    flex-wrap: wrap;

    .briefItem {
      margin-right: 32px;
      word-break: break-all;
    }
  }

  .path {
    font-size: 24px;
    color: rgba(18, 39, 77, 0.55);
    line-height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break: keep-all;
  }

  .cancel {
    width: 160px;
    height: 100%;
    background-color: #f83552;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #fff;
  }
}

.confirmText {
  margin: 20px 0 40px 0;
}

.iconSign {
  position: absolute;
  left: 2px;
  bottom: 2px;
  transform: scale(0.4);
  transform-origin: left bottom;
}
