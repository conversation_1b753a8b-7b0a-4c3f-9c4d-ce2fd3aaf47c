.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.buttonGroup {
  width: 0;
  flex-grow: 1;
  display: flex;
  align-items: center;
  overflow: hidden;
  transition-duration: 300ms;
  transition-timing-function: linear;

  &.hidden {
    width: 0;
  }
}

.button {
  width: 244px;
  height: 64px !important;
  margin-right: 24px;
  background-color: #fff !important;
  border-radius: 12px;
  font-size: 28px;
  border: 2px solid #2979ff !important;
  flex: none !important;
  flex-shrink: 0;
}

.searchIcon {
  text-align: right;
  line-height: 64px;
  flex-shrink: 0;
}

.search {
  width: 0;
  height: 68px;
  overflow: hidden;
  position: absolute;
  right: 0;
  top: 0;
  --search-padding: 0;
  --search-action-font-size: 28px;
  --search-action-padding: 0 0 0 12px;
  transition-duration: 300ms;
  transition-timing-function: linear;

  :global {
    .taroify-search__action {
      word-break: keep-all;
      white-space: nowrap;
    }
  }

  &.show {
    width: calc(100%);
  }
}

.popupCard {
  padding-left: 0;
  padding-right: 0;
  padding-bottom: 0;
}