import React, { useState } from 'react';
import { useLocalObservable, observer } from 'mobx-react-lite';
import classNames from 'classnames';
import { useIntl } from '@/util/intl';
import { View, Text, BaseEventOrig, InputProps } from '@tarojs/components';
import { Search } from '@taroify/core';
import { runInAction } from 'mobx';
import omit from 'lodash/omit';
import Taro from '@tarojs/taro';
import YqButton from '@/components/yq-button';
import YqIcon from '@/components/yq-icon';
import Modal from '@/components/yq-modal';
import MultipleSelect from '@/components/multiple-select';
import { Breadcrumb } from '@/components/multiple-select/MultipleSelect';
import TransKnowledge from '@/components/ui-action/components/trans-knowledge/transKnowledge';
import appStore from '@/store/app';
import kbStore from '@/store/kbstore';
import { linkKnowledge } from '../../api';
import { HeaderProps } from '../../RelevantKnowledge';
import styles from './RelevantKnowledgeHeader.module.less';
import Card from '../cell';

const Header:React.FC<HeaderProps> = function Header(props) {
  const {
    searchValue,
    setSearchValue,
    ticketId,
    businessObjectCode,
    businessObjectId,
    shortDescription,
    mutate = () => {},
  } = props;

  const intl = useIntl();

  const [show, setShow] = useState(false);
  const [focus, setFocus] = useState(false);
  const [records] = useState<Record<string, any>[]>([]);

  const store = useLocalObservable(() => ({
    url: `knowledge/v1/${appStore.tenantId}/know/space?search_enabledFlag=true`,
    method: 'get',
    params: {} as Record<string, any>,
    showCheckbox: false,
    breadcrumbs: [] as Breadcrumb[],
    valueField: 'id',
    cacheStore: {} as Record<string, any>,
    init() {
      store.url = `knowledge/v1/${appStore.tenantId}/know/space`;
      store.params = { search_enabledFlag: true };
      store.method = 'get';
      store.showCheckbox = false;
      store.breadcrumbs = [{ name: intl.formatMessage({ id: 'yqc.mobile.all', defaultMessage: '全部' }), value: { type: 'ALL' } }];
    },
  }));

  const handleShowSearch = () => {
    setShow(true);
    setFocus(true);
  };

  const handleClear = () => {
    setSearchValue('');
    setFocus(true);
  };

  const handleCancel = () => {
    setShow(false);
    setSearchValue('');
  };

  const handleSearch = (e: BaseEventOrig<InputProps.inputValueEventDetail>) => {
    setSearchValue(e.detail.value); // @ts-ignore
    e.target?.children?.[0]?.blur?.();
  };

  const handlePopupSearch = (v: string) => {
    if (!v) {
      runInAction(() => {
        if (store.cacheStore.url) {
          Object.assign(store, { ...store.cacheStore });
        } else {
          store.init();
        }
        store.cacheStore = {};
      });
    } else {
      runInAction(() => {
        store.cacheStore = omit({ ...store }, ['init', 'cacheStore']);
        store.url = `search/v1/${appStore.tenantId}/common/search?draftFlag=false&canCreateLink=true`;
        store.method = 'post';
        store.params = {
          filters: [
            { key: 'title', value: v },
            { key: 'fileType.keyword', value: 'FOLDER', containFlag: false },
          ],
          type: ['knowledge'],
        };
        store.showCheckbox = true;
      });
    }
  };

  const handlePopupBreadcrumbClick = (value: Record<string, any>) => {
    if (value.type === 'ALL') {
      store.init();
    } else {
      runInAction(() => {
        store.url = `knowledge/v1/${appStore.tenantId}/know/element?search_publishStatus=PUBLISHED&canCreateLink=true`;
        store.params = { folderId: value.folderId, spaceId: value.spaceId };
        store.method = 'post';
        store.showCheckbox = true;
        const preIndex = store.breadcrumbs.findIndex(b => b.value.key === value.key);
        store.breadcrumbs = store.breadcrumbs.slice(0, preIndex + 1);
      });
    }
  };

  const handlePopupRowClick = (record: Record<string, any>) => {
    if (record?.type === 'SPACE' || record?.type === 'FOLDER') {
      const spaceId = record.type === 'SPACE' ? record.id : record.spaceId;
      const folderId = record.type === 'SPACE' ? '0' : record.id;
      runInAction(() => {
        store.url = `knowledge/v1/${appStore.tenantId}/know/element?search_publishStatus=PUBLISHED&canCreateLink=true`;
        store.params = { folderId, spaceId };
        store.method = 'post';
        store.showCheckbox = true;
        store.breadcrumbs.push({
          name: record.name,
          value: { type: 'SPACE', spaceId, folderId, key: record.id },
        });
      });
      return false;
    } else {
      return true;
    }
  };

  const handleConfirm = async (data: Record<string, any>[], modal) => {
    if (!data?.length) {
      Taro.atMessage({
        type: 'warning',
        message: intl.formatMessage({ id: 'yqc.mobile.select.one.record', defaultMessage: '至少选中一条记录' }),
      });
      return;
    }
    const res = await linkKnowledge({ ticketId, businessObjectCode, data: data.map(d => d.id) });
    if (res === true) {
      Taro.atMessage({ type: 'success', message: intl.formatMessage({ id: 'yqc.mobile.link.successful', defaultMessage: '关联成功' }) });
      await mutate();
      modal?.close();
    }
  };

  const handleOpenLinkKnowledge = () => {
    store.init();
    Modal.open({
      popupProps: { style: { height: '80vh' } },
      children: (
        <MultipleSelect
          breadcrumb
          displayField={['name', 'titleNormal']}
          searchPrefix=""
          records={records}
          store={store}
          title={intl.formatMessage({ id: 'yqc.mobile.related.knowledge', defaultMessage: '关联知识' })}
          renderCell={(record) => (
            <Card className={styles.popupCard} record={record} disabled />
          )}
          onRowClick={handlePopupRowClick}
          onBreadcrumbClick={handlePopupBreadcrumbClick}
          onSearch={handlePopupSearch}
          checkBoxDisabled={(record) => record.type === 'FOLDER'}
          onConfirm={handleConfirm}
        />
      ),
    });
  };

  const handleGenerate = async () => {
    if (!kbStore.init) {
      await kbStore.fetch(appStore.tenantId, businessObjectId, ticketId);
    }
    const record = kbStore.getRecords(shortDescription);
    // 直接发布
    if (kbStore.knowledgeSettingFlag && !kbStore.modifyDefaultFlag) {
      const flag = kbStore.defaultSourceCoveredFlag; // 校验同源
      ticketId && await kbStore.directGenerate(flag, ticketId, record);
    } else {
      Modal.open({
        children: (
          <TransKnowledge
            title={intl.formatMessage({ id: 'yqc.mobile.conversation.knowledge', defaultMessage: '生成知识' })}
            record={record}
            tenantId={appStore.tenantId}
            businessObjId={businessObjectId || ''}
            intl={intl}
            ticketId={ticketId || ''}
            afterGenerate={mutate}
          />
        ),
        popupProps: {
          style: { minHeight: '80%' },
        },
      });
    }
  };

  return (
    <View className={styles.header}>
      <View className={classNames(styles.buttonGroup, { [styles.hidden]: show })}>
        <YqButton
          onClick={handleGenerate}
          className={styles.button}
          fontColor="#2979ff"
          iconProps={{ type: 'Book', fill: '#2979ff', size: 36 }}
        >
          {intl.formatMessage({ id: 'yqc.mobile.conversation.knowledge', defaultMessage: '生成知识' })}
        </YqButton>
        <YqButton
          onClick={handleOpenLinkKnowledge}
          className={styles.button}
          fontColor="#2979ff"
          iconProps={{ type: 'bookshelf', fill: '#2979ff', size: 36 }}
        >
          {intl.formatMessage({ id: 'yqc.mobile.related.knowledge', defaultMessage: '关联知识' })}
        </YqButton>
      </View>
      <View className={styles.searchIcon} onClick={handleShowSearch}>
        <YqIcon type="icon-search" fill="#12274d" opacity={0.85} size={40} />
      </View>
      <Search
        focus={focus}
        value={searchValue}
        className={classNames(styles.search, { [styles.show]: show })}
        icon={<YqIcon type="icon-search" size={36} fill="#12274d" opacity={0.45} />}
        action={<Text onClick={handleCancel}>{intl.formatMessage({ id: 'yqc.mobile.cancel', defaultMessage: '取消' })}</Text>}
        onSearch={handleSearch}
        onChange={(e) => !e.detail.value && setSearchValue('')}
        onFocus={() => setFocus(true)}
        onBlur={() => setFocus(false)}
        onClear={handleClear}
      />
    </View>
  );
};

export default observer(Header);
