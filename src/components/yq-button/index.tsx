/* eslint-disable no-unused-vars */
import React, { useState, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { ITouchEvent, View } from '@tarojs/components';
import { Loading } from '@taroify/core';
import classnames from 'classnames';
import YqIcon from '@/components/yq-icon';
import { _ } from '@/util';
import './index.scss';

interface YqIconProps {
  fill: string
  type: string
  size: number
  theme?: 'filled' | 'outline'
}

interface YqButtonProps {
  className?: string
  style?: React.CSSProperties
  iconProps?: YqIconProps
  icon?: React.ReactElement | undefined
  onClick?: (e: ITouchEvent) => void
  loading?: boolean
  fontColor?: string
  loadingSize?: number
  loadingColor?: string
  name?: string // 按钮名称
  children?: string // 按钮名称
  structure?: 'tb' | 'lr' // 按钮结构，上下结构还是左右结构
  mode?: 'cancel' | 'confirm' // 添加两种模式，大部分都是这两种模式
  disabled?: boolean // 禁用
}

const YqButton: React.FC<YqButtonProps> = function YqButton({
  className = '',
  style = {},
  iconProps = { type: '', fill: '', size: 0, theme: 'outline' },
  icon, // 优先使用icon
  loading: propsLoading = false,
  onClick = () => {},
  loadingSize = 18,
  name,
  fontColor = '',
  structure = 'lr',
  loadingColor = '#2979ff',
  children,
  mode,
  disabled = false,
}) {
  const [buttonLoading, setButtonLoading] = useState(false);
  const loading = propsLoading || buttonLoading;

  const handleClick = async (e: ITouchEvent): Promise<any> => {
    if (loading || disabled) return;
    try {
      setButtonLoading(true);
      await onClick(e);
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error(err);
    } finally {
      setButtonLoading(false);
    }
  };

  const renderIcon = (): React.ReactElement | null => {
    let _loadingColor = loadingColor;
    if (mode === 'cancel') _loadingColor = '#2979ff';
    if (mode === 'confirm') _loadingColor = '#fff';
    if (loading) {
      return <Loading size={loadingSize} style={{ color: _loadingColor }} />;
    }
    if ((!iconProps || !iconProps?.type) && !icon) {
      return null;
    }
    if (icon) {
      return icon;
    }
    const { type, fill, size, theme } = iconProps;
    return <YqIcon type={type} fill={fill || '#fff'} size={size || 24} theme={theme || 'outline'} />;
  };

  const getStyle = useMemo((): object => {
    const baseStyle = { height: _.pxToRem(76) };
    if (mode === 'cancel') {
      Object.assign(baseStyle, { backgroundColor: '#fff', border: '1px solid #2979ff' });
    }
    return mode ? { ...Object.assign((style || {}), baseStyle) } : { ...style };
  }, [mode, style]);

  const getFontColor = useMemo((): string => {
    if (mode === 'cancel') {
      return '#2979ff';
    } else if (mode === 'confirm') {
      return '#fff';
    } else {
      return fontColor;
    }
  }, [mode, fontColor]);

  const hasIcon = (): boolean => {
    if (loading) return true;
    return ((!!iconProps && !!iconProps.type) || !!icon);
  };

  const buttonText: string | undefined = name || children;
  const wrapperClassName = classnames(['yq-button', className], { disabled, loading });

  return (
    <View className={wrapperClassName} style={getStyle} onClick={handleClick}>
      <View className={classnames('yq-button-content', { 'top-bottom': structure === 'tb' })}>
        {renderIcon()}
        {buttonText && !loading && (
          <View className={classnames('yq-button-name', { 'no-icon': !hasIcon(), 'top-bottom': structure === 'tb' })} style={{ color: getFontColor }}>
            {buttonText}
          </View>
        )}
      </View>
    </View>
  );
};

export default observer(YqButton);
