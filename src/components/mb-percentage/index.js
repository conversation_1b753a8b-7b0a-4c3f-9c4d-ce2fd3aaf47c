import { View, Text, Image } from '@tarojs/components';
import { Button, Slider } from '@taroify/core';
import { observer } from 'mobx-react-lite';
import className from 'classnames';
import { injectIntl } from '@/util/intl';
import { addAction } from '@/util/guance';

import { fetch, _ } from '@/util';
import { MbRow, Drawer } from '../index';
import './index.scss';

function MbLike(props) {
  const {
    onChange = _.nullFunc,
    value,
    title,
    editable,
    intl,
    name,
    field,
  } = props;
  function getStyle() {
    return className({
      'mb-percentage': true,
      'item-output': !editable,
      error: false,
    });
  }
  function renderValue() {
    return (
      <View>
        <Slider
          size={4}
          value={value || 50}
          onChange={(v) => {
            // eslint-disable-next-line no-chinese/no-chinese
            addAction('使用调查评分组件');
            onChange(v);
          }}
        >

          <Slider.Thumb>
            <View className="custom-thumb">{value}</View>
          </Slider.Thumb>
        </Slider>
      </View>
    );
  }

  return (
    <View className={getStyle()}>
      <View className="mb-percentage">
        <MbRow
          {...props}
          intl={intl}
          label={title}
          value={renderValue()}
        />
      </View>
    </View>
  );
}

export default injectIntl(observer(MbLike));
